#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات عبر XAMPP
"""

import mysql.connector
from mysql.connector import Error
import sys
import os

def test_xampp_connection():
    """اختبار الاتصال بـ XAMPP"""
    print("🔌 اختبار الاتصال بـ XAMPP...")
    print("=" * 50)
    
    # إعدادات الاتصال لـ XAMPP
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',  # كلمة مرور قاعدة البيانات
        'charset': 'utf8mb4'
    }
    
    try:
        # محاولة الاتصال بـ MySQL
        print("📡 محاولة الاتصال بخادم MySQL...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("   ✅ تم الاتصال بخادم MySQL بنجاح!")
            
            cursor = connection.cursor()
            
            # عرض معلومات الخادم
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   📊 إصدار MySQL: {version[0]}")
            
            # فحص وجود قاعدة البيانات
            print("\n🗄️ فحص قاعدة البيانات...")
            cursor.execute("SHOW DATABASES LIKE 'money_manager'")
            db_exists = cursor.fetchone()
            
            if db_exists:
                print("   ✅ قاعدة البيانات 'money_manager' موجودة")
                
                # الاتصال بقاعدة البيانات
                cursor.execute("USE money_manager")
                
                # فحص الجداول
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"   📋 عدد الجداول: {len(tables)}")
                    print("   📝 الجداول الموجودة:")
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        print(f"      • {table[0]}: {count} سجل")
                else:
                    print("   ⚠️ لا توجد جداول في قاعدة البيانات")
                    
            else:
                print("   ⚠️ قاعدة البيانات 'money_manager' غير موجودة")
                print("   🔧 سيتم إنشاؤها تلقائياً عند تشغيل التطبيق")
            
            cursor.close()
            connection.close()
            print("\n✅ تم إغلاق الاتصال بنجاح")
            return True
            
    except Error as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
        
        if "Can't connect to MySQL server" in str(e):
            print("\n💡 نصائح لحل المشكلة:")
            print("   1. تأكد من تشغيل XAMPP")
            print("   2. تأكد من تشغيل خدمة MySQL في XAMPP")
            print("   3. تحقق من أن المنفذ 3306 غير محجوب")
            
        elif "Access denied" in str(e):
            print("\n💡 نصائح لحل المشكلة:")
            print("   1. تحقق من اسم المستخدم وكلمة المرور")
            print("   2. في XAMPP الافتراضي: المستخدم 'root' بدون كلمة مرور")
            
        return False
        
    except Exception as e:
        print(f"   ❌ خطأ غير متوقع: {e}")
        return False

def create_database_if_not_exists():
    """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
    print("\n🔧 إنشاء قاعدة البيانات...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # إنشاء قاعدة البيانات
        cursor.execute("CREATE DATABASE IF NOT EXISTS money_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("   ✅ تم إنشاء قاعدة البيانات 'money_manager'")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"   ❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def test_app_connection():
    """اختبار اتصال التطبيق بقاعدة البيانات"""
    print("\n🧪 اختبار اتصال التطبيق...")
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from database.connection import db
        
        # محاولة الاتصال
        if db.connect():
            print("   ✅ تم الاتصال بنجاح من خلال التطبيق")
            
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT 1 as test")
            if result:
                print("   ✅ تم تنفيذ الاستعلام بنجاح")
                return True
            else:
                print("   ❌ فشل في تنفيذ الاستعلام")
                return False
        else:
            print("   ❌ فشل في الاتصال من خلال التطبيق")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار اتصال التطبيق: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار اتصال XAMPP")
    print("=" * 60)
    
    # اختبار الاتصال الأساسي
    if test_xampp_connection():
        print("\n" + "=" * 60)
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        create_database_if_not_exists()
        
        # اختبار اتصال التطبيق
        test_app_connection()
        
        print("\n🎉 جميع الاختبارات مكتملة!")
    else:
        print("\n❌ فشل في الاتصال الأساسي")
        print("💡 تأكد من تشغيل XAMPP وخدمة MySQL")
    
    print("\n🏁 انتهى الاختبار")
