#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database.connection import db
    print("✅ تم استيراد وحدة قاعدة البيانات بنجاح")
    
    print("🔄 محاولة الاتصال بقاعدة البيانات...")
    if db.connect():
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        
        # اختبار استعلام بسيط
        result = db.execute_query("SELECT 1 as test")
        if result:
            print("✅ تم تنفيذ الاستعلام بنجاح!")
            print(f"النتيجة: {result}")
        else:
            print("❌ فشل في تنفيذ الاستعلام")
            
        db.close()
        print("✅ تم إغلاق الاتصال بنجاح")
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        
except ImportError as e:
    print(f"❌ خطأ في استيراد وحدة قاعدة البيانات: {e}")
except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")
    import traceback
    traceback.print_exc()
