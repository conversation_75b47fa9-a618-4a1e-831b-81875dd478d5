#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض العملات في الواجهة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_currencies_in_database():
    """اختبار العملات في قاعدة البيانات"""
    print("🧪 اختبار العملات في قاعدة البيانات")
    print("=" * 50)
    
    try:
        from database.connection import db
        from database.models import Currency
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # الحصول على العملات النشطة
        currencies = Currency.get_all()
        
        print(f"\n📊 عدد العملات النشطة: {len(currencies)}")
        
        expected_currencies = {
            'SAR': 'ريال سعودي',
            'YER': 'ريال يمني', 
            'AED': 'درهم إماراتي',
            'USD': 'دولار أمريكي'
        }
        
        print("\n💱 العملات المتاحة:")
        for currency in currencies:
            code = currency['code']
            name = currency['name']
            symbol = currency['symbol']
            rate = currency['exchange_rate']
            
            if code in expected_currencies:
                status = "✅"
                expected_currencies.pop(code)
            else:
                status = "⚠️ غير متوقع"
            
            print(f"   {status} {name} ({code}) - {symbol} - سعر الصرف: {rate}")
        
        # التحقق من العملات المفقودة
        if expected_currencies:
            print("\n❌ عملات مفقودة:")
            for code, name in expected_currencies.items():
                print(f"   • {name} ({code})")
            return False
        
        # التحقق من عدد العملات
        if len(currencies) != 4:
            print(f"\n⚠️ عدد العملات غير صحيح: متوقع 4، موجود {len(currencies)}")
            return False
        
        print("\n✅ جميع العملات المطلوبة موجودة ونشطة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currencies_in_config():
    """اختبار العملات في ملف الإعدادات"""
    print("\n🧪 اختبار العملات في ملف الإعدادات")
    print("=" * 50)
    
    try:
        from config.settings import SUPPORTED_CURRENCIES
        
        print(f"📊 عدد العملات في الإعدادات: {len(SUPPORTED_CURRENCIES)}")
        
        expected_codes = ['SAR', 'YER', 'AED', 'USD']
        actual_codes = list(SUPPORTED_CURRENCIES.keys())
        
        print("\n💱 العملات في الإعدادات:")
        for code, info in SUPPORTED_CURRENCIES.items():
            name = info['name']
            symbol = info['symbol']
            
            if code in expected_codes:
                status = "✅"
            else:
                status = "⚠️ غير متوقع"
            
            print(f"   {status} {name} ({code}) - {symbol}")
        
        # التحقق من التطابق
        if set(actual_codes) == set(expected_codes):
            print("\n✅ العملات في الإعدادات صحيحة!")
            return True
        else:
            missing = set(expected_codes) - set(actual_codes)
            extra = set(actual_codes) - set(expected_codes)
            
            if missing:
                print(f"\n❌ عملات مفقودة: {missing}")
            if extra:
                print(f"\n⚠️ عملات إضافية: {extra}")
            
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def test_account_creation_currencies():
    """اختبار العملات في نافذة إنشاء الحساب"""
    print("\n🧪 اختبار العملات في واجهة إنشاء الحساب")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # محاكاة الاستعلام المستخدم في نافذة إنشاء الحساب
        currencies = db.execute_query("SELECT * FROM currencies WHERE is_active = TRUE ORDER BY name")
        
        if not currencies:
            print("❌ لا توجد عملات نشطة")
            return False
        
        print(f"📊 عدد العملات المتاحة للحسابات: {len(currencies)}")
        
        expected_names = [
            'درهم إماراتي',
            'دولار أمريكي', 
            'ريال سعودي',
            'ريال يمني'
        ]
        
        print("\n💱 العملات المتاحة لإنشاء الحسابات:")
        actual_names = []
        for currency in currencies:
            name = currency['name']
            code = currency['code']
            symbol = currency['symbol']
            actual_names.append(name)
            
            if name in expected_names:
                status = "✅"
            else:
                status = "⚠️"
            
            print(f"   {status} {name} ({code}) - {symbol}")
        
        # التحقق من التطابق
        if set(actual_names) == set(expected_names) and len(currencies) == 4:
            print("\n✅ العملات متاحة بشكل صحيح لإنشاء الحسابات!")
            return True
        else:
            print(f"\n❌ مشكلة في العملات المتاحة")
            print(f"   متوقع: {len(expected_names)} عملة")
            print(f"   موجود: {len(currencies)} عملة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الحسابات: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل للعملات المحدثة")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار قاعدة البيانات
    if test_currencies_in_database():
        tests_passed += 1
    
    # اختبار ملف الإعدادات
    if test_currencies_in_config():
        tests_passed += 1
    
    # اختبار واجهة الحسابات
    if test_account_creation_currencies():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n💡 العملات المتاحة الآن في قسم الحسابات:")
        print("   ✅ ريال سعودي (ر.س)")
        print("   ✅ ريال يمني (ر.ي)")
        print("   ✅ درهم إماراتي (د.إ)")
        print("   ✅ دولار أمريكي ($)")
        
        print("\n🚀 يمكنك الآن:")
        print("   1. تشغيل التطبيق: python main.py")
        print("   2. إنشاء حساب جديد")
        print("   3. اختيار إحدى العملات الأربع المتاحة")
    else:
        print("⚠️ بعض الاختبارات فشلت - تحقق من الأخطاء أعلاه")
    
    print("\n🏁 انتهى الاختبار")
