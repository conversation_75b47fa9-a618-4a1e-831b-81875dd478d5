#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تسجيل الدخول الجديدة مع التبويبات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_login_window_with_tabs():
    """اختبار نافذة تسجيل الدخول مع التبويبات"""
    print("🧪 اختبار نافذة تسجيل الدخول الجديدة مع التبويبات")
    print("=" * 60)
    
    try:
        # استيراد المكتبات المطلوبة
        print("1️⃣ استيراد المكتبات...")
        import customtkinter as ctk
        from gui.login_window import LoginWindow
        from config.database_config import db_config
        print("✅ تم استيراد المكتبات بنجاح")
        
        # اختبار إعدادات قاعدة البيانات
        print("\n2️⃣ اختبار إعدادات قاعدة البيانات...")
        
        # تحميل الإعدادات
        config = db_config.get_config()
        print(f"✅ تم تحميل الإعدادات:")
        print(f"   - الخادم: {config['host']}:{config['port']}")
        print(f"   - قاعدة البيانات: {config['database']}")
        print(f"   - المستخدم: {config['user']}")
        
        # اختبار الاتصال
        success, message = db_config.test_connection()
        if success:
            print(f"✅ اختبار الاتصال: {message}")
        else:
            print(f"⚠️ اختبار الاتصال: {message}")
        
        # اختبار التحقق من صحة الإعدادات
        print("\n3️⃣ اختبار التحقق من صحة الإعدادات...")
        is_valid, errors = db_config.validate_config(config)
        if is_valid:
            print("✅ الإعدادات صحيحة")
        else:
            print("❌ أخطاء في الإعدادات:")
            for error in errors:
                print(f"   - {error}")
        
        # اختبار حفظ واستعادة الإعدادات
        print("\n4️⃣ اختبار حفظ واستعادة الإعدادات...")
        
        # حفظ إعدادات تجريبية
        test_config = config.copy()
        test_config['host'] = 'test_host'
        
        save_success, save_message = db_config.save_config(test_config)
        if save_success:
            print(f"✅ حفظ الإعدادات: {save_message}")
        else:
            print(f"❌ فشل حفظ الإعدادات: {save_message}")
        
        # استعادة الإعدادات الافتراضية
        reset_success, reset_message = db_config.reset_to_default()
        if reset_success:
            print(f"✅ استعادة الافتراضي: {reset_message}")
        else:
            print(f"❌ فشل استعادة الافتراضي: {reset_message}")
        
        # اختبار نافذة تسجيل الدخول
        print("\n5️⃣ اختبار نافذة تسجيل الدخول...")
        
        response = input("هل تريد فتح نافذة تسجيل الدخول للاختبار؟ (y/N): ").strip().lower()
        if response in ['y', 'yes', 'نعم']:
            print("🚀 فتح نافذة تسجيل الدخول...")
            
            try:
                app = LoginWindow()
                print("✅ تم إنشاء نافذة تسجيل الدخول بنجاح")
                print("\n📋 ميزات النافذة الجديدة:")
                print("   ✅ تبويب تسجيل الدخول")
                print("   ✅ تبويب إعدادات قاعدة البيانات")
                print("   ✅ حقول إعدادات الاتصال")
                print("   ✅ أزرار اختبار وحفظ الإعدادات")
                print("   ✅ واجهة عربية مع دعم RTL")
                
                print("\n🎯 للاختبار:")
                print("   1. جرب التنقل بين التبويبات")
                print("   2. اختبر تعديل إعدادات قاعدة البيانات")
                print("   3. جرب زر 'اختبار الاتصال'")
                print("   4. جرب زر 'حفظ الإعدادات'")
                print("   5. جرب زر 'استعادة الافتراضي'")
                print("   6. جرب تسجيل الدخول من التبويب الأول")
                
                app.run()
                print("✅ تم إغلاق النافذة بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في تشغيل النافذة: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("تم تخطي اختبار النافذة")
        
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار:")
        print("   ✅ استيراد المكتبات")
        print("   ✅ تحميل إعدادات قاعدة البيانات")
        print(f"   {'✅' if success else '⚠️'} اختبار الاتصال")
        print(f"   {'✅' if is_valid else '❌'} التحقق من صحة الإعدادات")
        print(f"   {'✅' if save_success else '❌'} حفظ الإعدادات")
        print(f"   {'✅' if reset_success else '❌'} استعادة الافتراضي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_config_features():
    """اختبار ميزات إعدادات قاعدة البيانات"""
    print("\n🔧 اختبار ميزات إعدادات قاعدة البيانات")
    print("=" * 50)
    
    try:
        from config.database_config import db_config
        
        # اختبار إعدادات مختلفة
        test_configs = [
            {
                'name': 'إعدادات صحيحة',
                'config': {
                    'host': 'localhost',
                    'port': 3306,
                    'database': 'money_manager',
                    'user': 'root',
                    'password': 'mohdam'
                }
            },
            {
                'name': 'منفذ خاطئ',
                'config': {
                    'host': 'localhost',
                    'port': 99999,
                    'database': 'money_manager',
                    'user': 'root',
                    'password': 'mohdam'
                }
            },
            {
                'name': 'حقول فارغة',
                'config': {
                    'host': '',
                    'port': 3306,
                    'database': '',
                    'user': 'root',
                    'password': 'mohdam'
                }
            }
        ]
        
        for test in test_configs:
            print(f"\n🧪 اختبار: {test['name']}")
            is_valid, errors = db_config.validate_config(test['config'])
            
            if is_valid:
                print("   ✅ الإعدادات صحيحة")
            else:
                print("   ❌ أخطاء في الإعدادات:")
                for error in errors:
                    print(f"      - {error}")
        
        # اختبار نص الاتصال
        print(f"\n🔗 نص الاتصال: {db_config.get_connection_string()}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نافذة تسجيل الدخول المحدثة")
    print("=" * 45)
    
    print("📋 هذا الاختبار سيقوم بـ:")
    print("   1. اختبار إعدادات قاعدة البيانات")
    print("   2. اختبار نافذة تسجيل الدخول الجديدة")
    print("   3. اختبار نظام التبويبات")
    print("   4. اختبار ميزات إدارة الإعدادات")
    
    # تشغيل الاختبارات
    success1 = test_database_config_features()
    success2 = test_login_window_with_tabs()
    
    if success1 and success2:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("\n📋 الميزات الجديدة:")
        print("   ✅ نظام تبويبات في نافذة تسجيل الدخول")
        print("   ✅ تبويب إعدادات قاعدة البيانات")
        print("   ✅ حقول إعدادات الاتصال")
        print("   ✅ اختبار الاتصال")
        print("   ✅ حفظ واستعادة الإعدادات")
        print("   ✅ التحقق من صحة الإعدادات")
        print("   ✅ واجهة عربية مع دعم RTL")
        
        print("\n🎯 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. ستظهر نافذة تسجيل الدخول مع التبويبات")
        print("3. استخدم تبويب 'إعدادات قاعدة البيانات' لتكوين الاتصال")
        print("4. استخدم تبويب 'تسجيل الدخول' للدخول للنظام")
        
    else:
        print("\n❌ بعض الاختبارات فشلت!")

if __name__ == "__main__":
    main()
