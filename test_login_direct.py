#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول مباشرة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        
        from database.connection import db
        
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات نجح")
            
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT 1 as test")
            if result:
                print("✅ تنفيذ الاستعلامات يعمل")
                return True
            else:
                print("❌ فشل في تنفيذ الاستعلام")
                return False
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الاتصال: {e}")
        return False

def test_auth_system():
    """اختبار نظام المصادقة"""
    try:
        print("\n🔍 اختبار نظام المصادقة...")
        
        from utils.auth import auth_manager
        
        # محاولة تسجيل الدخول
        print("   محاولة تسجيل الدخول بـ admin / 123456...")
        success, message = auth_manager.login("admin", "123456")
        
        if success:
            print("✅ تسجيل الدخول نجح")
            print(f"   المستخدم الحالي: {auth_manager.current_user}")
            
            # تسجيل الخروج
            auth_manager.logout()
            print("✅ تسجيل الخروج نجح")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المصادقة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_auth():
    """اختبار المصادقة يدوياً"""
    try:
        print("\n🔍 اختبار المصادقة يدوياً...")
        
        import mysql.connector
        import bcrypt
        
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # البحث عن المستخدم
        cursor.execute("""
            SELECT id, username, password_hash, full_name, role, is_active
            FROM users
            WHERE username = %s AND is_active = TRUE
        """, ("admin",))
        
        user = cursor.fetchone()
        
        if user:
            print("✅ تم العثور على المستخدم")
            print(f"   ID: {user['id']}")
            print(f"   اسم المستخدم: {user['username']}")
            print(f"   الاسم الكامل: {user['full_name']}")
            print(f"   الدور: {user['role']}")
            
            # اختبار كلمة المرور
            password = "123456"
            password_bytes = password.encode('utf-8')
            stored_hash = user['password_hash'].encode('utf-8')
            
            if bcrypt.checkpw(password_bytes, stored_hash):
                print("✅ كلمة المرور صحيحة")
                return True
            else:
                print("❌ كلمة المرور غير صحيحة")
                return False
        else:
            print("❌ لم يتم العثور على المستخدم")
            return False
            
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار اليدوي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لتسجيل الدخول")
    print("=" * 50)
    
    # 1. اختبار الاتصال بقاعدة البيانات
    db_success = test_database_connection()
    
    # 2. اختبار المصادقة يدوياً
    manual_success = test_manual_auth()
    
    # 3. اختبار نظام المصادقة
    auth_success = test_auth_system()
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print(f"   اتصال قاعدة البيانات: {'✅' if db_success else '❌'}")
    print(f"   اختبار يدوي: {'✅' if manual_success else '❌'}")
    print(f"   نظام المصادقة: {'✅' if auth_success else '❌'}")
    
    if db_success and manual_success and auth_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ يمكنك الآن تسجيل الدخول بـ admin / 123456")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل")
        
        if not db_success:
            print("   - مشكلة في الاتصال بقاعدة البيانات")
        if not manual_success:
            print("   - مشكلة في بيانات المستخدم أو كلمة المرور")
        if not auth_success:
            print("   - مشكلة في نظام المصادقة")

if __name__ == "__main__":
    main()
