#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صلاحيات المدير الجديدة لحذف جميع أنواع المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

def test_admin_delete_permissions():
    """اختبار صلاحيات حذف المستخدمين"""
    print("🧪 اختبار صلاحيات المدير لحذف المستخدمين")
    print("=" * 55)
    
    try:
        # الاتصال بقاعدة البيانات
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # تسجيل الدخول كمدير
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print(f"✅ تم تسجيل الدخول كمدير: {auth_manager.current_user['username']}")
        
        # 1. فحص عدد المديرين الحاليين
        print("\n1. فحص عدد المديرين في النظام...")
        admin_count_query = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = TRUE"
        admin_result = db.execute_query(admin_count_query)
        admin_count = admin_result[0]['count'] if admin_result else 0
        print(f"   📊 عدد المديرين النشطين: {admin_count}")
        
        # 2. فحص عدد المستخدمين العاديين
        print("\n2. فحص عدد المستخدمين العاديين...")
        user_count_query = "SELECT COUNT(*) as count FROM users WHERE role = 'user' AND is_active = TRUE"
        user_result = db.execute_query(user_count_query)
        user_count = user_result[0]['count'] if user_result else 0
        print(f"   👤 عدد المستخدمين العاديين النشطين: {user_count}")
        
        # 3. عرض جميع المستخدمين
        print("\n3. قائمة جميع المستخدمين...")
        all_users_query = """
            SELECT id, username, role, is_active, created_at 
            FROM users 
            ORDER BY role DESC, username
        """
        all_users = db.execute_query(all_users_query)
        
        if all_users:
            print("   📋 المستخدمون الموجودون:")
            for user in all_users:
                status = "نشط" if user['is_active'] else "غير نشط"
                role_text = "مدير" if user['role'] == 'admin' else "مستخدم عادي"
                print(f"      - {user['username']} (ID: {user['id']}) - {role_text} - {status}")
        else:
            print("   ⚠️ لا توجد مستخدمين في النظام")
        
        # 4. اختبار صلاحيات الحذف
        print("\n4. اختبار صلاحيات الحذف...")
        
        # فحص صلاحية إدارة المستخدمين
        can_manage = auth_manager.can_manage_users()
        print(f"   🔐 صلاحية إدارة المستخدمين: {'✅ متاحة' if can_manage else '❌ غير متاحة'}")
        
        if not can_manage:
            print("   ⚠️ المستخدم الحالي لا يملك صلاحية إدارة المستخدمين")
            return False
        
        # 5. محاكاة اختبار حذف مستخدم عادي (بدون تنفيذ فعلي)
        print("\n5. محاكاة اختبار حذف مستخدم عادي...")
        regular_users = [user for user in all_users if user['role'] == 'user' and user['is_active']]
        
        if regular_users:
            test_user = regular_users[0]
            print(f"   👤 مستخدم الاختبار: {test_user['username']} (ID: {test_user['id']})")
            print("   ✅ يمكن حذف المستخدمين العاديين (محاكاة)")
        else:
            print("   ⚠️ لا توجد مستخدمين عاديين للاختبار")
        
        # 6. محاكاة اختبار حذف مدير (بدون تنفيذ فعلي)
        print("\n6. محاكاة اختبار حذف مدير...")
        admin_users = [user for user in all_users if user['role'] == 'admin' and user['is_active'] and user['id'] != auth_manager.current_user['id']]
        
        if admin_users:
            test_admin = admin_users[0]
            print(f"   👑 مدير الاختبار: {test_admin['username']} (ID: {test_admin['id']})")
            
            if admin_count > 1:
                print("   ✅ يمكن حذف هذا المدير (يوجد مديرين آخرين)")
            else:
                print("   ❌ لا يمكن حذف المدير الوحيد في النظام")
        else:
            print("   ⚠️ لا توجد مديرين آخرين للاختبار")
        
        # 7. اختبار دالة التحقق من أمان الحذف
        print("\n7. اختبار دالة التحقق من أمان الحذف...")
        
        # محاكاة استيراد MainWindow للاختبار
        try:
            import customtkinter as ctk
            from gui.main_window import MainWindow
            
            # إنشاء نافذة اختبار
            test_window = ctk.CTk()
            test_window.withdraw()
            
            main_window = MainWindow(test_window)
            
            # اختبار دالة get_admin_count
            admin_count_from_function = main_window.get_admin_count()
            print(f"   📊 عدد المديرين من الدالة: {admin_count_from_function}")
            
            # اختبار دالة check_admin_deletion_safety
            if admin_users:
                can_delete, safety_message = main_window.check_admin_deletion_safety(admin_users[0])
                print(f"   🔒 أمان حذف المدير: {'✅ آمن' if can_delete else '❌ غير آمن'}")
                print(f"   💬 رسالة الأمان: {safety_message}")
            
            test_window.destroy()
            print("   ✅ دوال التحقق تعمل بشكل صحيح")
            
        except Exception as e:
            print(f"   ⚠️ خطأ في اختبار دوال التحقق: {e}")
        
        # 8. فحص تسجيل العمليات
        print("\n8. فحص تسجيل العمليات...")
        
        # فحص آخر العمليات المسجلة
        try:
            activity_query = """
                SELECT * FROM activity_logs 
                WHERE action LIKE '%delete%' 
                ORDER BY created_at DESC 
                LIMIT 5
            """
            activities = db.execute_query(activity_query)
            
            if activities:
                print("   📝 آخر عمليات الحذف المسجلة:")
                for activity in activities:
                    print(f"      - {activity['action']}: {activity['description']} ({activity['created_at']})")
            else:
                print("   📝 لا توجد عمليات حذف مسجلة")
                
        except Exception as e:
            print(f"   ⚠️ خطأ في فحص السجلات: {e}")
        
        auth_manager.logout()
        
        print("\n" + "=" * 55)
        print("📊 ملخص نتائج الاختبار:")
        print(f"   👑 عدد المديرين: {admin_count}")
        print(f"   👤 عدد المستخدمين العاديين: {user_count}")
        print(f"   🔐 صلاحية إدارة المستخدمين: {'✅ متاحة' if can_manage else '❌ غير متاحة'}")
        print("   ✅ يمكن حذف المستخدمين العاديين")
        print(f"   {'✅' if admin_count > 1 else '❌'} يمكن حذف المديرين (مع الحماية)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_users():
    """إنشاء مستخدمين تجريبيين للاختبار"""
    print("\n🔧 إنشاء مستخدمين تجريبيين للاختبار...")
    
    try:
        # تسجيل الدخول كمدير
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        # إنشاء مستخدم عادي تجريبي
        test_user_data = {
            'username': 'test_user',
            'password': 'test123',
            'role': 'user'
        }
        
        success, message = auth_manager.create_user(
            test_user_data['username'],
            test_user_data['password'],
            test_user_data['role']
        )
        
        if success:
            print("   ✅ تم إنشاء مستخدم تجريبي عادي: test_user")
        else:
            print(f"   ⚠️ فشل في إنشاء المستخدم التجريبي: {message}")
        
        # إنشاء مدير تجريبي
        test_admin_data = {
            'username': 'test_admin',
            'password': 'admin123',
            'role': 'admin'
        }
        
        success, message = auth_manager.create_user(
            test_admin_data['username'],
            test_admin_data['password'],
            test_admin_data['role']
        )
        
        if success:
            print("   ✅ تم إنشاء مدير تجريبي: test_admin")
        else:
            print(f"   ⚠️ فشل في إنشاء المدير التجريبي: {message}")
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين التجريبيين: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار صلاحيات المدير المحدثة")
    print("=" * 45)
    
    # إنشاء مستخدمين تجريبيين إذا لزم الأمر
    response = input("\nهل تريد إنشاء مستخدمين تجريبيين للاختبار؟ (y/N): ").strip().lower()
    if response in ['y', 'yes', 'نعم']:
        create_test_users()
    
    # تشغيل الاختبار
    if test_admin_delete_permissions():
        print("\n✅ جميع الاختبارات نجحت!")
        
        print("\n📋 الميزات الجديدة:")
        print("   ✅ المدير يمكنه حذف جميع أنواع المستخدمين")
        print("   ✅ تحذيرات خاصة عند حذف المديرين")
        print("   ✅ حماية من حذف المدير الوحيد")
        print("   ✅ تسجيل مفصل لعمليات الحذف")
        print("   ✅ تأكيدات متعددة للحماية")
        
        print("\n🚀 للاختبار في التطبيق:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول كمدير: admin / 123456")
        print("3. اذهب إلى 'إدارة المستخدمين'")
        print("4. جرب حذف مستخدم عادي أو مدير")
        print("5. لاحظ التحذيرات والتأكيدات الجديدة")
        
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        print("💡 تحقق من:")
        print("   - الاتصال بقاعدة البيانات")
        print("   - وجود مستخدم admin")
        print("   - صلاحيات قاعدة البيانات")

if __name__ == "__main__":
    main()
