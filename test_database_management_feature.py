#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة إدارة قاعدة البيانات الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_management_feature():
    """اختبار ميزة إدارة قاعدة البيانات"""
    print("🧪 اختبار ميزة إدارة قاعدة البيانات")
    print("=" * 50)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول كمدير
        print("\n2. اختبار تسجيل الدخول كمدير...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # التحقق من صلاحيات المدير
        if not auth_manager.is_admin():
            print("❌ المستخدم ليس مديراً")
            return False
        print("✅ المستخدم مدير - يمكنه الوصول لإدارة قاعدة البيانات")
        
        # 3. اختبار استيراد وحدة النسخ الاحتياطي
        print("\n3. اختبار استيراد وحدة النسخ الاحتياطي...")
        try:
            from utils.backup import backup_manager
            print("✅ تم استيراد وحدة النسخ الاحتياطي بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد وحدة النسخ الاحتياطي: {e}")
            return False
        
        # 4. اختبار إعدادات النسخ الاحتياطي
        print("\n4. اختبار إعدادات النسخ الاحتياطي...")
        try:
            from config.settings import BACKUP_CONFIG, BACKUP_INTERVALS, DATABASE_CONNECTION_SETTINGS
            
            print("✅ إعدادات النسخ الاحتياطي:")
            print(f"   - النسخ التلقائي: {BACKUP_CONFIG.get('auto_backup', 'غير محدد')}")
            print(f"   - فترة النسخ: {BACKUP_CONFIG.get('backup_interval_hours', 'غير محدد')} ساعة")
            print(f"   - عدد النسخ المحفوظة: {BACKUP_CONFIG.get('max_backup_files', 'غير محدد')}")
            print(f"   - مجلد النسخ: {BACKUP_CONFIG.get('backup_location', 'غير محدد')}")
            
            print("✅ فترات النسخ الاحتياطي المتاحة:")
            for key, info in BACKUP_INTERVALS.items():
                print(f"   - {key}: {info['name']} ({info['hours']} ساعة)")
                
            print("✅ إعدادات اتصال قاعدة البيانات:")
            print(f"   - مهلة الاختبار: {DATABASE_CONNECTION_SETTINGS.get('test_timeout', 'غير محدد')} ثانية")
            print(f"   - محاولات إعادة الاتصال: {DATABASE_CONNECTION_SETTINGS.get('retry_attempts', 'غير محدد')}")
            
        except Exception as e:
            print(f"❌ خطأ في إعدادات النسخ الاحتياطي: {e}")
            return False
        
        # 5. اختبار وظائف النسخ الاحتياطي
        print("\n5. اختبار وظائف النسخ الاحتياطي...")
        
        # اختبار تحميل الإعدادات
        try:
            backup_manager.load_backup_settings()
            print("✅ تم تحميل إعدادات النسخ الاحتياطي")
        except Exception as e:
            print(f"❌ فشل تحميل إعدادات النسخ الاحتياطي: {e}")
            return False
        
        # اختبار الحصول على قائمة النسخ الاحتياطية
        try:
            backup_files = backup_manager.get_backup_files()
            print(f"✅ تم العثور على {len(backup_files)} نسخة احتياطية")
            
            if backup_files:
                print("   النسخ الاحتياطية الموجودة:")
                for backup in backup_files[:3]:  # عرض أول 3 نسخ فقط
                    print(f"   - {backup['name']} ({backup['size_mb']} MB)")
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return False
        
        # اختبار اتصال قاعدة البيانات
        try:
            success, message = backup_manager.test_database_connection()
            if success:
                print("✅ اختبار اتصال قاعدة البيانات نجح")
            else:
                print(f"⚠️ اختبار اتصال قاعدة البيانات فشل: {message}")
        except Exception as e:
            print(f"❌ خطأ في اختبار اتصال قاعدة البيانات: {e}")
            return False
        
        # 6. اختبار استيراد الواجهة الرئيسية مع الزر الجديد
        print("\n6. اختبار استيراد الواجهة الرئيسية...")
        try:
            from gui.main_window import MainWindow
            print("✅ تم استيراد MainWindow بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد MainWindow: {e}")
            return False
        
        # 7. فحص وجود الدوال الجديدة
        print("\n7. فحص وجود الدوال الجديدة...")
        
        # قراءة ملف main_window.py للتحقق من وجود الدوال الجديدة
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'def show_database_management(',
            'def load_database_management_sections(',
            'def create_backup_management_section(',
            'def create_database_connection_section(',
            'def create_instant_backup(',
            'def restore_backup_dialog(',
            'def test_database_connection(',
            'def save_backup_settings('
        ]
        
        all_functions_exist = True
        for func in required_functions:
            if func in content:
                print(f"✅ الدالة {func} موجودة")
            else:
                print(f"❌ الدالة {func} مفقودة")
                all_functions_exist = False
        
        if not all_functions_exist:
            return False
        
        # 8. فحص وجود زر إدارة قاعدة البيانات للمديرين
        print("\n8. فحص وجود زر إدارة قاعدة البيانات...")
        
        if 'auth_manager.is_admin()' in content and '"🗄️ إدارة قاعدة البيانات"' in content:
            print("✅ زر إدارة قاعدة البيانات موجود ومحدود للمديرين")
        else:
            print("❌ زر إدارة قاعدة البيانات غير موجود أو غير محدود للمديرين")
            return False
        
        # 9. اختبار مكونات واجهة المستخدم
        print("\n9. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            print("✅ مكونات واجهة المستخدم متاحة")
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        # 10. محاكاة إنشاء واجهة إدارة قاعدة البيانات
        print("\n10. محاكاة إنشاء واجهة إدارة قاعدة البيانات...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة تجريبية
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # محاولة إنشاء MainWindow
            main_window = MainWindow(test_window)
            print("✅ تم إنشاء MainWindow بنجاح")
            
            # التحقق من وجود زر إدارة قاعدة البيانات للمديرين
            if hasattr(main_window, 'menu_buttons'):
                if 'database' in main_window.menu_buttons:
                    print("✅ زر إدارة قاعدة البيانات موجود في menu_buttons")
                else:
                    print("⚠️ زر إدارة قاعدة البيانات غير موجود (قد يكون بسبب عدم وجود صلاحيات مدير)")
            
            # إغلاق النافذة التجريبية
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة إنشاء الواجهة: {e}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_functionality():
    """اختبار وظائف النسخ الاحتياطي"""
    print("\n🔄 اختبار وظائف النسخ الاحتياطي")
    print("=" * 40)
    
    try:
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        from utils.backup import backup_manager
        
        # اختبار إنشاء نسخة احتياطية تجريبية
        print("1. اختبار إنشاء نسخة احتياطية تجريبية...")
        
        # محاولة إنشاء نسخة احتياطية صغيرة (للاختبار فقط)
        try:
            success, message = backup_manager.create_backup("test_backup")
            if success:
                print("✅ تم إنشاء نسخة احتياطية تجريبية بنجاح")
            else:
                print(f"⚠️ فشل إنشاء النسخة الاحتياطية: {message}")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {e}")
        
        # اختبار تحديث الإعدادات
        print("\n2. اختبار تحديث إعدادات النسخ الاحتياطي...")
        try:
            test_settings = {
                'auto_backup_enabled': True,
                'backup_interval': 'daily',
                'max_backup_files': 25
            }
            
            success = backup_manager.update_settings(test_settings)
            if success:
                print("✅ تم تحديث إعدادات النسخ الاحتياطي بنجاح")
            else:
                print("⚠️ فشل تحديث إعدادات النسخ الاحتياطي")
        except Exception as e:
            print(f"⚠️ خطأ في تحديث الإعدادات: {e}")
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف النسخ الاحتياطي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لميزة إدارة قاعدة البيانات")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار ميزة إدارة قاعدة البيانات
    if test_database_management_feature():
        tests_passed += 1
        print("\n✅ اختبار ميزة إدارة قاعدة البيانات نجح")
    else:
        print("\n❌ اختبار ميزة إدارة قاعدة البيانات فشل")
    
    # اختبار وظائف النسخ الاحتياطي
    if test_backup_functionality():
        tests_passed += 1
        print("\n✅ اختبار وظائف النسخ الاحتياطي نجح")
    else:
        print("\n❌ اختبار وظائف النسخ الاحتياطي فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 ميزة إدارة قاعدة البيانات تعمل بشكل مثالي!")
        
        print("\n✨ الميزات الجديدة:")
        print("   🗄️ زر إدارة قاعدة البيانات (للمديرين فقط)")
        print("   📥 إنشاء نسخة احتياطية فورية")
        print("   📤 استعادة نسخة احتياطية")
        print("   ⚙️ إعدادات النسخ الاحتياطي التلقائي")
        print("   🔗 إعدادات الاتصال بقاعدة البيانات")
        print("   🔄 اختبار الاتصال")
        print("   💾 حفظ الإعدادات")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول كمدير: admin / 123456")
        print("3. ستجد زر '🗄️ إدارة قاعدة البيانات' في الشريط الجانبي")
        print("4. انقر على الزر للوصول لجميع ميزات إدارة قاعدة البيانات")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
