import os
from datetime import datetime

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'mohdam',  # كلمة مرور قاعدة البيانات
    'database': 'money_manager',
    'charset': 'utf8mb4',
    'autocommit': True
}

# إعدادات التطبيق
APP_CONFIG = {
    'title': 'مدير الأموال',
    'version': '1.0.0',
    'window_size': '1200x800',
    'min_window_size': (800, 600),
    'icon_path': 'assets/icon.ico',
    'language': 'ar',  # العربية افتراضياً
    'date_format': 'hijri',  # هجري افتراضياً
    'currency_default': 'SAR',
    'backup_interval': 24,  # ساعة للنسخ الاحتياطي التلقائي
    'text_direction': 'rtl',  # اتجاه النص من اليمين إلى اليسار
}

# إعدادات الخطوط والنصوص العربية
ARABIC_FONT_CONFIG = {
    # خطوط النظام المدعومة للعربية (بترتيب الأولوية)
    'font_families': [
        'Segoe UI',           # خط Windows الافتراضي مع دعم عربي ممتاز
        'Tahoma',             # خط كلاسيكي مع دعم عربي جيد
        'Arial Unicode MS',   # خط شامل مع دعم عربي
        'DejaVu Sans',        # خط مفتوح المصدر مع دعم عربي
        'Noto Sans Arabic',   # خط Google مع دعم عربي ممتاز
        'Arial',              # خط احتياطي
    ],

    # أحجام الخطوط
    'font_sizes': {
        'title': 28,          # العناوين الرئيسية
        'subtitle': 20,       # العناوين الفرعية
        'header': 16,         # رؤوس الأقسام
        'body': 14,           # النص العادي
        'small': 12,          # النص الصغير
        'button': 14,         # نص الأزرار
        'input': 14,          # نص الحقول
    },

    # إعدادات RTL
    'rtl_settings': {
        'text_direction': 'rtl',
        'default_anchor': 'e',        # الربط من الشرق (اليمين)
        'button_anchor': 'center',    # الأزرار في المنتصف
        'input_justify': 'right',     # محاذاة النص في الحقول لليمين
        'label_justify': 'right',     # محاذاة النص في التسميات لليمين
    }
}

# العملات المدعومة (4 عملات فقط مع دعم RTL)
SUPPORTED_CURRENCIES = {
    'SAR': {
        'name': 'ريال سعودي',
        'symbol': 'ر.س',
        'code': 'SAR',
        'rtl_name': 'ريال سعودي',
        'display_name': 'ريال سعودي - SAR'
    },
    'YER': {
        'name': 'ريال يمني',
        'symbol': 'ر.ي',
        'code': 'YER',
        'rtl_name': 'ريال يمني',
        'display_name': 'ريال يمني - YER'
    },
    'AED': {
        'name': 'درهم إماراتي',
        'symbol': 'د.إ',
        'code': 'AED',
        'rtl_name': 'درهم إماراتي',
        'display_name': 'درهم إماراتي - AED'
    },
    'USD': {
        'name': 'دولار أمريكي',
        'symbol': '$',
        'code': 'USD',
        'rtl_name': 'دولار أمريكي',
        'display_name': 'دولار أمريكي - USD'
    },
}

# أنواع الحسابات
ACCOUNT_TYPES = {
    'cash': 'صندوق نقدي',
    'bank': 'حساب بنكي',
    'credit_card': 'بطاقة ائتمان',
    'digital_wallet': 'محفظة إلكترونية',
    'investment': 'استثمار',
    'savings': 'حساب توفير',
    'other': 'أخرى'
}

# تصنيفات المصروفات
EXPENSE_CATEGORIES = {
    'food': 'طعام وشراب',
    'transport': 'مواصلات',
    'housing': 'سكن',
    'utilities': 'فواتير',
    'healthcare': 'صحة',
    'education': 'تعليم',
    'entertainment': 'ترفيه',
    'shopping': 'تسوق',
    'travel': 'سفر',
    'family': 'عائلة',
    'charity': 'صدقات',
    'investment': 'استثمار',
    'debt': 'ديون',
    'other': 'أخرى'
}

# تصنيفات الواردات
INCOME_CATEGORIES = {
    'salary': 'راتب',
    'business': 'أعمال',
    'investment': 'استثمار',
    'freelance': 'عمل حر',
    'rental': 'إيجار',
    'gift': 'هدية',
    'bonus': 'مكافأة',
    'refund': 'استرداد',
    'other': 'أخرى'
}

# صلاحيات المستخدمين
USER_ROLES = {
    'admin': {
        'name': 'مدير',
        'permissions': [
            'view_all_data',
            'edit_all_data',
            'delete_data',
            'manage_users',
            'backup_restore',
            'system_settings'
        ]
    },
    'user': {
        'name': 'مستخدم',
        'permissions': [
            'view_own_data',
            'edit_own_data',
            'add_transactions',
            'generate_reports'
        ]
    }
}

# مسارات الملفات
PATHS = {
    'backup_dir': 'backups',
    'uploads_dir': 'uploads',
    'reports_dir': 'reports',
    'logs_dir': 'logs',
    'assets_dir': 'assets'
}

# إنشاء المجلدات إذا لم تكن موجودة
for path in PATHS.values():
    os.makedirs(path, exist_ok=True)

# إعدادات التقارير
REPORT_FORMATS = ['PDF', 'Excel', 'CSV']

# إعدادات النسخ الاحتياطي المحسنة
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval_hours': 24,
    'max_backup_files': 30,
    'backup_location': PATHS['backup_dir'],
    'include_uploads': True,
    'include_logs': False,
    'compression_level': 6,  # مستوى الضغط (0-9)
    'backup_on_startup': False,
    'backup_on_shutdown': True,
    'notification_enabled': True,
    'cleanup_enabled': True
}

# خيارات فترات النسخ الاحتياطي
BACKUP_INTERVALS = {
    'hourly': {'hours': 1, 'name': 'كل ساعة'},
    'daily': {'hours': 24, 'name': 'يومياً'},
    'weekly': {'hours': 168, 'name': 'أسبوعياً'},  # 24 * 7
    'monthly': {'hours': 720, 'name': 'شهرياً'}  # 24 * 30
}

# إعدادات اتصال قاعدة البيانات القابلة للتعديل
DATABASE_CONNECTION_SETTINGS = {
    'test_timeout': 10,  # مهلة اختبار الاتصال بالثواني
    'retry_attempts': 3,  # عدد محاولات إعادة الاتصال
    'retry_delay': 5,  # تأخير بين المحاولات بالثواني
    'pool_size': 5,  # حجم مجموعة الاتصالات
    'max_overflow': 10,  # الحد الأقصى للاتصالات الإضافية
    'pool_timeout': 30,  # مهلة انتظار الاتصال
    'pool_recycle': 3600  # إعادة تدوير الاتصالات (بالثواني)
}
