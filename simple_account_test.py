#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإنشاء الحسابات
"""

import sys
import os
import mysql.connector

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_account_creation():
    """اختبار إنشاء حساب جديد"""
    print("🧪 اختبار إنشاء حساب جديد...")
    
    try:
        # استيراد النماذج
        from database.models import Account
        from utils.auth import auth_manager
        
        # تسجيل دخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        user_id = auth_manager.current_user['id']
        
        # إنشاء حساب تجريبي
        account_name = "حساب تجريبي للاختبار"
        
        print(f"🔍 إنشاء حساب: {account_name}")
        
        account_id = Account.create(
            user_id=user_id,
            name=account_name,
            description="حساب تجريبي"
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب بنجاح (ID: {account_id})")
            
            # إضافة رصيد بالريال السعودي
            print("🔍 إضافة رصيد بالريال السعودي...")
            
            balance_result = Account.add_currency_balance(account_id, 1, 1000.0)
            if balance_result:
                print("✅ تم إضافة الرصيد بنجاح")
                
                # فحص الرصيد
                balance = Account.get_currency_balance(account_id, 1)
                print(f"💰 الرصيد: {balance:,.2f} ر.س")
            else:
                print("❌ فشل في إضافة الرصيد")
            
            # حذف الحساب التجريبي
            connection = mysql.connector.connect(
                host='localhost',
                port=3306,
                user='root',
                password='mohdam',
                database='money_manager'
            )
            cursor = connection.cursor()
            cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
            connection.commit()
            cursor.close()
            connection.close()
            
            print("🗑️ تم حذف الحساب التجريبي")
            
            return True
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*50)
    print("🧪 اختبار مبسط لإنشاء الحسابات")
    print("="*50)
    
    if test_account_creation():
        print("\n✅ الاختبار نجح - إنشاء الحسابات يعمل بشكل صحيح")
    else:
        print("\n❌ الاختبار فشل - هناك مشكلة في إنشاء الحسابات")

if __name__ == "__main__":
    main()
