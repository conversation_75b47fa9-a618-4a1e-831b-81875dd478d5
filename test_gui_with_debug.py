#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهة مع التسجيل المفصل
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_with_debug():
    """اختبار لوحة التحكم مع التسجيل المفصل"""
    print("🧪 اختبار لوحة التحكم مع التسجيل المفصل...")
    
    try:
        # استيراد المكونات المطلوبة
        from utils.auth import auth_manager
        from gui.main_window import MainWindow
        import customtkinter as ctk
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        print(f"👤 المستخدم: {auth_manager.current_user['username']}")
        print(f"🆔 معرف المستخدم: {auth_manager.current_user['id']}")
        
        # إنشاء نافذة التطبيق
        print("\n🖥️ إنشاء نافذة التطبيق...")
        root = ctk.CTk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء نافذة إدارة الأموال
        app = MainWindow(root)
        
        print("✅ تم إنشاء نافذة التطبيق")
        
        # اختبار دالة get_dashboard_stats مباشرة
        print("\n📊 اختبار دالة get_dashboard_stats مباشرة...")
        stats = app.get_dashboard_stats()
        
        print(f"\n📋 نتائج get_dashboard_stats:")
        print(f"   - currency_balances: {len(stats['currency_balances']) if stats['currency_balances'] else 0} عملة")
        print(f"   - currency_transactions: {len(stats['currency_transactions']) if stats['currency_transactions'] else 0} معاملة")
        print(f"   - accounts_count: {stats['accounts_count']}")
        
        if stats['currency_balances']:
            print("\n💰 تفاصيل الأرصدة:")
            for balance in stats['currency_balances']:
                print(f"   - {balance['name']} ({balance['code']}): {balance['total_balance']:,.2f} {balance['symbol']}")
        
        if stats['currency_transactions']:
            print("\n📈 تفاصيل المعاملات الشهرية:")
            for trans in stats['currency_transactions']:
                type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
                print(f"   - {trans['code']} {type_text}: {trans['transaction_count']} معاملة بقيمة {trans['total_amount']:,.2f} {trans['symbol']}")
        
        # اختبار إنشاء البطاقات
        print("\n🎨 اختبار إنشاء البطاقات...")
        
        # إنشاء إطار تجريبي
        test_frame = ctk.CTkFrame(root)
        test_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # استدعاء دالة إنشاء البطاقات
        app.create_stats_cards(test_frame)
        
        print("✅ تم إنشاء البطاقات")
        
        # تنظيف
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات من الواجهة"""
    print("\n🔗 اختبار الاتصال بقاعدة البيانات من الواجهة...")
    
    try:
        from database.connection import DatabaseConnection
        
        db_conn = DatabaseConnection()
        success = db_conn.connect()
        
        if success:
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            
            # اختبار استعلام بسيط
            result = db_conn.execute_query("SELECT COUNT(*) as count FROM currencies WHERE is_active = TRUE")
            if result:
                print(f"✅ عدد العملات النشطة: {result[0]['count']}")
            
            db_conn.close()
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار الواجهة مع التسجيل المفصل")
    print("="*60)
    
    # اختبار الاتصال بقاعدة البيانات
    db_success = test_database_connection()
    
    if not db_success:
        print("❌ فشل في اختبار قاعدة البيانات - لا يمكن المتابعة")
        return
    
    # اختبار لوحة التحكم
    gui_success = test_dashboard_with_debug()
    
    print("\n" + "="*60)
    print("📋 ملخص نتائج الاختبار:")
    print(f"✅ اختبار قاعدة البيانات: {'نجح' if db_success else 'فشل'}")
    print(f"✅ اختبار لوحة التحكم: {'نجح' if gui_success else 'فشل'}")
    
    if db_success and gui_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 راجع رسائل التسجيل أعلاه لفهم سلوك لوحة التحكم")
        print("\n🚀 الآن يمكنك تشغيل التطبيق:")
        print("   python main.py")
        print("   وراقب رسائل التسجيل في الطرفية")
    else:
        print("\n⚠️ توجد مشاكل تحتاج إلى إصلاح")
        print("راجع الأخطاء أعلاه")
    
    print("="*60)

if __name__ == "__main__":
    main()
