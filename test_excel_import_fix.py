#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة عدم ظهور المعاملات المستوردة من Excel
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Transaction, Account, Currency
from utils.auth import auth_manager
from datetime import datetime, date
import pandas as pd

def test_transaction_creation():
    """اختبار إنشاء معاملة مباشرة"""
    print("🧪 اختبار إنشاء معاملة مباشرة...")
    
    try:
        # محاولة تسجيل دخول مستخدم تجريبي
        # (في الاختبار الحقيقي، يجب أن يكون هناك مستخدم مسجل دخول)
        
        # الحصول على أول مستخدم من قاعدة البيانات
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ لا يوجد مستخدمين في قاعدة البيانات")
            return False
            
        user_id = users[0]['id']
        print(f"✅ تم العثور على مستخدم: {users[0]['username']}")
        
        # الحصول على أول حساب
        accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s LIMIT 1", (user_id,))
        if not accounts:
            print("❌ لا يوجد حسابات للمستخدم")
            return False
            
        account_id = accounts[0]['id']
        print(f"✅ تم العثور على حساب: {accounts[0]['name']}")
        
        # الحصول على أول عملة
        currencies = db.execute_query("SELECT * FROM currencies WHERE is_active = 1 LIMIT 1")
        if not currencies:
            print("❌ لا يوجد عملات نشطة")
            return False
            
        currency_id = currencies[0]['id']
        print(f"✅ تم العثور على عملة: {currencies[0]['name']}")
        
        # إنشاء معاملة تجريبية
        transaction_id = Transaction.create(
            user_id=user_id,
            account_id=account_id,
            currency_id=currency_id,
            transaction_type='income',
            amount=1000,
            description='اختبار إنشاء معاملة',
            transaction_date=date.today()
        )
        
        if transaction_id > 0:
            print(f"✅ تم إنشاء معاملة بنجاح - ID: {transaction_id}")
            
            # التحقق من وجود المعاملة في قاعدة البيانات
            query = """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.id = %s
            """
            result = db.execute_query(query, (transaction_id,))
            
            if result:
                transaction = result[0]
                print(f"✅ تم التحقق من المعاملة:")
                print(f"   المبلغ: {transaction['amount']} {transaction['currency_symbol']}")
                print(f"   النوع: {transaction['transaction_type']}")
                print(f"   الحساب: {transaction['account_name']}")
                print(f"   التاريخ: {transaction['transaction_date']}")
                print(f"   الوصف: {transaction['description']}")
                
                # حذف المعاملة التجريبية
                if Transaction.delete(transaction_id):
                    print("✅ تم حذف المعاملة التجريبية بنجاح")
                else:
                    print("⚠️ فشل في حذف المعاملة التجريبية")
                
                return True
            else:
                print("❌ لم يتم العثور على المعاملة في قاعدة البيانات")
                return False
        else:
            print("❌ فشل في إنشاء المعاملة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء المعاملة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_data_simulation():
    """محاكاة بيانات Excel واختبار معالجتها"""
    print("\n🧪 اختبار محاكاة بيانات Excel...")
    
    try:
        # إنشاء بيانات تجريبية مشابهة لما يأتي من Excel
        test_data = {
            'المبلغ': [500, 750, 1000],
            'الحساب': ['الحساب الرئيسي', 'حساب التوفير', 'الحساب الرئيسي'],
            'العملة': ['SAR', 'USD', 'SAR'],
            'التاريخ': [
                datetime.now().strftime('%Y-%m-%d'),
                datetime.now().strftime('%Y-%m-%d'),
                datetime.now().strftime('%Y-%m-%d')
            ],
            'الوصف': [
                'اختبار وارد 1',
                'اختبار وارد 2', 
                'اختبار وارد 3'
            ]
        }
        
        df = pd.DataFrame(test_data)
        print("✅ تم إنشاء بيانات Excel تجريبية:")
        print(df.to_string(index=False))
        
        # الحصول على معلومات المستخدم والحسابات
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if not users:
            print("❌ لا يوجد مستخدمين")
            return False
            
        user_id = users[0]['id']
        
        # الحصول على الحسابات والعملات
        accounts_query = "SELECT id, name FROM accounts WHERE user_id = %s AND is_active = 1"
        accounts = db.execute_query(accounts_query, (user_id,))
        accounts_dict = {acc['name']: acc['id'] for acc in accounts}
        
        currencies_query = "SELECT id, code, symbol FROM currencies WHERE is_active = 1"
        currencies = db.execute_query(currencies_query)
        currencies_dict = {}
        for curr in currencies:
            currencies_dict[curr['code']] = curr['id']
            currencies_dict[curr['symbol']] = curr['id']
        
        print(f"✅ الحسابات المتاحة: {list(accounts_dict.keys())}")
        print(f"✅ العملات المتاحة: {list(currencies_dict.keys())}")
        
        # محاكاة عملية الاستيراد
        successful_imports = 0
        failed_imports = 0
        error_messages = []
        
        for index, row in df.iterrows():
            try:
                amount = float(row['المبلغ'])
                account_name = str(row['الحساب']).strip()
                currency_code = str(row['العملة']).strip()
                transaction_date = row['التاريخ']
                description = str(row['الوصف']).strip()
                
                # التحقق من البيانات
                if amount <= 0:
                    error_messages.append(f"الصف {index + 1}: مبلغ غير صحيح")
                    failed_imports += 1
                    continue
                    
                if account_name not in accounts_dict:
                    error_messages.append(f"الصف {index + 1}: حساب غير موجود ({account_name})")
                    failed_imports += 1
                    continue
                    
                if currency_code not in currencies_dict:
                    error_messages.append(f"الصف {index + 1}: عملة غير مدعومة ({currency_code})")
                    failed_imports += 1
                    continue
                
                # إنشاء المعاملة
                account_id = accounts_dict[account_name]
                currency_id = currencies_dict[currency_code]
                
                transaction_id = Transaction.create(
                    user_id=user_id,
                    account_id=account_id,
                    currency_id=currency_id,
                    transaction_type='income',
                    amount=amount,
                    description=description,
                    transaction_date=transaction_date
                )
                
                if transaction_id > 0:
                    successful_imports += 1
                    print(f"✅ تم إنشاء معاملة {index + 1} - ID: {transaction_id}")
                else:
                    error_messages.append(f"الصف {index + 1}: فشل في حفظ المعاملة")
                    failed_imports += 1
                    
            except Exception as e:
                error_messages.append(f"الصف {index + 1}: خطأ - {str(e)}")
                failed_imports += 1
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"✅ نجح: {successful_imports}")
        print(f"❌ فشل: {failed_imports}")
        if error_messages:
            print("🔍 الأخطاء:")
            for error in error_messages:
                print(f"   • {error}")
        
        return successful_imports > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار محاكاة Excel: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔧 اختبار إصلاح مشكلة استيراد Excel")
    print("=" * 50)
    
    # اختبار إنشاء معاملة مباشرة
    test1_result = test_transaction_creation()
    
    # اختبار محاكاة بيانات Excel
    test2_result = test_excel_data_simulation()
    
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    print(f"   اختبار إنشاء المعاملة: {'✅ نجح' if test1_result else '❌ فشل'}")
    print(f"   اختبار محاكاة Excel: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        print("\n🎉 جميع الاختبارات نجحت! المشكلة تم إصلاحها.")
        print("💡 يمكنك الآن اختبار استيراد Excel في التطبيق.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. قد تحتاج لمراجعة إضافية.")

if __name__ == "__main__":
    main()
