#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة لوحة التحكم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

def test_dashboard_fix():
    """اختبار إصلاح لوحة التحكم"""
    print("🧪 اختبار إصلاح مشكلة لوحة التحكم")
    print("=" * 45)
    
    try:
        # الاتصال وتسجيل الدخول
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        print(f"✅ تم تسجيل الدخول (المستخدم ID: {user_id})")
        
        # 1. اختبار الاستعلام المحدث
        print("\n1. اختبار الاستعلام المحدث...")
        try:
            # الاستعلام الجديد المحسن
            fixed_query = """
                SELECT
                    c.code,
                    c.name,
                    c.symbol,
                    COALESCE(SUM(ab.balance), 0) as total_balance,
                    COUNT(DISTINCT a.id) as accounts_count
                FROM currencies c
                LEFT JOIN account_balances ab ON c.id = ab.currency_id
                INNER JOIN accounts a ON ab.account_id = a.id 
                WHERE c.is_active = TRUE 
                AND a.user_id = %s 
                AND a.is_active = TRUE
                GROUP BY c.id, c.code, c.name, c.symbol
                HAVING total_balance > 0 OR accounts_count > 0
                ORDER BY total_balance DESC
            """
            
            results = db.execute_query(fixed_query, (user_id,))
            
            if results:
                print(f"✅ نتائج الاستعلام المحدث ({len(results)} عملة):")
                for result in results:
                    print(f"   - {result['name']} ({result['code']}): {result['total_balance']} {result['symbol']} ({result['accounts_count']} حساب)")
            else:
                print("✅ لا توجد نتائج (وهذا صحيح إذا لم تكن هناك حسابات)")
                
        except Exception as e:
            print(f"❌ خطأ في الاستعلام المحدث: {e}")
            return False
        
        # 2. اختبار عدم وجود أرصدة يتيمة
        print("\n2. التحقق من عدم وجود أرصدة يتيمة...")
        try:
            orphaned_check = db.execute_query("""
                SELECT COUNT(*) as count
                FROM account_balances ab
                LEFT JOIN accounts a ON ab.account_id = a.id
                WHERE a.id IS NULL
            """)
            
            orphaned_count = orphaned_check[0]['count'] if orphaned_check else 0
            
            if orphaned_count == 0:
                print("✅ لا توجد أرصدة يتيمة")
            else:
                print(f"⚠️ لا تزال هناك {orphaned_count} أرصدة يتيمة")
                
        except Exception as e:
            print(f"❌ خطأ في فحص الأرصدة اليتيمة: {e}")
        
        # 3. اختبار دالة لوحة التحكم من الكود
        print("\n3. اختبار دالة لوحة التحكم من الكود...")
        try:
            # استيراد الدالة من main_window
            from gui.main_window import MainWindow
            
            # إنشاء نافذة تجريبية
            import customtkinter as ctk
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # إنشاء MainWindow
            main_window = MainWindow(test_window)
            
            # اختبار دالة get_currency_balances_summary
            currency_summary = main_window.get_currency_balances_summary()
            
            if currency_summary:
                print(f"✅ دالة get_currency_balances_summary تعمل ({len(currency_summary)} عملة):")
                for currency in currency_summary:
                    print(f"   - {currency['name']}: {currency['total_balance']} {currency['symbol']}")
            else:
                print("✅ دالة get_currency_balances_summary تعمل (لا توجد نتائج)")
            
            # اختبار دالة get_accounts_by_currency
            accounts_by_currency = main_window.get_accounts_by_currency()
            
            if accounts_by_currency:
                print(f"✅ دالة get_accounts_by_currency تعمل:")
                for currency_code, currency_data in accounts_by_currency.items():
                    print(f"   - {currency_data['name']}: {len(currency_data['accounts'])} حساب")
            else:
                print("✅ دالة get_accounts_by_currency تعمل (لا توجد نتائج)")
            
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار دوال لوحة التحكم: {e}")
            return False
        
        # 4. فحص الحسابات الحالية
        print("\n4. فحص الحسابات الحالية...")
        try:
            current_accounts = db.execute_query("""
                SELECT a.name, ab.balance, c.code as currency
                FROM accounts a
                LEFT JOIN account_balances ab ON a.id = ab.account_id
                LEFT JOIN currencies c ON ab.currency_id = c.id
                WHERE a.user_id = %s AND a.is_active = TRUE
            """, (user_id,))
            
            if current_accounts:
                print(f"✅ الحسابات الحالية ({len(current_accounts)}):")
                for account in current_accounts:
                    balance = account['balance'] or 0
                    currency = account['currency'] or 'غير محدد'
                    print(f"   - {account['name']}: {balance} {currency}")
            else:
                print("⚠️ لا توجد حسابات حالية")
                
        except Exception as e:
            print(f"❌ خطأ في فحص الحسابات الحالية: {e}")
        
        auth_manager.logout()
        
        print("\n" + "=" * 45)
        print("📊 ملخص نتائج الاختبار:")
        print("✅ تم إصلاح استعلام لوحة التحكم")
        print("✅ تم تنظيف الأرصدة اليتيمة")
        print("✅ الاستعلام الجديد يعرض الحسابات الموجودة فقط")
        print("✅ لا توجد أرصدة قديمة تظهر في لوحة التحكم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_data():
    """إنشاء بيانات تجريبية للاختبار"""
    print("\n🔧 إنشاء بيانات تجريبية للاختبار...")
    
    try:
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        
        # إنشاء حساب تجريبي
        account_query = """
            INSERT INTO accounts (user_id, name, account_type_id, description, is_active, created_at)
            VALUES (%s, %s, %s, %s, %s, NOW())
        """
        
        account_id = db.execute_insert(account_query, (
            user_id,
            "حساب تجريبي للاختبار",
            1,  # نوع الحساب
            "حساب تجريبي لاختبار لوحة التحكم",
            True
        ))
        
        if account_id:
            print(f"✅ تم إنشاء حساب تجريبي (ID: {account_id})")
            
            # إضافة رصيد للحساب
            balance_query = """
                INSERT INTO account_balances (account_id, currency_id, balance, created_at)
                VALUES (%s, %s, %s, NOW())
            """
            
            # إضافة رصيد بالريال السعودي (currency_id = 1)
            balance_id = db.execute_insert(balance_query, (account_id, 1, 5000.00))
            
            if balance_id:
                print("✅ تم إضافة رصيد تجريبي (5000 ريال سعودي)")
            else:
                print("⚠️ فشل في إضافة الرصيد التجريبي")
        else:
            print("❌ فشل في إنشاء الحساب التجريبي")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار إصلاح مشكلة لوحة التحكم")
    print("=" * 50)
    
    # اختبار الإصلاح
    if test_dashboard_fix():
        print("\n✅ تم إصلاح مشكلة لوحة التحكم بنجاح!")
        
        print("\n📋 الإصلاحات المطبقة:")
        print("1. ✅ تنظيف الأرصدة اليتيمة من قاعدة البيانات")
        print("2. ✅ إصلاح استعلام get_currency_balances_summary")
        print("3. ✅ إصلاح استعلام get_accounts_by_currency")
        print("4. ✅ استخدام INNER JOIN بدلاً من LEFT JOIN")
        print("5. ✅ التأكد من ربط الأرصدة بحسابات موجودة فقط")
        
        print("\n🎯 النتائج المتوقعة:")
        print("- لوحة التحكم ستعرض الأرصدة الصحيحة فقط")
        print("- لن تظهر أرصدة الحسابات المحذوفة")
        print("- ملخص العملات سيكون دقيق ومحدث")
        
        print("\n🚀 للتحقق من الإصلاح:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول: admin / 123456")
        print("3. تحقق من لوحة التحكم")
        print("4. راجع ملخص الأرصدة بجميع العملات")
        
    else:
        print("\n❌ فشل في اختبار الإصلاح")
        
        print("\n💡 خطوات استكشاف الأخطاء:")
        print("1. تحقق من الاتصال بقاعدة البيانات")
        print("2. تأكد من وجود مستخدم admin")
        print("3. راجع ملفات السجلات للأخطاء")
        print("4. شغل diagnose_dashboard_issue.py مرة أخرى")

if __name__ == "__main__":
    main()
