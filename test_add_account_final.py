#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي لإضافة حساب جديد بعد الإصلاحات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from database.connection import db
from database.models import Account, Currency
from utils.auth import auth_manager
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, BUTTON_STYLES
from config.styles import ARABIC_TEXT_STYLES
import tkinter.messagebox as messagebox

class FinalAddAccountTest:
    """اختبار نهائي لإضافة حساب"""
    
    def __init__(self):
        # تسجيل دخول تلقائي للاختبار
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if users:
            auth_manager.current_user = users[0]
            print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
        
        # إنشاء النافذة
        self.window = ctk.CTk()
        self.window.title("اختبار نهائي - إضافة حساب جديد")
        self.window.geometry("600x700")
        self.window.configure(fg_color=COLORS['bg_primary'])
        
        self.create_test_interface()
        
        # تشغيل النافذة
        self.window.mainloop()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # عنوان الاختبار
        title_label = create_rtl_label(
            self.window,
            text="🎉 اختبار نهائي - إضافة حساب جديد",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 10))
        
        # معلومات الاختبار
        info_label = create_rtl_label(
            self.window,
            text="✅ تم إصلاح مشكلة currency_id المفقود\n✅ دالة Account.create() تعمل بشكل صحيح\n✅ دالة update_balance_for_currency() تعمل بشكل صحيح\n\nجرب إضافة حساب جديد الآن!",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        info_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        form_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        # اسم الحساب
        name_label = create_rtl_label(
            form_frame,
            text="اسم الحساب: *",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w", pady=(20, 5), padx=20)
        
        self.name_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: حساب البنك الأهلي الجديد",
            height=40
        )
        self.name_entry.pack(fill="x", pady=(0, 15), padx=20)
        
        # العملة
        currency_label = create_rtl_label(
            form_frame,
            text="العملة: *",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        # الحصول على العملات
        currencies = db.execute_query("SELECT id, name, symbol FROM currencies WHERE is_active = TRUE")
        currency_options = [f"{c['id']} - {c['name']} ({c['symbol']})" for c in currencies] if currencies else ["1 - ريال سعودي (ر.س)"]
        
        self.currency_combo = ctk.CTkComboBox(
            form_frame,
            values=currency_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.currency_combo.pack(fill="x", pady=(0, 15), padx=20)
        self.currency_combo.set(currency_options[0])
        
        # المبلغ الابتدائي
        balance_label = create_rtl_label(
            form_frame,
            text="المبلغ الابتدائي:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        balance_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        self.balance_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: 5000.00 (اختياري)",
            height=40
        )
        self.balance_entry.pack(fill="x", pady=(0, 15), padx=20)
        
        # الوصف
        desc_label = create_rtl_label(
            form_frame,
            text="الوصف:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        self.desc_entry = ctk.CTkTextbox(
            form_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        self.desc_entry.pack(fill="x", pady=(0, 20), padx=20)
        
        # أزرار الاختبار
        buttons_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        buttons_frame.pack(pady=20)
        
        # زر إضافة الحساب
        add_button = create_rtl_button(
            buttons_frame,
            text="💾 إضافة الحساب",
            command=self.add_account_test,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))
        
        # زر عرض الحسابات
        show_button = create_rtl_button(
            buttons_frame,
            text="📋 عرض جميع الحسابات",
            command=self.show_all_accounts,
            **BUTTON_STYLES['secondary']
        )
        show_button.pack(side="left", padx=(0, 10))
        
        # زر إغلاق
        close_button = create_rtl_button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.window.destroy,
            **BUTTON_STYLES['secondary']
        )
        close_button.pack(side="left")
        
        # منطقة النتائج
        results_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        results_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))
        
        results_title = create_rtl_label(
            results_frame,
            text="📊 نتائج الاختبار:",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        results_title.pack(pady=(15, 10))
        
        self.results_text = ctk.CTkTextbox(
            results_frame,
            height=150,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # رسالة ترحيبية
        self.log_result("🎯 جاهز لاختبار إضافة حساب جديد!")
        self.log_result("📝 املأ النموذج أعلاه واضغط على 'إضافة الحساب'")
    
    def log_result(self, message):
        """إضافة رسالة إلى منطقة النتائج"""
        self.results_text.insert("end", f"{message}\n")
        self.results_text.see("end")
        self.window.update()
    
    def add_account_test(self):
        """اختبار إضافة حساب جديد (محاكاة دالة save_new_account)"""
        try:
            self.log_result("\n🔄 بدء عملية إضافة الحساب...")
            
            # التحقق من البيانات
            name = self.name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                self.log_result("❌ خطأ: اسم الحساب مطلوب")
                return
            
            self.log_result(f"✅ اسم الحساب: {name}")
            
            # استخراج معرف العملة
            currency_text = self.currency_combo.get()
            currency_id = int(currency_text.split(' - ')[0]) if currency_text else 1
            self.log_result(f"✅ معرف العملة: {currency_id}")
            
            # الرصيد الابتدائي
            try:
                initial_balance = float(self.balance_entry.get() or "0")
                self.log_result(f"✅ الرصيد الابتدائي: {initial_balance}")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                self.log_result("❌ خطأ: رصيد غير صحيح")
                return
            
            description = self.desc_entry.get("1.0", "end-1c").strip()
            self.log_result(f"✅ الوصف: {description if description else 'لا يوجد'}")
            
            # إدراج الحساب في قاعدة البيانات
            user_id = auth_manager.current_user['id']
            self.log_result(f"✅ معرف المستخدم: {user_id}")
            
            self.log_result("🔄 إنشاء الحساب في قاعدة البيانات...")
            
            # استخدام نموذج Account للإنشاء مع تحديد العملة
            result = Account.create(
                user_id=user_id,
                name=name,
                currency_id=currency_id,
                description=description
            )
            
            self.log_result(f"📊 نتيجة Account.create(): {result}")
            
            if result and result > 0:
                self.log_result(f"✅ تم إنشاء الحساب بنجاح! معرف الحساب: {result}")
                
                # إضافة الرصيد الابتدائي إذا كان أكبر من صفر
                if initial_balance > 0:
                    self.log_result("🔄 إضافة الرصيد الابتدائي...")
                    balance_result = Account.update_balance_for_currency(result, currency_id, initial_balance)
                    
                    if balance_result and balance_result > 0:
                        self.log_result(f"✅ تم إضافة الرصيد الابتدائي: {initial_balance}")
                    else:
                        self.log_result("❌ فشل في إضافة الرصيد الابتدائي")
                
                # التحقق النهائي
                self.log_result("🔍 التحقق من الحساب المُنشأ...")
                created_account = Account.get_by_id(result)
                
                if created_account:
                    self.log_result("✅ تم التحقق من الحساب بنجاح:")
                    self.log_result(f"   📝 الاسم: {created_account['name']}")
                    self.log_result(f"   🆔 المعرف: {created_account['id']}")
                    self.log_result(f"   👤 المستخدم: {created_account['user_id']}")
                    self.log_result(f"   🔄 نشط: {'نعم' if created_account['is_active'] else 'لا'}")
                    
                    balances = created_account.get('balances', [])
                    if balances:
                        for balance in balances:
                            self.log_result(f"   💰 الرصيد: {balance['balance']} {balance['symbol']}")
                    else:
                        self.log_result("   💰 الرصيد: 0 (لا توجد أرصدة)")
                    
                    self.log_result("🎉 تم إضافة الحساب بنجاح!")
                    messagebox.showinfo("نجح", f"تم إضافة الحساب بنجاح!\nمعرف الحساب: {result}")
                    
                    # مسح النموذج
                    self.name_entry.delete(0, 'end')
                    self.balance_entry.delete(0, 'end')
                    self.desc_entry.delete("1.0", 'end')
                else:
                    self.log_result("❌ فشل في التحقق من الحساب المُنشأ")
                    messagebox.showerror("خطأ", "تم إنشاء الحساب ولكن فشل في التحقق منه")
            else:
                self.log_result("❌ فشل في إنشاء الحساب")
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")
        
        except Exception as e:
            self.log_result(f"❌ خطأ في إضافة الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    def show_all_accounts(self):
        """عرض جميع الحسابات"""
        try:
            self.log_result("\n📋 عرض جميع الحسابات:")
            
            user_id = auth_manager.current_user['id']
            accounts = Account.get_by_user(user_id)
            
            if accounts:
                self.log_result(f"📊 تم العثور على {len(accounts)} حساب:")
                
                for i, account in enumerate(accounts, 1):
                    self.log_result(f"\n{i}. {account.get('name')} (ID: {account.get('id')})")
                    self.log_result(f"   الوصف: {account.get('description', 'لا يوجد')}")
                    self.log_result(f"   نشط: {'نعم' if account.get('is_active') else 'لا'}")
                    
                    balances = account.get('balances', [])
                    if balances:
                        for balance in balances:
                            self.log_result(f"   الرصيد: {balance['balance']} {balance['symbol']}")
                    else:
                        self.log_result("   الرصيد: 0")
            else:
                self.log_result("❌ لا توجد حسابات")
        
        except Exception as e:
            self.log_result(f"❌ خطأ في عرض الحسابات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🎉 بدء الاختبار النهائي لإضافة حساب جديد")
    print("=" * 50)
    
    # تشغيل الاختبار
    FinalAddAccountTest()

if __name__ == "__main__":
    main()
