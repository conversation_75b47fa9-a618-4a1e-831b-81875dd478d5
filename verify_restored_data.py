#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من البيانات المستعادة
"""

import mysql.connector
from mysql.connector import Error

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def verify_accounts(cursor):
    """التحقق من الحسابات المستعادة"""
    print("👥 التحقق من الحسابات المستعادة...")
    
    try:
        cursor.execute("SELECT name, description, is_active FROM accounts ORDER BY name")
        accounts = cursor.fetchall()
        
        required_accounts = ['فضل', 'احمد', 'محمد', 'معاذ']
        found_accounts = [acc['name'] for acc in accounts]
        
        print(f"📋 الحسابات الموجودة ({len(accounts)}):")
        for acc in accounts:
            status = "نشط" if acc['is_active'] else "غير نشط"
            print(f"   ✅ {acc['name']}: {acc['description']} ({status})")
        
        missing = [acc for acc in required_accounts if acc not in found_accounts]
        if missing:
            print(f"\n⚠️ الحسابات المفقودة: {missing}")
        else:
            print(f"\n✅ جميع الحسابات المطلوبة موجودة!")
        
        return len(accounts)
        
    except Exception as e:
        print(f"❌ خطأ في فحص الحسابات: {e}")
        return 0

def verify_currencies(cursor):
    """التحقق من العملات المستعادة"""
    print("\n💱 التحقق من العملات المستعادة...")
    
    try:
        cursor.execute("SELECT code, name, symbol, is_active FROM currencies ORDER BY code")
        currencies = cursor.fetchall()
        
        required_currencies = ['SAR', 'AED', 'USD', 'YER']
        found_currencies = [curr['code'] for curr in currencies]
        
        print(f"📋 العملات الموجودة ({len(currencies)}):")
        for curr in currencies:
            status = "نشط" if curr['is_active'] else "غير نشط"
            print(f"   ✅ {curr['code']}: {curr['name']} ({curr['symbol']}) - {status}")
        
        missing = [code for code in required_currencies if code not in found_currencies]
        if missing:
            print(f"\n⚠️ العملات المفقودة: {missing}")
        else:
            print(f"\n✅ جميع العملات المطلوبة موجودة!")
        
        return len(currencies)
        
    except Exception as e:
        print(f"❌ خطأ في فحص العملات: {e}")
        return 0

def verify_transactions(cursor):
    """التحقق من المعاملات المستعادة"""
    print("\n💰 التحقق من المعاملات المستعادة...")
    
    try:
        cursor.execute("""
            SELECT t.type, COUNT(*) as count, SUM(t.amount) as total
            FROM transactions t
            GROUP BY t.type
        """)
        transaction_summary = cursor.fetchall()
        
        print(f"📊 ملخص المعاملات:")
        total_transactions = 0
        for summary in transaction_summary:
            total_transactions += summary['count']
            print(f"   {summary['type']}: {summary['count']} معاملة، المجموع: {summary['total']}")
        
        print(f"\n✅ إجمالي المعاملات: {total_transactions}")
        
        return total_transactions
        
    except Exception as e:
        print(f"❌ خطأ في فحص المعاملات: {e}")
        return 0

def verify_transfers(cursor):
    """التحقق من التحويلات المستعادة"""
    print("\n🔄 التحقق من التحويلات المستعادة...")
    
    try:
        cursor.execute("SELECT COUNT(*) as count, SUM(from_amount) as total FROM transfers")
        result = cursor.fetchone()
        
        count = result['count']
        total = result['total'] or 0
        
        print(f"📊 ملخص التحويلات:")
        print(f"   عدد التحويلات: {count}")
        print(f"   إجمالي المبالغ المحولة: {total}")
        
        if count > 0:
            cursor.execute("""
                SELECT 
                    fa.name as from_account,
                    ta.name as to_account,
                    t.from_amount,
                    t.description
                FROM transfers t
                JOIN accounts fa ON t.from_account_id = fa.id
                JOIN accounts ta ON t.to_account_id = ta.id
                LIMIT 3
            """)
            sample_transfers = cursor.fetchall()
            
            print(f"\n📝 عينة من التحويلات:")
            for transfer in sample_transfers:
                print(f"   - من {transfer['from_account']} إلى {transfer['to_account']}: {transfer['from_amount']} ({transfer['description']})")
        
        return count
        
    except Exception as e:
        print(f"❌ خطأ في فحص التحويلات: {e}")
        return 0

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("✅ التحقق من البيانات المستعادة - مدير الأموال")
    print("="*60)
    
    # الاتصال بقاعدة البيانات
    connection, cursor = connect_to_database()
    
    if not connection:
        print("❌ لا يمكن المتابعة بدون اتصال بقاعدة البيانات")
        return
    
    try:
        # التحقق من الحسابات
        accounts_count = verify_accounts(cursor)
        
        # التحقق من العملات
        currencies_count = verify_currencies(cursor)
        
        # التحقق من المعاملات
        transactions_count = verify_transactions(cursor)
        
        # التحقق من التحويلات
        transfers_count = verify_transfers(cursor)
        
        # ملخص النتائج
        print("\n" + "="*60)
        print("📋 ملخص البيانات المستعادة")
        print("="*60)
        print(f"👥 الحسابات: {accounts_count}")
        print(f"💱 العملات: {currencies_count}")
        print(f"💰 المعاملات: {transactions_count}")
        print(f"🔄 التحويلات: {transfers_count}")
        print("\n✅ تم استعادة جميع البيانات المطلوبة بنجاح!")
        print("💡 يمكنك الآن تشغيل التطبيق واستخدام جميع الوظائف المالية.")
        print("="*60)
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    main()
