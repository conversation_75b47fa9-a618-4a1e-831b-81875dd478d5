#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأزرار المُصلحة في نافذة إضافة الحساب
"""

import customtkinter as ctk
from config.colors import COLORS, BUTTON_STYLES

def test_fixed_dialog():
    """اختبار النافذة المُصلحة"""
    ctk.set_appearance_mode("light")
    root = ctk.CTk()
    root.title("اختبار الأزرار المُصلحة")
    root.geometry("300x200")
    
    def show_fixed_dialog():
        """عرض النافذة المُصلحة"""
        dialog = ctk.CTkToplevel(root)
        dialog.title("إضافة حساب جديد")
        dialog.geometry("500x700")  # الحجم الجديد
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()

        # توسيط النافذة
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (700 // 2)
        dialog.geometry(f"500x700+{x}+{y}")

        # عنوان النافذة
        title_label = ctk.CTkLabel(
            dialog,
            text="إضافة حساب جديد",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=COLORS['text_primary']
        )
        title_label.pack(pady=(20, 30))

        # إطار النموذج
        form_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        form_frame.pack(fill="x", padx=30, pady=(0, 10))

        # اسم الحساب
        name_label = ctk.CTkLabel(form_frame, text="اسم الحساب:", font=ctk.CTkFont(size=14))
        name_label.pack(anchor="w", pady=(0, 5))

        name_entry = ctk.CTkEntry(
            form_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="مثال: حساب الراجحي"
        )
        name_entry.pack(fill="x", pady=(0, 15))

        # نوع الحساب
        type_label = ctk.CTkLabel(form_frame, text="نوع الحساب:", font=ctk.CTkFont(size=14))
        type_label.pack(anchor="w", pady=(0, 5))

        type_entry = ctk.CTkEntry(
            form_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="مثال: حساب جاري، محفظة نقدية، بطاقة ائتمان، إلخ..."
        )
        type_entry.pack(fill="x", pady=(0, 15))

        # العملة
        currency_label = ctk.CTkLabel(form_frame, text="العملة:", font=ctk.CTkFont(size=14))
        currency_label.pack(anchor="w", pady=(0, 5))

        currency_combo = ctk.CTkComboBox(
            form_frame,
            values=["1 - ريال سعودي (ر.س)", "2 - دولار أمريكي ($)"],
            height=40,
            font=ctk.CTkFont(size=14)
        )
        currency_combo.pack(fill="x", pady=(0, 15))
        currency_combo.set("1 - ريال سعودي (ر.س)")

        # الرصيد الابتدائي
        balance_label = ctk.CTkLabel(form_frame, text="الرصيد الابتدائي:", font=ctk.CTkFont(size=14))
        balance_label.pack(anchor="w", pady=(0, 5))

        balance_entry = ctk.CTkEntry(
            form_frame,
            height=40,
            font=ctk.CTkFont(size=14),
            placeholder_text="0.00"
        )
        balance_entry.pack(fill="x", pady=(0, 15))

        # الوصف
        desc_label = ctk.CTkLabel(form_frame, text="الوصف (اختياري):", font=ctk.CTkFont(size=14))
        desc_label.pack(anchor="w", pady=(0, 5))

        desc_entry = ctk.CTkTextbox(
            form_frame,
            height=60,  # ارتفاع مُقلل
            font=ctk.CTkFont(size=14)
        )
        desc_entry.pack(fill="x", pady=(0, 5))

        # أزرار الحفظ والإلغاء - خارج form_frame
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=30, pady=(20, 30))

        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            width=120,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="right", padx=(15, 0))

        save_button = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            width=120,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            command=lambda: print("✅ تم الضغط على حفظ!"),
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="right")
        
        print("✅ تم إنشاء النافذة المُصلحة - تحقق من ظهور الأزرار بوضوح!")

    # زر لفتح النافذة
    test_button = ctk.CTkButton(
        root,
        text="اختبار النافذة المُصلحة",
        command=show_fixed_dialog,
        **BUTTON_STYLES['primary']
    )
    test_button.pack(pady=50)
    
    print("🚀 اختبار النافذة المُصلحة - اضغط على الزر")
    root.mainloop()

if __name__ == "__main__":
    test_fixed_dialog()
