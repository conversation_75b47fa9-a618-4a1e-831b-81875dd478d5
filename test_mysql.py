#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from mysql.connector import Error

def test_connection():
    print("اختبار الاتصال بـ MySQL...")
    
    configs = [
        {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam'
        },
        {
            'host': '127.0.0.1',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam'
        }
    ]
    
    for i, config in enumerate(configs, 1):
        try:
            print(f"\nمحاولة {i}: {config['host']}:{config['port']}")
            connection = mysql.connector.connect(**config)
            
            if connection.is_connected():
                print("✅ تم الاتصال بنجاح!")
                
                cursor = connection.cursor()
                cursor.execute("SHOW DATABASES")
                databases = cursor.fetchall()
                
                print("قواعد البيانات الموجودة:")
                for db in databases:
                    print(f"  - {db[0]}")
                
                # التحقق من وجود قاعدة بيانات money_manager
                db_exists = any('money_manager' in str(db) for db in databases)
                if db_exists:
                    print("✅ قاعدة بيانات money_manager موجودة")
                else:
                    print("⚠️ قاعدة بيانات money_manager غير موجودة")
                
                cursor.close()
                connection.close()
                return True
                
        except Error as e:
            print(f"❌ فشل الاتصال: {e}")
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
    
    return False

if __name__ == "__main__":
    test_connection()
