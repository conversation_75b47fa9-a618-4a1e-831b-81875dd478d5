#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للوحة التحكم
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_dashboard():
    """اختبار دالة لوحة التحكم في الواجهة"""
    print("🧪 اختبار دالة لوحة التحكم في الواجهة...")
    
    try:
        # استيراد المكونات المطلوبة
        from database.connection import get_db_connection
        from utils.auth import auth_manager
        import mysql.connector
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")

        # إنشاء اتصال قاعدة البيانات
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        # اختبار الاستعلامات المصححة
        user_id = auth_manager.current_user['id']
        current_month = "2025-01"
        
        print(f"👤 المستخدم: {user_id}")
        print(f"📅 الشهر الحالي: {current_month}")
        
        # 1. اختبار استعلام الأرصدة
        print("\n1️⃣ اختبار استعلام الأرصدة:")
        balance_query = """
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING total_balance > 0
            ORDER BY total_balance DESC
        """
        
        cursor.execute(balance_query, (user_id,))
        balance_results = cursor.fetchall()
        
        if balance_results:
            print("   ✅ نتائج الأرصدة:")
            for balance in balance_results:
                print(f"      - {balance['name']} ({balance['code']}): {balance['total_balance']:,.2f} {balance['symbol']}")
        else:
            print("   ❌ لا توجد أرصدة")
        
        # 2. اختبار استعلام المعاملات الشهرية
        print("\n2️⃣ اختبار استعلام المعاملات الشهرية:")
        transactions_query = """
            SELECT
                c.code,
                c.symbol,
                t.type,
                SUM(t.amount) as total_amount,
                COUNT(*) as transaction_count
            FROM transactions t
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s
            AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            AND c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol, t.type
            ORDER BY c.code, t.type
        """
        
        cursor.execute(transactions_query, (user_id, current_month))
        transactions_results = cursor.fetchall()
        
        if transactions_results:
            print("   ✅ نتائج المعاملات الشهرية:")
            for trans in transactions_results:
                type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
                print(f"      - {trans['code']} {type_text}: {trans['transaction_count']} معاملة بقيمة {trans['total_amount']:,.2f} {trans['symbol']}")
        else:
            print("   ❌ لا توجد معاملات شهرية")
        
        # 3. محاكاة البيانات المرجعة من get_dashboard_stats
        print("\n3️⃣ محاكاة get_dashboard_stats:")
        
        accounts_query = "SELECT COUNT(*) as accounts_count FROM accounts WHERE user_id = %s AND is_active = TRUE"
        cursor.execute(accounts_query, (user_id,))
        accounts_result = cursor.fetchall()
        accounts_count = accounts_result[0]['accounts_count'] if accounts_result else 0
        
        dashboard_stats = {
            'currency_balances': balance_results or [],
            'currency_transactions': transactions_results or [],
            'accounts_count': accounts_count
        }
        
        print(f"   📊 عدد الحسابات: {dashboard_stats['accounts_count']}")
        print(f"   💰 عدد العملات مع أرصدة: {len(dashboard_stats['currency_balances'])}")
        print(f"   📈 عدد أنواع المعاملات الشهرية: {len(dashboard_stats['currency_transactions'])}")
        
        # 4. محاكاة عرض البطاقات
        print("\n4️⃣ محاكاة عرض البطاقات:")
        
        print("   🏦 بطاقة الحسابات:")
        print(f"      عدد الحسابات: {dashboard_stats['accounts_count']}")
        
        print("   💰 بطاقات الأرصدة:")
        for balance in dashboard_stats['currency_balances']:
            print(f"      - رصيد {balance['code']}: {balance['total_balance']:,.0f} {balance['symbol']}")
        
        print("   📊 بطاقة المعاملات الشهرية:")
        if dashboard_stats['currency_transactions']:
            # تجميع المعاملات حسب العملة
            currency_groups = {}
            for trans in dashboard_stats['currency_transactions']:
                currency_key = trans['code']
                if currency_key not in currency_groups:
                    currency_groups[currency_key] = {'symbol': trans['symbol'], 'income': 0, 'expense': 0}
                
                if trans['type'] == 'income':
                    currency_groups[currency_key]['income'] = trans['total_amount']
                elif trans['type'] == 'expense':
                    currency_groups[currency_key]['expense'] = trans['total_amount']
            
            for currency_code, data in currency_groups.items():
                if data['income'] > 0 or data['expense'] > 0:
                    print(f"      💱 {currency_code}:")
                    if data['income'] > 0:
                        print(f"         📈 الواردات: +{data['income']:,.0f} {data['symbol']}")
                    if data['expense'] > 0:
                        print(f"         📉 المصروفات: -{data['expense']:,.0f} {data['symbol']}")
        else:
            print("      ⚠️ لا توجد معاملات شهرية")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار مبسط للوحة التحكم")
    print("="*60)
    
    success = test_gui_dashboard()
    
    print("\n" + "="*60)
    if success:
        print("✅ الاختبار نجح!")
        print("💡 الآن يمكنك تشغيل التطبيق ولوحة التحكم ستعرض:")
        print("   - جميع العملات في بطاقات الأرصدة")
        print("   - المعاملات الشهرية بجميع العملات")
        print("   - تحديث فوري عند إضافة معاملات")
        print("\n🚀 قم بتشغيل: python main.py")
    else:
        print("❌ الاختبار فشل!")
        print("⚠️ راجع الأخطاء أعلاه")
    
    print("="*60)

if __name__ == "__main__":
    main()
