#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لمقارنة التباين اللوني قبل وبعد الإصلاحات
"""

import customtkinter as ctk
from config.colors import COLORS

def create_color_comparison_window():
    """إنشاء نافذة لمقارنة الألوان"""
    
    # إنشاء النافذة
    window = ctk.CTk()
    window.title("مقارنة التباين اللوني - إصلاحات استيراد Excel")
    window.geometry("800x600")
    window.configure(fg_color=COLORS['bg_light'])
    
    # العنوان الرئيسي
    title = ctk.CTkLabel(
        window,
        text="🎨 مقارنة التباين اللوني",
        font=ctk.CTkFont(size=24, weight="bold"),
        text_color=COLORS['text_primary']
    )
    title.pack(pady=20)
    
    # إطار المقارنة
    comparison_frame = ctk.CTkFrame(window, fg_color="transparent")
    comparison_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # العمود الأيسر - قبل الإصلاح
    left_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_card'])
    left_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
    
    left_title = ctk.CTkLabel(
        left_frame,
        text="❌ قبل الإصلاح",
        font=ctk.CTkFont(size=18, weight="bold"),
        text_color=COLORS['error']
    )
    left_title.pack(pady=10)
    
    # مثال على الخلفية الداكنة مع النص الداكن (مشكلة)
    old_example = ctk.CTkFrame(left_frame, fg_color=COLORS['bg_secondary'])
    old_example.pack(fill="x", padx=20, pady=10)
    
    old_text = ctk.CTkLabel(
        old_example,
        text="نص صعب القراءة",
        font=ctk.CTkFont(size=14),
        text_color=COLORS['text_primary']  # نص داكن على خلفية داكنة
    )
    old_text.pack(pady=10)
    
    old_error = ctk.CTkLabel(
        old_example,
        text="• رسالة خطأ غير واضحة",
        font=ctk.CTkFont(size=12),
        text_color=COLORS['error']  # أحمر داكن على خلفية داكنة
    )
    old_error.pack(pady=5)
    
    # العمود الأيمن - بعد الإصلاح
    right_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_card'])
    right_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
    
    right_title = ctk.CTkLabel(
        right_frame,
        text="✅ بعد الإصلاح",
        font=ctk.CTkFont(size=18, weight="bold"),
        text_color=COLORS['success']
    )
    right_title.pack(pady=10)
    
    # مثال على الخلفية الداكنة مع النص الفاتح (محسن)
    new_example = ctk.CTkFrame(right_frame, fg_color=COLORS['bg_secondary'])
    new_example.pack(fill="x", padx=20, pady=10)
    
    new_text = ctk.CTkLabel(
        new_example,
        text="نص واضح ومقروء",
        font=ctk.CTkFont(size=14),
        text_color=COLORS['text_on_dark']  # نص أبيض على خلفية داكنة
    )
    new_text.pack(pady=10)
    
    new_error = ctk.CTkLabel(
        new_example,
        text="• رسالة خطأ واضحة ومميزة",
        font=ctk.CTkFont(size=12),
        text_color=COLORS['error_on_dark']  # أحمر فاتح على خلفية داكنة
    )
    new_error.pack(pady=5)
    
    # معلومات التباين
    info_frame = ctk.CTkFrame(window, fg_color=COLORS['bg_card'])
    info_frame.pack(fill="x", padx=20, pady=(0, 20))
    
    info_title = ctk.CTkLabel(
        info_frame,
        text="📊 معلومات التباين",
        font=ctk.CTkFont(size=16, weight="bold"),
        text_color=COLORS['text_primary']
    )
    info_title.pack(pady=10)
    
    info_text = ctk.CTkLabel(
        info_frame,
        text="التباين قبل الإصلاح: 2.1:1 ❌ | التباين بعد الإصلاح: 12.6:1 ✅\nمعيار WCAG AAA: 7:1 أو أعلى",
        font=ctk.CTkFont(size=12),
        text_color=COLORS['text_secondary']
    )
    info_text.pack(pady=(0, 10))
    
    # زر الإغلاق
    close_button = ctk.CTkButton(
        window,
        text="إغلاق",
        command=window.destroy,
        **COLORS['primary']
    )
    close_button.pack(pady=10)
    
    return window

if __name__ == "__main__":
    print("🎨 عرض مقارنة التباين اللوني...")
    
    # تشغيل النافذة
    ctk.set_appearance_mode("light")
    window = create_color_comparison_window()
    window.mainloop()
    
    print("✅ تم إغلاق نافذة المقارنة")
