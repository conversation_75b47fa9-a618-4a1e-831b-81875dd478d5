#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة المعاملات الشهرية في لوحة التحكم
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_monthly_transactions_display():
    """اختبار عرض المعاملات الشهرية"""
    try:
        print("🧪 اختبار عرض المعاملات الشهرية في لوحة التحكم...")
        print("=" * 60)
        
        from database.connection import db
        from utils.auth import auth_manager
        from datetime import datetime
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        current_month = datetime.now().strftime('%Y-%m')
        
        print(f"👤 معرف المستخدم: {user_id}")
        print(f"📅 الشهر الحالي: {current_month}")
        
        # 1. اختبار الاستعلام المُصلح
        print("\n1️⃣ اختبار الاستعلام المُصلح:")
        fixed_query = """
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as expense
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id
                AND t.user_id = %s
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING income > 0 OR expense > 0
            ORDER BY (income + expense) DESC
        """
        
        results = db.execute_query(fixed_query, (user_id, current_month))
        
        if results:
            print(f"   ✅ تم العثور على {len(results)} عملة مع معاملات:")
            for result in results:
                print(f"      - {result['code']} ({result['name']}):")
                print(f"        📈 واردات: {result['income']:,.2f} {result['symbol']}")
                print(f"        📉 مصروفات: {result['expense']:,.2f} {result['symbol']}")
                print(f"        💰 الصافي: {(result['income'] - result['expense']):,.2f} {result['symbol']}")
        else:
            print("   ⚠️ لا توجد معاملات في الشهر الحالي")
        
        # 2. اختبار دالة get_monthly_statistics_by_currency من الواجهة
        print("\n2️⃣ اختبار دالة get_monthly_statistics_by_currency:")
        
        # محاكاة استدعاء الدالة من الواجهة
        try:
            # استيراد الكلاس الرئيسي
            from gui.main_window import MoneyManagerApp
            
            # إنشاء كائن مؤقت للاختبار
            app = MoneyManagerApp()
            
            # استدعاء الدالة
            monthly_stats = app.get_monthly_statistics_by_currency()
            
            if monthly_stats:
                print(f"   ✅ دالة الواجهة تعمل بشكل صحيح: {len(monthly_stats)} عملة")
                for stat in monthly_stats:
                    print(f"      - {stat['code']}: واردات {stat['income']:,.2f} - مصروفات {stat['expense']:,.2f}")
            else:
                print("   ⚠️ دالة الواجهة لا تُرجع نتائج")
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار دالة الواجهة: {e}")
        
        # 3. إضافة معاملة تجريبية إذا لم توجد معاملات
        if not results:
            print("\n3️⃣ إضافة معاملة تجريبية للشهر الحالي:")
            
            from database.models import Transaction, Account
            
            # الحصول على أول حساب
            accounts = Account.get_by_user(user_id)
            if accounts:
                account_id = accounts[0]['id']
                
                # إضافة معاملة وارد
                income_id = Transaction.create(
                    user_id=user_id,
                    account_id=account_id,
                    currency_id=1,  # SAR
                    transaction_type='income',
                    amount=2000.00,
                    description=f"راتب شهري - {datetime.now().strftime('%B %Y')}",
                    transaction_date=datetime.now().date()
                )
                
                # إضافة معاملة صادر
                expense_id = Transaction.create(
                    user_id=user_id,
                    account_id=account_id,
                    currency_id=1,  # SAR
                    transaction_type='expense',
                    amount=800.00,
                    description=f"مصروفات شهرية - {datetime.now().strftime('%B %Y')}",
                    transaction_date=datetime.now().date()
                )
                
                if income_id > 0 and expense_id > 0:
                    print(f"   ✅ تم إضافة معاملات تجريبية: وارد {income_id}, صادر {expense_id}")
                    
                    # إعادة اختبار الاستعلام
                    print("\n   🔄 إعادة اختبار الاستعلام بعد إضافة المعاملات:")
                    new_results = db.execute_query(fixed_query, (user_id, current_month))
                    
                    if new_results:
                        print(f"   ✅ الآن يوجد {len(new_results)} عملة مع معاملات:")
                        for result in new_results:
                            print(f"      - {result['code']}: واردات {result['income']:,.2f} - مصروفات {result['expense']:,.2f}")
                    else:
                        print("   ❌ ما زال لا توجد نتائج!")
                else:
                    print("   ❌ فشل في إضافة المعاملات التجريبية")
            else:
                print("   ❌ لا توجد حسابات للمستخدم")
        
        # تسجيل الخروج
        auth_manager.logout()
        
        return len(results) > 0 if results else False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dashboard_stats_function():
    """اختبار دالة get_dashboard_stats المحسنة"""
    try:
        print("\n📊 اختبار دالة get_dashboard_stats المحسنة...")
        print("-" * 50)
        
        from utils.auth import auth_manager
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        try:
            from gui.main_window import MoneyManagerApp
            
            # إنشاء كائن التطبيق
            app = MoneyManagerApp()
            
            # استدعاء دالة get_dashboard_stats
            stats = app.get_dashboard_stats()
            
            print(f"📋 نتائج get_dashboard_stats:")
            print(f"   - currency_balances: {len(stats.get('currency_balances', []))} عملة")
            print(f"   - currency_transactions: {len(stats.get('currency_transactions', []))} معاملة")
            print(f"   - accounts_count: {stats.get('accounts_count', 0)} حساب")
            
            if stats.get('currency_transactions'):
                print(f"\n   📈 تفاصيل المعاملات:")
                for trans in stats['currency_transactions']:
                    type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
                    print(f"      - {trans['code']} {type_text}: {trans['total_amount']:,.2f} {trans['symbol']}")
            
            # تسجيل الخروج
            auth_manager.logout()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار دالة get_dashboard_stats: {e}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة المعاملات الشهرية")
    print("=" * 60)
    
    # اختبار عرض المعاملات الشهرية
    monthly_test_ok = test_monthly_transactions_display()
    
    # اختبار دالة لوحة التحكم
    dashboard_test_ok = test_dashboard_stats_function()
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print(f"   المعاملات الشهرية: {'✅' if monthly_test_ok else '❌'}")
    print(f"   دالة لوحة التحكم: {'✅' if dashboard_test_ok else '❌'}")
    
    if monthly_test_ok and dashboard_test_ok:
        print("\n🎉 تم إصلاح مشكلة المعاملات الشهرية بنجاح!")
        print("✅ لوحة التحكم ستعرض المعاملات الشهرية بشكل صحيح الآن")
        print("🔄 أعد تشغيل التطبيق لرؤية التحسينات")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى مراجعة إضافية")
        
        if not monthly_test_ok:
            print("   - تحقق من وجود معاملات في الشهر الحالي")
        if not dashboard_test_ok:
            print("   - تحقق من دالة get_dashboard_stats")

if __name__ == "__main__":
    main()
