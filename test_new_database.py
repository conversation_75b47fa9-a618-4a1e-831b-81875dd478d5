#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات الجديدة
"""

import mysql.connector
from mysql.connector import Error

def test_database():
    """اختبار قاعدة البيانات الجديدة"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        print("🧪 اختبار قاعدة البيانات الجديدة...")
        print("=" * 50)
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # اختبار الاتصال
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print("✅ اختبار الاتصال: نجح")
        
        # اختبار المستخدم
        cursor.execute("SELECT * FROM users WHERE username = 'admin'")
        user = cursor.fetchone()
        if user:
            print(f"✅ اختبار المستخدم: نجح - {user['full_name']}")
        else:
            print("❌ اختبار المستخدم: فشل")
            return False
        
        # اختبار العملات
        cursor.execute("SELECT COUNT(*) as count FROM currencies")
        currencies_count = cursor.fetchone()['count']
        if currencies_count >= 4:
            print(f"✅ اختبار العملات: نجح ({currencies_count} عملة)")
        else:
            print(f"❌ اختبار العملات: فشل ({currencies_count} عملة)")
            return False
        
        # عرض العملات
        cursor.execute("SELECT code, name, symbol FROM currencies")
        currencies = cursor.fetchall()
        print("   العملات المتاحة:")
        for currency in currencies:
            print(f"     - {currency['code']}: {currency['name']} ({currency['symbol']})")
        
        # اختبار الحسابات
        cursor.execute("SELECT COUNT(*) as count FROM accounts")
        accounts_count = cursor.fetchone()['count']
        if accounts_count >= 5:
            print(f"✅ اختبار الحسابات: نجح ({accounts_count} حساب)")
        else:
            print(f"❌ اختبار الحسابات: فشل ({accounts_count} حساب)")
            return False
        
        # عرض الحسابات
        cursor.execute("""
            SELECT a.name, c.code as currency, a.balance 
            FROM accounts a 
            JOIN currencies c ON a.currency_id = c.id
        """)
        accounts = cursor.fetchall()
        print("   الحسابات المتاحة:")
        for account in accounts:
            print(f"     - {account['name']}: {account['balance']} {account['currency']}")
        
        # اختبار المعاملات
        cursor.execute("SELECT COUNT(*) as count FROM transactions")
        transactions_count = cursor.fetchone()['count']
        if transactions_count >= 20:
            print(f"✅ اختبار المعاملات: نجح ({transactions_count} معاملة)")
        else:
            print(f"❌ اختبار المعاملات: فشل ({transactions_count} معاملة)")
            return False
        
        # عرض آخر المعاملات
        cursor.execute("""
            SELECT t.type, t.amount, c.code as currency, t.description, t.transaction_date
            FROM transactions t
            JOIN currencies c ON t.currency_id = c.id
            ORDER BY t.created_at DESC
            LIMIT 5
        """)
        transactions = cursor.fetchall()
        print("   آخر المعاملات:")
        for trans in transactions:
            trans_type = "وارد" if trans['type'] == 'income' else "صادر"
            print(f"     - {trans_type}: {trans['amount']} {trans['currency']} - {trans['description']}")
        
        # اختبار التصنيفات
        cursor.execute("SELECT COUNT(*) as count FROM income_categories")
        income_cats = cursor.fetchone()['count']
        
        cursor.execute("SELECT COUNT(*) as count FROM expense_categories")
        expense_cats = cursor.fetchone()['count']
        
        print(f"✅ تصنيفات الواردات: {income_cats}")
        print(f"✅ تصنيفات المصروفات: {expense_cats}")
        
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 50)
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ قاعدة البيانات جاهزة للاستخدام")
        print("\n📝 معلومات تسجيل الدخول:")
        print("   المستخدم: admin")
        print("   كلمة المرور: 123456")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    test_database()
