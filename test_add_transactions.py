#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة المعاملات الجديدة (واردات ومصروفات)
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Transaction, Account
from utils.auth import auth_manager
from datetime import datetime, date

def test_add_transactions():
    """اختبار إضافة المعاملات"""
    print("🧪 اختبار إضافة المعاملات الجديدة")
    print("=" * 50)
    
    # تسجيل دخول تلقائي
    users = db.execute_query("SELECT * FROM users LIMIT 1")
    if users:
        auth_manager.current_user = users[0]
        user_id = users[0]['id']
        print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
    else:
        print("❌ لا يوجد مستخدمين")
        return
    
    # الحصول على الحسابات المتاحة
    accounts = db.execute_query(
        "SELECT id, name, currency_id FROM accounts WHERE user_id = %s AND is_active = TRUE LIMIT 3", 
        (user_id,)
    )
    
    if not accounts:
        print("❌ لا توجد حسابات متاحة للاختبار")
        return
    
    print(f"📋 وجدت {len(accounts)} حساب للاختبار:")
    for account in accounts:
        print(f"   - {account['name']} (ID: {account['id']})")
    
    # اختبار إضافة وارد
    print("\n💰 اختبار إضافة وارد جديد...")
    test_income_transaction(accounts[0])
    
    # اختبار إضافة مصروف
    print("\n💸 اختبار إضافة مصروف جديد...")
    test_expense_transaction(accounts[0])
    
    # عرض النتائج النهائية
    print("\n📊 النتائج النهائية:")
    show_final_results(user_id)

def test_income_transaction(account):
    """اختبار إضافة معاملة وارد"""
    try:
        user_id = auth_manager.current_user['id']
        
        # بيانات الوارد التجريبي
        test_data = {
            'user_id': user_id,
            'account_id': account['id'],
            'currency_id': account['currency_id'],
            'transaction_type': 'income',
            'amount': 5000.0,
            'description': 'اختبار وارد تلقائي',
            'transaction_date': date.today()
        }
        
        print(f"🔄 إضافة وارد للحساب: {account['name']}")
        print(f"   المبلغ: {test_data['amount']}")
        print(f"   الوصف: {test_data['description']}")
        
        # الحصول على الرصيد قبل المعاملة
        old_balance = get_account_balance(account['id'], account['currency_id'])
        print(f"   الرصيد قبل المعاملة: {old_balance}")
        
        # إنشاء المعاملة
        result = Transaction.create(**test_data)
        
        if result > 0:
            print(f"✅ تم إنشاء الوارد بنجاح (ID: {result})")
            
            # التحقق من تحديث الرصيد
            new_balance = get_account_balance(account['id'], account['currency_id'])
            expected_balance = float(old_balance) + test_data['amount']
            
            print(f"   الرصيد بعد المعاملة: {new_balance}")
            print(f"   الرصيد المتوقع: {expected_balance}")
            
            if abs(float(new_balance) - expected_balance) < 0.01:
                print("✅ تم تحديث الرصيد بشكل صحيح")
            else:
                print("❌ خطأ في تحديث الرصيد")
        else:
            print("❌ فشل في إنشاء الوارد")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الوارد: {e}")
        import traceback
        traceback.print_exc()

def test_expense_transaction(account):
    """اختبار إضافة معاملة مصروف"""
    try:
        user_id = auth_manager.current_user['id']
        
        # بيانات المصروف التجريبي
        test_data = {
            'user_id': user_id,
            'account_id': account['id'],
            'currency_id': account['currency_id'],
            'transaction_type': 'expense',
            'amount': 2000.0,
            'description': 'اختبار مصروف تلقائي',
            'transaction_date': date.today()
        }
        
        print(f"🔄 إضافة مصروف للحساب: {account['name']}")
        print(f"   المبلغ: {test_data['amount']}")
        print(f"   الوصف: {test_data['description']}")
        
        # الحصول على الرصيد قبل المعاملة
        old_balance = get_account_balance(account['id'], account['currency_id'])
        print(f"   الرصيد قبل المعاملة: {old_balance}")
        
        # إنشاء المعاملة
        result = Transaction.create(**test_data)
        
        if result > 0:
            print(f"✅ تم إنشاء المصروف بنجاح (ID: {result})")
            
            # التحقق من تحديث الرصيد
            new_balance = get_account_balance(account['id'], account['currency_id'])
            expected_balance = float(old_balance) - test_data['amount']
            
            print(f"   الرصيد بعد المعاملة: {new_balance}")
            print(f"   الرصيد المتوقع: {expected_balance}")
            
            if abs(float(new_balance) - expected_balance) < 0.01:
                print("✅ تم خصم الرصيد بشكل صحيح")
            else:
                print("❌ خطأ في خصم الرصيد")
        else:
            print("❌ فشل في إنشاء المصروف")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المصروف: {e}")
        import traceback
        traceback.print_exc()

def get_account_balance(account_id, currency_id):
    """الحصول على رصيد الحساب لعملة معينة"""
    try:
        balance_query = """
            SELECT balance FROM account_balances 
            WHERE account_id = %s AND currency_id = %s
        """
        result = db.execute_query(balance_query, (account_id, currency_id))
        return result[0]['balance'] if result else 0.0
    except Exception as e:
        print(f"خطأ في الحصول على الرصيد: {e}")
        return 0.0

def show_final_results(user_id):
    """عرض النتائج النهائية"""
    try:
        # عدد المعاملات الجديدة
        today = date.today()
        new_transactions = db.execute_query("""
            SELECT transaction_type, COUNT(*) as count, SUM(amount) as total
            FROM transactions 
            WHERE user_id = %s AND DATE(created_at) = %s
            GROUP BY transaction_type
        """, (user_id, today))
        
        print("📈 المعاملات المُضافة اليوم:")
        if new_transactions:
            for trans in new_transactions:
                type_name = "واردات" if trans['transaction_type'] == 'income' else "مصروفات"
                print(f"   - {type_name}: {trans['count']} معاملة بإجمالي {trans['total']:,.2f}")
        else:
            print("   - لا توجد معاملات جديدة")
        
        # إجمالي المعاملات
        total_transactions = db.execute_query(
            "SELECT COUNT(*) as count FROM transactions WHERE user_id = %s", 
            (user_id,)
        )
        total_count = total_transactions[0]['count'] if total_transactions else 0
        print(f"📊 إجمالي المعاملات: {total_count}")
        
    except Exception as e:
        print(f"خطأ في عرض النتائج: {e}")

def test_transaction_create_function():
    """اختبار دالة Transaction.create مباشرة"""
    print("\n🔧 اختبار دالة Transaction.create مباشرة...")
    
    try:
        # تسجيل دخول
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if users:
            auth_manager.current_user = users[0]
            user_id = users[0]['id']
        else:
            print("❌ لا يوجد مستخدمين")
            return
        
        # الحصول على حساب للاختبار
        accounts = db.execute_query(
            "SELECT id, currency_id FROM accounts WHERE user_id = %s LIMIT 1", 
            (user_id,)
        )
        
        if not accounts:
            print("❌ لا توجد حسابات")
            return
        
        account = accounts[0]
        
        # اختبار التوقيع الجديد للدالة
        test_params = {
            'user_id': user_id,
            'account_id': account['id'],
            'currency_id': account['currency_id'],
            'transaction_type': 'income',
            'amount': 1000.0,
            'description': 'اختبار دالة create',
            'transaction_date': date.today()
        }
        
        print("🔄 اختبار التوقيع الجديد...")
        print(f"   المعاملات: {test_params}")
        
        result = Transaction.create(**test_params)
        
        if result > 0:
            print(f"✅ دالة Transaction.create تعمل بشكل صحيح (ID: {result})")
        else:
            print("❌ دالة Transaction.create لا تعمل")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الدالة: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    try:
        # اختبار دالة Transaction.create أولاً
        test_transaction_create_function()
        
        # اختبار إضافة المعاملات
        test_add_transactions()
        
        print("\n" + "=" * 50)
        print("🎉 انتهى اختبار إضافة المعاملات!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
