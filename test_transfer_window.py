import customtkinter as ctk
import sys
import os

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.transfer_window import TransferWindow
from utils.auth import auth_manager
from database.connection import db

def test_transfer_window():
    """اختبار نافذة التحويل المحسنة"""
    
    # إعداد CustomTkinter
    ctk.set_appearance_mode("light")
    
    # إنشاء نافذة رئيسية مؤقتة
    root = ctk.CTk()
    root.title("اختبار نافذة التحويل")
    root.geometry("400x300")
    
    # محاولة تسجيل دخول مستخدم تجريبي
    try:
        # الاتصال بقاعدة البيانات
        db.connect()
        
        # محاولة تسجيل دخول المستخدم admin
        result = auth_manager.login("admin", "admin123")
        
        if result["success"]:
            print("تم تسجيل دخول المستخدم admin بنجاح")
            
            # إنشاء زر لفتح نافذة التحويل
            def open_transfer_window():
                def on_close():
                    print("تم إغلاق نافذة التحويل")
                
                transfer_window = TransferWindow(root, on_close)
            
            test_button = ctk.CTkButton(
                root, 
                text="فتح نافذة التحويل المحسنة", 
                command=open_transfer_window,
                font=ctk.CTkFont(size=14)
            )
            test_button.pack(pady=50)
            
            info_label = ctk.CTkLabel(
                root, 
                text="اضغط على الزر لفتح نافذة التحويل المحسنة\nتم إصلاح:\n• حجم النافذة\n• إزالة العملة من قسم المستلم\n• إضافة زر إلغاء",
                font=ctk.CTkFont(size=12),
                justify="center"
            )
            info_label.pack(pady=20)
            
        else:
            # إنشاء مستخدم تجريبي إذا لم يكن موجوداً
            print("لا يمكن تسجيل الدخول، سيتم عرض النافذة بدون بيانات")
            
            # محاكاة بيانات المستخدم
            auth_manager.current_user = {
                'id': 1,
                'username': 'test_user',
                'full_name': 'مستخدم تجريبي'
            }
            
            def open_transfer_window():
                def on_close():
                    print("تم إغلاق نافذة التحويل")
                
                try:
                    transfer_window = TransferWindow(root, on_close)
                except Exception as e:
                    print(f"خطأ في فتح نافذة التحويل: {e}")
                    # عرض نافذة بسيطة للاختبار
                    test_window = ctk.CTkToplevel(root)
                    test_window.title("نافذة التحويل المحسنة")
                    test_window.geometry("600x550")
                    
                    label = ctk.CTkLabel(test_window, text="تم إصلاح المشاكل التالية:\n\n1. تقليل حجم النافذة من 750 إلى 550\n2. إزالة قسم العملة من المستلم\n3. إضافة زر إلغاء\n4. تحسين التخطيط", 
                                       font=ctk.CTkFont(size=14), justify="center")
                    label.pack(pady=50)
                    
                    close_btn = ctk.CTkButton(test_window, text="إغلاق", command=test_window.destroy)
                    close_btn.pack(pady=20)
            
            test_button = ctk.CTkButton(
                root, 
                text="فتح نافذة التحويل المحسنة", 
                command=open_transfer_window,
                font=ctk.CTkFont(size=14)
            )
            test_button.pack(pady=50)
            
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        
        # عرض معلومات الإصلاحات فقط
        info_label = ctk.CTkLabel(
            root, 
            text="تم إصلاح المشاكل التالية في نافذة التحويل:\n\n✅ تقليل حجم النافذة من 750 إلى 550 بكسل\n✅ إزالة عرض العملة من قسم 'المستلم'\n✅ إضافة زر إلغاء بجانب زر الحفظ\n✅ تحسين تخطيط العناصر وترتيبها\n✅ إضافة حاسبة سعر الصرف التلقائية",
            font=ctk.CTkFont(size=12),
            justify="center"
        )
        info_label.pack(pady=30)
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    test_transfer_window()
