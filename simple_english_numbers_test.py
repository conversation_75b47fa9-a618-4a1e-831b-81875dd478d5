#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لتنسيق الأرقام الإنجليزية
"""

import os
import sys
import locale

# إعداد الأرقام الإنجليزية
def setup_english_numbers():
    """إعداد الأرقام الإنجليزية"""
    try:
        os.environ['LC_ALL'] = 'C'
        os.environ['LC_NUMERIC'] = 'C'
        locale.setlocale(locale.LC_ALL, 'C')
        print("✅ تم إعداد الأرقام الإنجليزية")
        return True
    except Exception as e:
        print(f"⚠️ تحذير في إعداد الأرقام: {e}")
        return False

def convert_arabic_to_english(text):
    """تحويل الأرقام العربية إلى إنجليزية"""
    if not isinstance(text, str):
        text = str(text)
    
    conversion_map = {
        '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
        '۰': '0', '۱': '1', '۲': '2', '۳': '3', '۴': '4',
        '۵': '5', '۶': '6', '۷': '7', '۸': '8', '۹': '9',
        '،': ',', '٫': '.'
    }
    
    for arabic, english in conversion_map.items():
        text = text.replace(arabic, english)
    
    return text

def format_number_english(number, decimal_places=2):
    """تنسيق الأرقام بالإنجليزية فقط"""
    try:
        # تحويل إلى نص أولاً للمعالجة
        if isinstance(number, str):
            number = convert_arabic_to_english(number)
            import re
            number = re.sub(r'[^\d.-]', '', number)
        
        # تحويل إلى float
        num = float(number)
        
        # تنسيق الرقم
        if decimal_places == 0:
            formatted = f"{num:,.0f}"
        else:
            formatted = f"{num:,.{decimal_places}f}"
        
        # تحويل أي أرقام عربية
        formatted = convert_arabic_to_english(formatted)
        
        # فرض استخدام الفاصلة والنقطة الإنجليزية
        formatted = formatted.replace('،', ',').replace('٫', '.')
        
        return formatted
        
    except (ValueError, TypeError):
        return "0.00" if decimal_places > 0 else "0"

def test_number_formatting():
    """اختبار تنسيق الأرقام"""
    print("🧪 اختبار تنسيق الأرقام...")
    
    # أرقام اختبار
    test_numbers = [
        1234.56,
        1000000.789,
        0.12,
        999999.99,
        12345678.123,
        500296960.00,
        110001568050.00,
        -2500.75,
        0.0
    ]
    
    print("   📊 اختبار الأرقام العادية:")
    all_passed = True
    
    for num in test_numbers:
        formatted = format_number_english(num, 2)
        print(f"      {num} → {formatted}")
        
        # فحص وجود أرقام عربية
        arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
        has_arabic = any(digit in formatted for digit in arabic_digits)
        
        if has_arabic:
            print(f"         ❌ يحتوي على أرقام عربية!")
            all_passed = False
        else:
            print(f"         ✅ أرقام إنجليزية فقط")
    
    # اختبار الأرقام العربية
    print("   🔄 اختبار تحويل الأرقام العربية:")
    arabic_test_cases = [
        "١٢٣٤٫٥٦",
        "١٬٠٠٠٬٠٠٠٫٧٨٩",
        "٩٩٩٬٩٩٩٫٩٩",
        "٠٫١٢",
        "١٢٣٤٥٦٧٨٫١٢٣"
    ]
    
    for arabic_num in arabic_test_cases:
        formatted = format_number_english(arabic_num, 2)
        print(f"      {arabic_num} → {formatted}")
        
        has_arabic = any(digit in formatted for digit in arabic_digits)
        if has_arabic:
            print(f"         ❌ لا يزال يحتوي على أرقام عربية!")
            all_passed = False
        else:
            print(f"         ✅ تم التحويل بنجاح")
    
    return all_passed

def test_currency_formatting():
    """اختبار تنسيق العملات"""
    print("\n💰 اختبار تنسيق العملات...")
    
    # بيانات اختبار العملات
    currency_data = [
        {'amount': 500296960.00, 'symbol': 'ر.س', 'code': 'SAR'},
        {'amount': 1000051000.00, 'symbol': '$', 'code': 'USD'},
        {'amount': 110001568050.00, 'symbol': 'ر.ي', 'code': 'YER'},
        {'amount': 146749.96, 'symbol': 'د.إ', 'code': 'AED'}
    ]
    
    all_passed = True
    
    for data in currency_data:
        amount_formatted = format_number_english(data['amount'], 2)
        currency_text = f"{amount_formatted} {data['symbol']}"
        
        print(f"   {data['code']}: {currency_text}")
        
        # فحص وجود أرقام عربية في المبلغ
        arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
        has_arabic = any(digit in amount_formatted for digit in arabic_digits)
        
        if has_arabic:
            print(f"      ❌ يحتوي على أرقام عربية!")
            all_passed = False
        else:
            print(f"      ✅ تنسيق صحيح")
    
    return all_passed

def test_transaction_formatting():
    """اختبار تنسيق المعاملات"""
    print("\n📈 اختبار تنسيق المعاملات...")
    
    # بيانات اختبار المعاملات
    transaction_data = [
        {'income': 10000042500.00, 'expense': 0.00, 'symbol': 'ر.ي', 'code': 'YER'},
        {'income': 101000.00, 'expense': 200.00, 'symbol': 'د.إ', 'code': 'AED'},
        {'income': 13500.00, 'expense': 7845.00, 'symbol': 'ر.س', 'code': 'SAR'}
    ]
    
    all_passed = True
    
    for data in transaction_data:
        income_text = format_number_english(data['income'], 2)
        expense_text = format_number_english(data['expense'], 2)
        net = data['income'] - data['expense']
        net_text = format_number_english(net, 2)
        net_prefix = "+" if net >= 0 else ""
        
        transaction_text = f"{data['code']}: واردات {income_text} - مصروفات {expense_text} = صافي {net_prefix}{net_text} {data['symbol']}"
        print(f"   {transaction_text}")
        
        # فحص وجود أرقام عربية
        arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
        for text, label in [(income_text, "الواردات"), (expense_text, "المصروفات"), (net_text, "الصافي")]:
            has_arabic = any(digit in text for digit in arabic_digits)
            if has_arabic:
                print(f"      ❌ أرقام عربية في {label}: {text}")
                all_passed = False
            else:
                print(f"      ✅ تنسيق صحيح في {label}")
    
    return all_passed

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔢 اختبار مبسط لتنسيق الأرقام الإنجليزية")
    print("=" * 60)
    
    # إعداد الأرقام الإنجليزية
    setup_english_numbers()
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار 1: تنسيق الأرقام
    if test_number_formatting():
        tests_passed += 1
        print("✅ اختبار تنسيق الأرقام نجح")
    else:
        print("❌ اختبار تنسيق الأرقام فشل")
    
    # اختبار 2: تنسيق العملات
    if test_currency_formatting():
        tests_passed += 1
        print("✅ اختبار تنسيق العملات نجح")
    else:
        print("❌ اختبار تنسيق العملات فشل")
    
    # اختبار 3: تنسيق المعاملات
    if test_transaction_formatting():
        tests_passed += 1
        print("✅ اختبار تنسيق المعاملات نجح")
    else:
        print("❌ اختبار تنسيق المعاملات فشل")
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {tests_passed}/{total_tests} اختبارات")
    
    if tests_passed == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! الأرقام تظهر بالتنسيق الإنجليزي")
        print("📋 النتائج:")
        print("   • جميع الأرقام تستخدم التنسيق الإنجليزي (1,234.56)")
        print("   • لا توجد أرقام عربية في العرض")
        print("   • الفواصل الإنجليزية (,) والنقاط العشرية (.) فقط")
        print("   • رموز العملات العربية محفوظة")
        return True
    else:
        print(f"\n❌ فشل {total_tests - tests_passed} اختبارات")
        return False

if __name__ == "__main__":
    main()
