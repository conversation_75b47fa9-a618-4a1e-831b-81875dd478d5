#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح RTL في حقول الوصف
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from config.fonts import create_rtl_textbox, create_rtl_entry, create_rtl_label
from config.colors import COLORS
from config.styles import ARABIC_TEXT_STYLES

class RTLDescriptionTester:
    """اختبار إصلاح RTL في حقول الوصف"""
    
    def __init__(self):
        # إنشاء النافذة الرئيسية
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاح RTL في حقول الوصف")
        self.window.geometry("800x600")
        self.window.configure(fg_color=COLORS['bg_primary'])
        
        self.create_test_interface()
        
        # تشغيل النافذة
        self.window.mainloop()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # عنوان الاختبار
        title_label = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاح RTL في حقول الوصف",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))
        
        # معلومات الاختبار
        info_label = create_rtl_label(
            self.window,
            text="اختبار عرض النص العربي في حقول الوصف بشكل صحيح (RTL)",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        info_label.pack(pady=(0, 20))
        
        # إطار المقارنة
        comparison_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        comparison_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # عنوان المقارنة
        comparison_title = create_rtl_label(
            comparison_frame,
            text="مقارنة بين الطريقة القديمة والجديدة:",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        comparison_title.pack(pady=(15, 10))
        
        # إطار الطريقة القديمة
        old_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_secondary'])
        old_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        old_title = create_rtl_label(
            old_frame,
            text="❌ الطريقة القديمة (CTkTextbox عادي):",
            font_size='body',
            text_color=COLORS['error'],
            **ARABIC_TEXT_STYLES['label']
        )
        old_title.pack(pady=(10, 5), anchor="w")
        
        # textbox عادي (الطريقة القديمة)
        old_textbox = ctk.CTkTextbox(
            old_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        old_textbox.pack(fill="x", padx=10, pady=(0, 10))
        old_textbox.insert("1.0", "هذا نص عربي تجريبي لاختبار اتجاه النص في حقل الوصف القديم")
        
        # إطار الطريقة الجديدة
        new_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_secondary'])
        new_frame.pack(fill="x", padx=15, pady=(0, 10))
        
        new_title = create_rtl_label(
            new_frame,
            text="✅ الطريقة الجديدة (create_rtl_textbox):",
            font_size='body',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['label']
        )
        new_title.pack(pady=(10, 5), anchor="w")
        
        # textbox مع RTL (الطريقة الجديدة)
        new_textbox = create_rtl_textbox(
            new_frame,
            height=80
        )
        new_textbox.pack(fill="x", padx=10, pady=(0, 10))
        new_textbox.insert("1.0", "هذا نص عربي تجريبي لاختبار اتجاه النص في حقل الوصف الجديد")
        
        # إطار الاختبار التفاعلي
        interactive_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_secondary'])
        interactive_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        interactive_title = create_rtl_label(
            interactive_frame,
            text="🖊️ اختبار تفاعلي - اكتب نص عربي:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        interactive_title.pack(pady=(10, 5), anchor="w")
        
        # textbox للاختبار التفاعلي
        self.test_textbox = create_rtl_textbox(
            interactive_frame,
            height=100
        )
        self.test_textbox.pack(fill="x", padx=10, pady=(0, 10))
        self.test_textbox.insert("1.0", "اكتب هنا نص عربي لاختبار اتجاه النص...")
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(comparison_frame, fg_color="transparent")
        buttons_frame.pack(pady=(0, 15))
        
        # زر اختبار نافذة إضافة حساب
        add_account_btn = ctk.CTkButton(
            buttons_frame,
            text="🏦 اختبار نافذة إضافة حساب",
            command=self.test_add_account_dialog,
            font=ctk.CTkFont(size=14),
            height=40
        )
        add_account_btn.pack(side="left", padx=5)
        
        # زر اختبار نافذة تعديل حساب
        edit_account_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ اختبار نافذة تعديل حساب",
            command=self.test_edit_account_dialog,
            font=ctk.CTkFont(size=14),
            height=40
        )
        edit_account_btn.pack(side="left", padx=5)
        
        # زر مسح النص
        clear_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ مسح النص",
            command=self.clear_test_text,
            font=ctk.CTkFont(size=14),
            height=40
        )
        clear_btn.pack(side="left", padx=5)
        
        # إطار النتائج
        results_frame = ctk.CTkFrame(comparison_frame, fg_color=COLORS['bg_secondary'])
        results_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        results_title = create_rtl_label(
            results_frame,
            text="📊 نتائج الاختبار:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        results_title.pack(pady=(10, 5), anchor="w")
        
        self.results_text = ctk.CTkTextbox(
            results_frame,
            height=100,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="x", padx=10, pady=(0, 10))
        
        # رسالة ترحيبية
        self.log_result("🎯 جاهز لاختبار إصلاح RTL في حقول الوصف!")
        self.log_result("📝 قارن بين الطريقة القديمة والجديدة أعلاه")
        self.log_result("🖊️ اكتب نص عربي في الحقل التفاعلي لاختبار الاتجاه")
    
    def log_result(self, message):
        """إضافة رسالة إلى منطقة النتائج"""
        self.results_text.insert("end", f"{message}\n")
        self.results_text.see("end")
        self.window.update()
    
    def clear_test_text(self):
        """مسح النص من الحقل التفاعلي"""
        self.test_textbox.delete("1.0", "end")
        self.log_result("🗑️ تم مسح النص من الحقل التفاعلي")
    
    def test_add_account_dialog(self):
        """اختبار نافذة إضافة حساب (محاكاة)"""
        self.log_result("🏦 اختبار نافذة إضافة حساب...")
        
        # إنشاء نافذة محاكاة
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("اختبار - إضافة حساب جديد")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        
        # عنوان
        title = create_rtl_label(
            dialog,
            text="اختبار حقل الوصف في نافذة إضافة حساب",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(20, 15))
        
        # حقل الوصف المُصلح
        desc_label = create_rtl_label(
            dialog,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        desc_entry = create_rtl_textbox(
            dialog,
            height=80
        )
        desc_entry.pack(fill="x", padx=20, pady=(0, 15))
        desc_entry.insert("1.0", "هذا وصف تجريبي للحساب الجديد باللغة العربية")
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            dialog,
            text="إغلاق",
            command=dialog.destroy,
            height=35
        )
        close_btn.pack(pady=15)
        
        self.log_result("✅ تم فتح نافذة اختبار إضافة حساب")
    
    def test_edit_account_dialog(self):
        """اختبار نافذة تعديل حساب (محاكاة)"""
        self.log_result("✏️ اختبار نافذة تعديل حساب...")
        
        # إنشاء نافذة محاكاة
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("اختبار - تعديل حساب")
        dialog.geometry("400x300")
        dialog.transient(self.window)
        
        # عنوان
        title = create_rtl_label(
            dialog,
            text="اختبار حقل الوصف في نافذة تعديل حساب",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(20, 15))
        
        # حقل الوصف المُصلح
        desc_label = create_rtl_label(
            dialog,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        desc_entry = create_rtl_textbox(
            dialog,
            height=80
        )
        desc_entry.pack(fill="x", padx=20, pady=(0, 15))
        desc_entry.insert("1.0", "وصف الحساب الحالي باللغة العربية - يمكن تعديله")
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            dialog,
            text="إغلاق",
            command=dialog.destroy,
            height=35
        )
        close_btn.pack(pady=15)
        
        self.log_result("✅ تم فتح نافذة اختبار تعديل حساب")

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار إصلاح RTL في حقول الوصف")
    print("=" * 50)
    
    # تشغيل الاختبار
    RTLDescriptionTester()

if __name__ == "__main__":
    main()
