#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تعديل المعاملات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_schema():
    """اختبار مخطط قاعدة البيانات"""
    print("🔍 اختبار مخطط قاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # فحص هيكل جدول transactions
        print("\n📋 هيكل جدول transactions:")
        try:
            columns = db.execute_query("DESCRIBE transactions")
            column_names = [col['Field'] for col in columns]
            
            required_columns = ['id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date']
            
            for col in columns:
                status = "✅" if col['Field'] in required_columns else "📋"
                print(f"   {status} {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
            
            # التحقق من وجود العمود المطلوب
            if 'category_name' in column_names:
                print("\n✅ عمود category_name موجود")
            else:
                print("\n❌ عمود category_name غير موجود - سيتم إضافته")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص هيكل الجدول: {e}")
            return False
        
        # اختبار استعلام البيانات
        print("\n🔍 اختبار استعلام البيانات:")
        try:
            test_query = """
                SELECT t.*, a.name as account_name, c.symbol as currency_symbol,
                       c.name as currency_name, c.id as currency_id,
                       COALESCE(t.category_name, 'بدون تصنيف') as category_name
                FROM transactions t
                JOIN accounts a ON t.account_id = a.id
                JOIN currencies c ON t.currency_id = c.id
                WHERE t.user_id = 1
                LIMIT 1
            """
            result = db.execute_query(test_query)
            if result:
                print("✅ استعلام البيانات يعمل بشكل صحيح")
                print(f"   عدد النتائج: {len(result)}")
                if result:
                    sample = result[0]
                    print(f"   مثال: {sample.get('amount', 'N/A')} {sample.get('currency_symbol', 'N/A')}")
            else:
                print("⚠️ لا توجد بيانات للاختبار")
                
        except Exception as e:
            print(f"❌ خطأ في استعلام البيانات: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_transaction_model():
    """اختبار نموذج Transaction"""
    print("\n🧪 اختبار نموذج Transaction...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        
        # التحقق من وجود دالة update
        if hasattr(Transaction, 'update'):
            print("✅ دالة Transaction.update موجودة")
            
            # فحص توقيع الدالة
            import inspect
            sig = inspect.signature(Transaction.update)
            params = list(sig.parameters.keys())
            print(f"   المعاملات: {', '.join(params)}")
            
            required_params = ['transaction_id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date']
            missing_params = [p for p in required_params if p not in params]
            
            if missing_params:
                print(f"⚠️ معاملات مفقودة: {', '.join(missing_params)}")
            else:
                print("✅ جميع المعاملات المطلوبة موجودة")
                
        else:
            print("❌ دالة Transaction.update غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النموذج: {e}")
        return False

def add_missing_column():
    """إضافة العمود المفقود"""
    print("\n🔧 إضافة عمود category_name...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # إضافة العمود
        db.execute_update("""
            ALTER TABLE transactions 
            ADD COLUMN category_name VARCHAR(100) NULL 
            COMMENT 'اسم التصنيف المدخل يدوياً'
        """)
        print("✅ تم إضافة عمود category_name")
        
        # إنشاء فهرس
        try:
            db.execute_update("CREATE INDEX idx_category_name ON transactions(category_name)")
            print("✅ تم إنشاء فهرس category_name")
        except Exception as e:
            if "already exists" in str(e):
                print("✅ فهرس category_name موجود بالفعل")
            else:
                print(f"⚠️ خطأ في إنشاء الفهرس: {e}")
        
        return True
        
    except Exception as e:
        if "Duplicate column name" in str(e):
            print("✅ عمود category_name موجود بالفعل")
            return True
        else:
            print(f"❌ خطأ في إضافة العمود: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نافذة تعديل المعاملات")
    print("=" * 60)
    
    # 1. اختبار مخطط قاعدة البيانات
    schema_ok = test_database_schema()
    
    if not schema_ok:
        print("\n🔧 محاولة إصلاح مخطط قاعدة البيانات...")
        if add_missing_column():
            print("✅ تم إصلاح مخطط قاعدة البيانات")
            schema_ok = test_database_schema()
        else:
            print("❌ فشل في إصلاح مخطط قاعدة البيانات")
    
    # 2. اختبار نموذج Transaction
    model_ok = test_transaction_model()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    if schema_ok and model_ok:
        print("🎉 جميع الاختبارات نجحت!")
        print("\nيمكنك الآن:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. الذهاب لقسم الواردات أو المصروفات")
        print("3. تجربة ميزة التعديل")
        print("\nإذا لم تظهر الحقول الجديدة، تأكد من:")
        print("- إعادة تشغيل التطبيق")
        print("- التحقق من ملف gui/main_window.py")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print("يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
