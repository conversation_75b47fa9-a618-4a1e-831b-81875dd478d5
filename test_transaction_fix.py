#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح المعاملات
"""

from database.models import Transaction, Account
from datetime import date

def test_transaction_models():
    """اختبار نماذج المعاملات"""
    try:
        print("🧪 اختبار نماذج المعاملات...")
        
        # 1. إنشاء حساب اختبار
        print("1️⃣ إنشاء حساب اختبار...")
        account_id = Account.create(
            user_id=1,
            name="حساب اختبار المعاملات المحدث",
            description="حساب لاختبار المعاملات بعد الإصلاح"
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب - ID: {account_id}")
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        # 2. إضافة رصيد ابتدائي
        print("2️⃣ إضافة رصيد ابتدائي...")
        Account.add_currency_balance(account_id, 1, 1000.0)  # SAR
        print("✅ تم إضافة 1000 ريال سعودي")
        
        # 3. اختبار إضافة وارد
        print("3️⃣ اختبار إضافة وارد...")
        income_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=1,
            transaction_type='income',
            amount=500.0,
            description="راتب شهري",
            transaction_date=date.today()
        )
        
        if income_id > 0:
            print(f"✅ تم إضافة الوارد - ID: {income_id}")
            balance = Account.get_currency_balance(account_id, 1)
            print(f"💰 الرصيد بعد الوارد: {balance} ريال")
        else:
            print("❌ فشل في إضافة الوارد")
            return False
        
        # 4. اختبار إضافة مصروف
        print("4️⃣ اختبار إضافة مصروف...")
        expense_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=1,
            transaction_type='expense',
            amount=200.0,
            description="مصروفات متنوعة",
            transaction_date=date.today()
        )
        
        if expense_id > 0:
            print(f"✅ تم إضافة المصروف - ID: {expense_id}")
            balance = Account.get_currency_balance(account_id, 1)
            print(f"💰 الرصيد بعد المصروف: {balance} ريال")
        else:
            print("❌ فشل في إضافة المصروف")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🔧 اختبار إصلاح المعاملات")
    print("=" * 50)
    
    success = test_transaction_models()
    
    if success:
        print("\n✅ تم إصلاح مشاكل المعاملات!")
        print("🎯 يمكنك الآن استخدام التطبيق لإضافة الواردات والمصروفات")
    else:
        print("\n❌ لا تزال هناك مشاكل تحتاج إصلاح")
