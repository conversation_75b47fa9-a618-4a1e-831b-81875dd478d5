#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة الحسابات مع التسجيل المفصل
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_account_creation_with_gui():
    """اختبار إضافة حساب مع الواجهة والتسجيل المفصل"""
    print("🧪 اختبار إضافة حساب مع الواجهة والتسجيل المفصل...")
    
    try:
        # استيراد المكونات المطلوبة
        from utils.auth import auth_manager
        from gui.main_window import MainWindow
        import customtkinter as ctk
        import tkinter.messagebox as messagebox
        
        # تسجيل الدخول
        print("🔍 تسجيل الدخول...")
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # إنشاء نافذة التطبيق (مخفية)
        root = ctk.CTk()
        root.withdraw()
        
        # إنشاء نافذة إدارة الأموال
        app = MainWindow(root)
        
        # محاكاة عناصر الواجهة
        class MockEntry:
            def __init__(self, value):
                self.value = value
            def get(self):
                return self.value
        
        class MockText:
            def __init__(self, value):
                self.value = value
            def get(self, start, end):
                return self.value
        
        class MockDialog:
            def destroy(self):
                print("🔄 [MOCK] تم إغلاق النافذة")
        
        # إنشاء عناصر وهمية
        mock_dialog = MockDialog()
        mock_name_entry = MockEntry("حساب تجريبي مع التسجيل المفصل")
        mock_desc_entry = MockText("حساب تجريبي للاختبار مع تسجيل مفصل للأخطاء")
        
        # محاكاة حقول العملات
        print("🔍 إعداد حقول العملات...")
        app.currency_entries = {
            1: MockEntry("1000.0"),  # SAR
            2: MockEntry("500.0"),   # AED
            3: MockEntry(""),        # YER - فارغ
            4: MockEntry("200.0")    # USD
        }
        
        print(f"📊 تم إعداد {len(app.currency_entries)} حقل عملة")
        
        # تسجيل رسائل النجاح والخطأ
        success_called = False
        error_called = False
        error_message = ""
        
        original_showinfo = messagebox.showinfo
        original_showerror = messagebox.showerror
        
        def mock_showinfo(title, message):
            nonlocal success_called
            success_called = True
            print(f"✅ [MOCK] رسالة نجاح: {message}")
        
        def mock_showerror(title, message):
            nonlocal error_called, error_message
            error_called = True
            error_message = message
            print(f"❌ [MOCK] رسالة خطأ: {message}")
        
        messagebox.showinfo = mock_showinfo
        messagebox.showerror = mock_showerror
        
        # محاكاة دالة show_accounts
        def mock_show_accounts():
            print("🔄 [MOCK] تم استدعاء show_accounts")
        
        app.show_accounts = mock_show_accounts
        
        # محاكاة دالة refresh_dashboard_if_active
        def mock_refresh_dashboard():
            print("🔄 [MOCK] تم استدعاء refresh_dashboard_if_active")
        
        app.refresh_dashboard_if_active = mock_refresh_dashboard
        
        # اختبار دالة save_new_account_multi_currency المحسنة
        print("\n🔍 اختبار دالة save_new_account_multi_currency المحسنة...")
        print("="*60)
        
        try:
            app.save_new_account_multi_currency(mock_dialog, mock_name_entry, mock_desc_entry)
            
            print("="*60)
            
            if success_called:
                print("✅ تم إنشاء الحساب بنجاح من الواجهة")
                result = True
            elif error_called:
                print(f"❌ فشل في إنشاء الحساب: {error_message}")
                result = False
            else:
                print("⚠️ لم يتم استدعاء أي رسالة")
                result = False
                
        except Exception as e:
            print(f"❌ خطأ في اختبار الواجهة: {e}")
            import traceback
            traceback.print_exc()
            result = False
        
        # استعادة الدوال الأصلية
        messagebox.showinfo = original_showinfo
        messagebox.showerror = original_showerror
        
        # تنظيف
        root.destroy()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار إضافة الحسابات مع التسجيل المفصل")
    print("="*60)
    
    if test_account_creation_with_gui():
        print("\n🎉 الاختبار نجح!")
        print("✅ وظيفة إضافة الحسابات تعمل بشكل صحيح")
        
        print("\n🚀 يمكنك الآن:")
        print("   1. تشغيل التطبيق: python main.py")
        print("   2. تسجيل الدخول: admin / 123456")
        print("   3. إضافة حسابات جديدة")
        print("   4. مراقبة رسائل التسجيل في وحدة التحكم")
        
    else:
        print("\n❌ الاختبار فشل")
        print("💡 راجع رسائل التسجيل أعلاه لتحديد المشكلة")

if __name__ == "__main__":
    main()
