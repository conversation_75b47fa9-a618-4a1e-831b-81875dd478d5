#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح إضافة الحسابات الجديدة
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_account_creation():
    """اختبار إنشاء حساب جديد"""
    try:
        print("🧪 اختبار إنشاء حساب جديد...")
        print("=" * 50)
        
        # استيراد المكونات المطلوبة
        from database.models import Account
        from utils.auth import auth_manager
        from datetime import datetime
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        user_id = auth_manager.current_user['id']
        
        # بيانات الحساب التجريبي
        test_name = f"حساب تجريبي - {datetime.now().strftime('%H:%M:%S')}"
        test_description = "حساب تجريبي للاختبار"
        
        print(f"📝 إنشاء حساب:")
        print(f"   الاسم: {test_name}")
        print(f"   الوصف: {test_description}")
        
        # إنشاء الحساب
        account_id = Account.create(
            user_id=user_id,
            name=test_name,
            description=test_description,
            currency_id=1  # SAR
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب بنجاح! ID: {account_id}")
            
            # إضافة رصيد تجريبي
            result = Account.add_currency_balance(account_id, 1, 1000.00)  # 1000 ريال سعودي
            if result:
                print(f"✅ تم إضافة رصيد ابتدائي: 1000.00 ريال سعودي")
            
            # إضافة رصيد بعملة أخرى
            result2 = Account.add_currency_balance(account_id, 4, 500.00)  # 500 دولار
            if result2:
                print(f"✅ تم إضافة رصيد ابتدائي: 500.00 دولار أمريكي")
            
            # التحقق من الحساب المنشأ
            from database.connection import db
            
            account_query = """
                SELECT a.*, at.name as account_type_name, c.name as currency_name
                FROM accounts a
                LEFT JOIN account_types at ON a.account_type_id = at.id
                LEFT JOIN currencies c ON a.currency_id = c.id
                WHERE a.id = %s
            """
            account_data = db.execute_query(account_query, (account_id,))
            
            if account_data:
                account = account_data[0]
                print(f"\n📊 بيانات الحساب المنشأ:")
                print(f"   ID: {account['id']}")
                print(f"   الاسم: {account['name']}")
                print(f"   النوع: {account['account_type_name']}")
                print(f"   العملة الأساسية: {account['currency_name']}")
                print(f"   الرصيد الأساسي: {account['balance']}")
                print(f"   نشط: {account['is_active']}")
            
            # عرض جميع أرصدة الحساب
            balances_query = """
                SELECT ab.balance, c.code, c.name, c.symbol
                FROM account_balances ab
                JOIN currencies c ON ab.currency_id = c.id
                WHERE ab.account_id = %s AND ab.balance > 0
            """
            balances = db.execute_query(balances_query, (account_id,))
            
            if balances:
                print(f"\n💰 أرصدة الحساب:")
                for balance in balances:
                    print(f"   - {balance['balance']:,.2f} {balance['symbol']} ({balance['name']})")
            
            # حذف الحساب التجريبي
            print(f"\n🗑️ حذف الحساب التجريبي...")
            db.execute_update("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            db.execute_update("DELETE FROM accounts WHERE id = %s", (account_id,))
            print(f"✅ تم حذف الحساب التجريبي")
            
            # تسجيل الخروج
            auth_manager.logout()
            
            return True
        else:
            print(f"❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء الحساب: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_account_creation():
    """اختبار إنشاء حساب من خلال الواجهة"""
    try:
        print("\n🖥️ اختبار إنشاء حساب من خلال الواجهة...")
        print("-" * 50)
        
        # محاكاة إنشاء حساب من الواجهة
        from database.models import Account
        from utils.auth import auth_manager
        from datetime import datetime
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        
        # محاكاة البيانات من الواجهة
        account_name = f"حساب من الواجهة - {datetime.now().strftime('%H:%M:%S')}"
        description = "حساب تم إنشاؤه من خلال الواجهة"
        
        print(f"📝 محاكاة إنشاء حساب من الواجهة:")
        print(f"   الاسم: {account_name}")
        print(f"   الوصف: {description}")
        
        # إنشاء الحساب (كما يحدث في الواجهة)
        account_id = Account.create(
            user_id=user_id,
            name=account_name,
            description=description,
            currency_id=1  # العملة الافتراضية
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب من الواجهة! ID: {account_id}")
            
            # محاكاة إضافة أرصدة متعددة العملات (كما في الواجهة)
            currency_balances = {
                1: 5000.00,  # SAR
                2: 2000.00,  # AED
                4: 1000.00   # USD
            }
            
            balances_added = 0
            for currency_id, balance in currency_balances.items():
                if balance > 0:
                    result = Account.add_currency_balance(account_id, currency_id, balance)
                    if result:
                        balances_added += 1
                        print(f"   ✅ تم إضافة رصيد العملة {currency_id}: {balance:,.2f}")
                    else:
                        print(f"   ❌ فشل في إضافة رصيد العملة {currency_id}")
            
            print(f"📊 تم إضافة {balances_added} عملة من أصل {len(currency_balances)}")
            
            # عرض النتيجة النهائية
            from database.connection import db
            
            final_query = """
                SELECT 
                    a.name,
                    GROUP_CONCAT(
                        CONCAT(ab.balance, ' ', c.symbol) 
                        ORDER BY c.code 
                        SEPARATOR ', '
                    ) as all_balances
                FROM accounts a
                LEFT JOIN account_balances ab ON a.id = ab.account_id AND ab.balance > 0
                LEFT JOIN currencies c ON ab.currency_id = c.id
                WHERE a.id = %s
                GROUP BY a.id, a.name
            """
            
            final_result = db.execute_query(final_query, (account_id,))
            if final_result:
                account_info = final_result[0]
                print(f"\n💰 الحساب النهائي:")
                print(f"   الاسم: {account_info['name']}")
                print(f"   الأرصدة: {account_info['all_balances'] or 'لا توجد أرصدة'}")
            
            # حذف الحساب التجريبي
            print(f"\n🗑️ حذف الحساب التجريبي...")
            db.execute_update("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            db.execute_update("DELETE FROM accounts WHERE id = %s", (account_id,))
            print(f"✅ تم حذف الحساب التجريبي")
            
            # تسجيل الخروج
            auth_manager.logout()
            
            return True
        else:
            print(f"❌ فشل في إنشاء الحساب من الواجهة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لإصلاح إضافة الحسابات")
    print("=" * 60)
    
    # اختبار إنشاء حساب عادي
    test1_ok = test_account_creation()
    
    # اختبار إنشاء حساب من الواجهة
    test2_ok = test_gui_account_creation()
    
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print(f"   إنشاء حساب عادي: {'✅' if test1_ok else '❌'}")
    print(f"   إنشاء حساب من الواجهة: {'✅' if test2_ok else '❌'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ إصلاح إضافة الحسابات مكتمل")
        print("🚀 يمكنك الآن إضافة حسابات جديدة من التطبيق")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى مراجعة")

if __name__ == "__main__":
    main()
