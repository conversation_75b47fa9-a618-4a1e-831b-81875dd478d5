#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("اختبار نموذج Account...")

try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from database.connection import db
    from database.models import Account
    from utils.auth import auth_manager
    
    print("تم استيراد النماذج")
    
    # تسجيل دخول
    users = db.execute_query("SELECT * FROM users LIMIT 1")
    if users:
        auth_manager.current_user = users[0]
        user_id = users[0]['id']
        print(f"المستخدم: {users[0]['username']} (ID: {user_id})")
        
        # اختبار النموذج
        print("اختبار Account.get_by_user()...")
        accounts = Account.get_by_user(user_id)
        
        if accounts:
            print(f"النموذج يُرجع: {len(accounts)} حساب")
            
            for i, account in enumerate(accounts[:3], 1):  # عرض أول 3 حسابات فقط
                print(f"\nالحساب {i}:")
                print(f"  الاسم: {account['name']}")
                print(f"  المعرف: {account['id']}")
                print(f"  نشط: {account.get('is_active')}")
                print(f"  الوصف: {account.get('description', 'لا يوجد')}")
                
                # فحص الأرصدة
                balances = account.get('balances', [])
                print(f"  عدد الأرصدة: {len(balances)}")
                
                if balances:
                    for balance in balances:
                        print(f"    - {balance['balance']} {balance['symbol']} ({balance['name']})")
                else:
                    print("    - لا توجد أرصدة")
        else:
            print("النموذج لا يُرجع أي حسابات")
            
        # اختبار دالة get_balances مباشرة
        print("\nاختبار get_balances للحساب الأول...")
        first_account_id = 7  # معاذ
        balances = Account.get_balances(first_account_id)
        print(f"أرصدة الحساب {first_account_id}: {len(balances) if balances else 0}")
        
        if balances:
            for balance in balances:
                print(f"  - {balance['balance']} {balance['symbol']}")
        
    else:
        print("لا يوجد مستخدمين")

except Exception as e:
    print(f"خطأ: {e}")
    import traceback
    traceback.print_exc()

print("انتهى اختبار النموذج")
