#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إنشاء الحسابات الجديدة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account
from utils.auth import auth_manager

def test_account_creation():
    """اختبار إنشاء حساب جديد"""
    print("🧪 اختبار إنشاء حساب جديد...")
    
    try:
        # التأكد من الاتصال بقاعدة البيانات
        if not db.is_connected():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # التحقق من وجود جدول account_types
        result = db.execute_query("SHOW TABLES LIKE 'account_types'")
        if not result:
            print("❌ جدول account_types غير موجود")
            return False
            
        print("✅ جدول account_types موجود")
        
        # التحقق من هيكل جدول account_types
        columns = db.execute_query("DESCRIBE account_types")
        print("📋 أعمدة جدول account_types:")
        for col in columns:
            print(f"   - {col['Field']}: {col['Type']}")
        
        # التحقق من وجود أنواع حسابات افتراضية
        account_types = db.execute_query("SELECT * FROM account_types")
        print(f"📊 عدد أنواع الحسابات الموجودة: {len(account_types) if account_types else 0}")
        
        if account_types:
            print("🏦 أنواع الحسابات المتاحة:")
            for at in account_types:
                print(f"   - {at['id']}: {at['name']}")
        
        # التحقق من وجود عملات
        currencies = db.execute_query("SELECT * FROM currencies")
        print(f"💱 عدد العملات الموجودة: {len(currencies) if currencies else 0}")
        
        if currencies:
            print("💰 العملات المتاحة:")
            for curr in currencies:
                print(f"   - {curr['id']}: {curr['name']} ({curr['symbol']})")
        
        # محاولة إنشاء حساب تجريبي
        print("\n🔧 محاولة إنشاء حساب تجريبي...")
        
        # استخدام معرف مستخدم افتراضي (1) للاختبار
        test_user_id = 1
        test_account_name = "حساب اختبار"
        test_account_type_id = 1  # نوع الحساب الأول
        test_currency_id = 1      # العملة الأولى
        test_initial_balance = 1000.0
        test_description = "حساب تجريبي للاختبار"
        
        # إنشاء الحساب
        result = Account.create(
            user_id=test_user_id,
            name=test_account_name,
            account_type_id=test_account_type_id,
            currency_id=test_currency_id,
            initial_balance=test_initial_balance,
            description=test_description
        )
        
        if result > 0:
            print(f"✅ تم إنشاء الحساب بنجاح! معرف الحساب: {result}")
            
            # التحقق من الحساب المُنشأ
            created_account = Account.get_by_id(result)
            if created_account:
                print("📋 تفاصيل الحساب المُنشأ:")
                print(f"   - الاسم: {created_account['name']}")
                print(f"   - النوع: {created_account['account_type_name']}")
                print(f"   - العملة: {created_account['currency_name']}")
                print(f"   - الرصيد الابتدائي: {created_account['initial_balance']}")
                print(f"   - الرصيد الحالي: {created_account['current_balance']}")
                
                # حذف الحساب التجريبي
                delete_result = db.execute_update("DELETE FROM accounts WHERE id = %s", (result,))
                if delete_result > 0:
                    print("🗑️ تم حذف الحساب التجريبي بنجاح")
                
                return True
            else:
                print("❌ فشل في استرجاع الحساب المُنشأ")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إنشاء الحسابات...")
    success = test_account_creation()
    
    if success:
        print("\n🎉 نجح الاختبار! إنشاء الحسابات يعمل بشكل صحيح")
    else:
        print("\n💥 فشل الاختبار! هناك مشكلة في إنشاء الحسابات")
    
    print("\n" + "="*50)
