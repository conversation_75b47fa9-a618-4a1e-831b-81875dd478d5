#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتسجيل الدخول
"""

import mysql.connector
import bcrypt

def test_login():
    """اختبار تسجيل الدخول"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        print("🧪 اختبار تسجيل الدخول...")
        print("=" * 40)
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        
        # البحث عن المستخدم admin
        print("🔍 البحث عن المستخدم admin...")
        cursor.execute("""
            SELECT id, username, password_hash, full_name, role, is_active
            FROM users
            WHERE username = %s AND is_active = TRUE
        """, ("admin",))
        
        user = cursor.fetchone()
        
        if user:
            print("✅ تم العثور على المستخدم admin")
            print(f"   ID: {user['id']}")
            print(f"   الاسم الكامل: {user['full_name']}")
            print(f"   الدور: {user['role']}")
            print(f"   نشط: {user['is_active']}")
            
            # اختبار كلمة المرور
            print("\n🔐 اختبار كلمة المرور...")
            password = "123456"
            stored_hash = user['password_hash']
            
            print(f"   كلمة المرور المدخلة: {password}")
            print(f"   نوع التشفير: {'bcrypt' if stored_hash.startswith('$2b$') else 'SHA-256' if len(stored_hash) == 64 else 'غير معروف'}")
            
            try:
                password_bytes = password.encode('utf-8')
                stored_hash_bytes = stored_hash.encode('utf-8')
                
                if bcrypt.checkpw(password_bytes, stored_hash_bytes):
                    print("✅ كلمة المرور صحيحة!")
                    
                    print("\n📝 بيانات تسجيل الدخول الصحيحة:")
                    print("   اسم المستخدم: admin")
                    print("   كلمة المرور: 123456")
                    print("\n🎉 يمكنك الآن تسجيل الدخول في التطبيق!")
                    
                    return True
                else:
                    print("❌ كلمة المرور غير صحيحة")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في التحقق من كلمة المرور: {e}")
                return False
        else:
            print("❌ لم يتم العثور على المستخدم admin")
            return False
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_login()
