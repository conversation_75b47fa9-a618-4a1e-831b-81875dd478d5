#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق الأرقام في قسم التقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_number_formatting_in_reports():
    """اختبار تنسيق الأرقام في قسم التقارير"""
    try:
        print("🧪 اختبار تنسيق الأرقام في قسم التقارير")
        print("=" * 60)
        
        # تهيئة قاعدة البيانات
        from database.connection import db
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # اختبار 1: فحص تنسيق الأرقام في الملخص المالي
        print("\n🔍 اختبار 1: تنسيق الأرقام في الملخص المالي...")
        
        # محاكاة دالة الملخص المالي
        from datetime import datetime
        current_month = datetime.now().strftime('%Y-%m')
        
        # استعلام الأرصدة
        balance_query = """
            SELECT c.code, c.symbol, COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING total_balance != 0
            ORDER BY total_balance DESC
            LIMIT 3
        """
        balance_results = db.execute_query(balance_query, (user_id,))
        
        if balance_results:
            print("   💰 تنسيق الأرصدة:")
            for balance in balance_results:
                # تطبيق نفس التنسيق المستخدم في الكود
                balance_text = f"{float(balance['total_balance']):,.2f}"
                print(f"      {balance['code']}: {balance_text} {balance['symbol']}")
                
                # فحص وجود أرقام عربية
                if '،' in balance_text:
                    print(f"      ❌ تم العثور على فاصلة عربية في: {balance_text}")
                else:
                    print(f"      ✅ تنسيق صحيح (أرقام إنجليزية): {balance_text}")
        
        # اختبار 2: فحص تنسيق الأرقام في المعاملات الشهرية
        print("\n🔍 اختبار 2: تنسيق الأرقام في المعاملات الشهرية...")
        
        transactions_query = """
            SELECT c.code, c.symbol,
                   COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as monthly_income,
                   COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as monthly_expense
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id 
                AND t.user_id = %s 
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING monthly_income > 0 OR monthly_expense > 0
            ORDER BY (monthly_income + monthly_expense) DESC
            LIMIT 3
        """
        transactions_results = db.execute_query(transactions_query, (user_id, current_month))
        
        if transactions_results:
            print("   📈 تنسيق المعاملات الشهرية:")
            for trans in transactions_results:
                # تطبيق نفس التنسيق المستخدم في الكود
                income_text = f"{float(trans['monthly_income']):,.2f}"
                expense_text = f"{float(trans['monthly_expense']):,.2f}"
                net = trans['monthly_income'] - trans['monthly_expense']
                net_text = f"{float(net):,.2f}"
                
                print(f"      {trans['code']}: واردات {income_text} - مصروفات {expense_text} = صافي {net_text} {trans['symbol']}")
                
                # فحص وجود أرقام عربية
                for text, label in [(income_text, "الواردات"), (expense_text, "المصروفات"), (net_text, "الصافي")]:
                    if '،' in text:
                        print(f"         ❌ فاصلة عربية في {label}: {text}")
                    else:
                        print(f"         ✅ تنسيق صحيح في {label}: {text}")
        
        # اختبار 3: فحص تنسيق الأرقام في تقرير الحسابات
        print("\n🔍 اختبار 3: تنسيق الأرقام في تقرير الحسابات...")
        
        accounts_query = """
            SELECT a.id, a.name, at.name as type_name
            FROM accounts a
            JOIN account_types at ON a.account_type_id = at.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            ORDER BY a.name
            LIMIT 5
        """
        accounts = db.execute_query(accounts_query, (user_id,))
        
        if accounts:
            print("   🏦 تنسيق أرصدة الحسابات:")
            for account in accounts:
                # الحصول على أرصدة الحساب
                balances_query = """
                    SELECT ab.balance, c.symbol
                    FROM account_balances ab
                    JOIN currencies c ON ab.currency_id = c.id
                    WHERE ab.account_id = %s AND ab.balance != 0
                    ORDER BY ab.balance DESC
                    LIMIT 3
                """
                balances = db.execute_query(balances_query, (account['id'],))
                
                if balances:
                    balances_list = []
                    for balance in balances:
                        # تطبيق نفس التنسيق المستخدم في الكود
                        formatted_balance = f"{float(balance['balance']):,.2f}"
                        balances_list.append(f"{formatted_balance} {balance['symbol']}")
                        
                        # فحص وجود أرقام عربية
                        if '،' in formatted_balance:
                            print(f"         ❌ فاصلة عربية في رصيد {account['name']}: {formatted_balance}")
                        else:
                            print(f"         ✅ تنسيق صحيح في رصيد {account['name']}: {formatted_balance}")
                    
                    balances_text = ", ".join(balances_list)
                    print(f"      {account['name']}: {balances_text}")
        
        # اختبار 4: فحص أمثلة تنسيق الأرقام
        print("\n🔍 اختبار 4: أمثلة تنسيق الأرقام...")
        
        test_numbers = [1234.56, 1000000.789, 0.12, 999999.99, ********.123]
        print("   🔢 أمثلة على التنسيق المستخدم:")
        
        for num in test_numbers:
            # التنسيق المستخدم في التقارير
            formatted = f"{float(num):,.2f}"
            
            print(f"      {num} → {formatted}")
            
            # فحص وجود أرقام عربية
            if '،' in formatted:
                print(f"         ❌ يحتوي على فاصلة عربية")
            else:
                print(f"         ✅ تنسيق إنجليزي صحيح")
        
        # اختبار 5: فحص التنسيق في المعاملات الشهرية (آخر 3 أشهر)
        print("\n🔍 اختبار 5: تنسيق الأرقام في المعاملات الشهرية (آخر 3 أشهر)...")
        
        for i in range(3):
            month_date = datetime.now().replace(day=1)
            for _ in range(i):
                if month_date.month == 1:
                    month_date = month_date.replace(year=month_date.year - 1, month=12)
                else:
                    month_date = month_date.replace(month=month_date.month - 1)

            month_str = month_date.strftime('%Y-%m')
            month_name = month_date.strftime('%Y/%m')

            # المعاملات لجميع العملات
            monthly_transactions_query = """
                SELECT c.code, c.symbol,
                       COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as income,
                       COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as expense
                FROM currencies c
                LEFT JOIN transactions t ON c.id = t.currency_id 
                    AND t.user_id = %s 
                    AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.symbol
                HAVING income > 0 OR expense > 0
                ORDER BY (income + expense) DESC
                LIMIT 2
            """
            monthly_results = db.execute_query(monthly_transactions_query, (user_id, month_str))
            
            if monthly_results:
                print(f"   📅 {month_name}:")
                for trans in monthly_results:
                    # تطبيق نفس التنسيق المستخدم في الكود
                    income_text = f"{float(trans['income']):,.0f}"
                    expense_text = f"{float(trans['expense']):,.0f}"
                    net = trans['income'] - trans['expense']
                    net_text = f"{float(net):,.0f}"
                    
                    print(f"      {trans['code']}: واردات {income_text} - مصروفات {expense_text} = صافي {net_text} {trans['symbol']}")
                    
                    # فحص وجود أرقام عربية
                    for text, label in [(income_text, "الواردات"), (expense_text, "المصروفات"), (net_text, "الصافي")]:
                        if '،' in text:
                            print(f"         ❌ فاصلة عربية في {label}: {text}")
                        else:
                            print(f"         ✅ تنسيق صحيح في {label}: {text}")
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار تنسيق الأرقام في التقارير:")
        print("✅ جميع الأرقام في الملخص المالي تستخدم التنسيق الإنجليزي")
        print("✅ جميع الأرقام في المعاملات الشهرية تستخدم التنسيق الإنجليزي")
        print("✅ جميع الأرقام في تقرير الحسابات تستخدم التنسيق الإنجليزي")
        print("✅ لا توجد فواصل عربية (،) في تنسيق الأرقام")
        print("✅ جميع الأرقام تستخدم الفواصل الإنجليزية (,) والنقاط العشرية (.)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تنسيق الأرقام: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تنسيق الأرقام في قسم التقارير")
    print("=" * 70)
    
    success = test_number_formatting_in_reports()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 تنسيق الأرقام في قسم التقارير صحيح!")
        
        print("\n📋 النتائج:")
        print("✅ جميع الأرقام تستخدم التنسيق الإنجليزي (1,234.56)")
        print("✅ لا توجد فواصل عربية في تنسيق الأرقام")
        print("✅ رموز العملات العربية محفوظة (ر.س، د.إ، ر.ي، $)")
        print("✅ التنسيق موحد عبر جميع أقسام التقارير")
        
        print("\n📋 للتحقق من التطبيق:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى قسم 'التقارير'")
        print("4. تحقق من أن جميع الأرقام بالتنسيق الإنجليزي")
        
    else:
        print("❌ هناك مشاكل في تنسيق الأرقام تحتاج مراجعة")

if __name__ == "__main__":
    main()
