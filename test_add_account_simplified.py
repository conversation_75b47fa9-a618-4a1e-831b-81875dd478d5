#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إضافة حساب جديد بدون نوع حساب
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from database.connection import db
from database.models import Account, Currency
from utils.auth import auth_manager
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, BUTTON_STYLES
from config.styles import ARABIC_TEXT_STYLES
import tkinter.messagebox as messagebox

class AddAccountTest:
    """اختبار إضافة حساب مبسط"""
    
    def __init__(self):
        # تسجيل دخول تلقائي للاختبار
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if users:
            auth_manager.current_user = users[0]
            print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
        
        # إنشاء النافذة
        self.window = ctk.CTk()
        self.window.title("اختبار إضافة حساب مبسط")
        self.window.geometry("500x600")
        self.window.configure(fg_color=COLORS['bg_primary'])
        
        self.create_test_interface()
        
        # تشغيل النافذة
        self.window.mainloop()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # عنوان الاختبار
        title_label = create_rtl_label(
            self.window,
            text="🧪 اختبار إضافة حساب مبسط",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))
        
        # معلومات الاختبار
        info_label = create_rtl_label(
            self.window,
            text="هذا الاختبار يتحقق من إمكانية إضافة حساب جديد بدون حقل نوع الحساب\nالحقول المطلوبة: اسم الحساب، العملة، المبلغ، الوصف",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        info_label.pack(pady=(0, 30))
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        form_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        # اسم الحساب
        name_label = create_rtl_label(
            form_frame,
            text="اسم الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w", pady=(20, 5), padx=20)
        
        self.name_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: حساب الراجحي الجديد",
            height=40
        )
        self.name_entry.pack(fill="x", pady=(0, 15), padx=20)
        
        # العملة
        currency_label = create_rtl_label(
            form_frame,
            text="العملة:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        # الحصول على العملات
        currencies = db.execute_query("SELECT id, name, symbol FROM currencies WHERE is_active = TRUE")
        currency_options = [f"{c['id']} - {c['name']} ({c['symbol']})" for c in currencies] if currencies else ["1 - ريال سعودي (ر.س)"]
        
        self.currency_combo = ctk.CTkComboBox(
            form_frame,
            values=currency_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.currency_combo.pack(fill="x", pady=(0, 15), padx=20)
        self.currency_combo.set(currency_options[0])
        
        # المبلغ الابتدائي
        balance_label = create_rtl_label(
            form_frame,
            text="المبلغ الابتدائي:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        balance_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        self.balance_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: 1000.00",
            height=40
        )
        self.balance_entry.pack(fill="x", pady=(0, 15), padx=20)
        
        # الوصف
        desc_label = create_rtl_label(
            form_frame,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5), padx=20)
        
        self.desc_entry = ctk.CTkTextbox(
            form_frame,
            height=80,
            font=ctk.CTkFont(size=14)
        )
        self.desc_entry.pack(fill="x", pady=(0, 20), padx=20)
        
        # أزرار الاختبار
        buttons_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        buttons_frame.pack(pady=20)
        
        # زر إضافة الحساب
        add_button = create_rtl_button(
            buttons_frame,
            text="✅ إضافة الحساب",
            command=self.test_add_account,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))
        
        # زر عرض الحسابات الحالية
        show_button = create_rtl_button(
            buttons_frame,
            text="📋 عرض الحسابات الحالية",
            command=self.show_current_accounts,
            **BUTTON_STYLES['secondary']
        )
        show_button.pack(side="left", padx=(0, 10))
        
        # زر إغلاق
        close_button = create_rtl_button(
            buttons_frame,
            text="❌ إغلاق",
            command=self.window.destroy,
            **BUTTON_STYLES['secondary']
        )
        close_button.pack(side="left")
    
    def test_add_account(self):
        """اختبار إضافة حساب جديد"""
        try:
            print("\n🧪 بدء اختبار إضافة حساب جديد...")
            
            # التحقق من البيانات
            name = self.name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحساب")
                return
            
            # استخراج معرف العملة
            currency_text = self.currency_combo.get()
            currency_id = int(currency_text.split(' - ')[0]) if currency_text else 1
            
            # الرصيد الابتدائي
            try:
                initial_balance = float(self.balance_entry.get() or "0")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال رصيد صحيح")
                return
            
            description = self.desc_entry.get("1.0", "end-1c").strip()
            
            print(f"   📋 البيانات:")
            print(f"      الاسم: {name}")
            print(f"      العملة: {currency_id}")
            print(f"      المبلغ: {initial_balance}")
            print(f"      الوصف: {description}")
            
            # إدراج الحساب في قاعدة البيانات
            user_id = auth_manager.current_user['id']
            
            # استخدام نموذج Account للإنشاء (بدون نوع حساب)
            result = Account.create(
                user_id=user_id,
                name=name,
                description=description
            )
            
            print(f"   📊 نتيجة إنشاء الحساب: {result}")
            
            # إضافة الرصيد الابتدائي إذا كان أكبر من صفر
            if result > 0 and initial_balance > 0:
                Account.update_balance_for_currency(result, currency_id, initial_balance)
                print(f"   💰 تم إضافة رصيد ابتدائي: {initial_balance}")
            
            if result > 0:
                messagebox.showinfo("نجح", f"تم إضافة الحساب بنجاح!\nمعرف الحساب: {result}")
                print("   ✅ تم إضافة الحساب بنجاح!")
                
                # مسح النموذج
                self.name_entry.delete(0, 'end')
                self.balance_entry.delete(0, 'end')
                self.desc_entry.delete("1.0", 'end')
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الحساب")
                print("   ❌ فشل في إضافة الحساب")
        
        except Exception as e:
            print(f"   ❌ خطأ في اختبار إضافة الحساب: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    def show_current_accounts(self):
        """عرض الحسابات الحالية"""
        try:
            print("\n📋 الحسابات الحالية:")
            user_id = auth_manager.current_user['id']
            accounts = Account.get_by_user(user_id)
            
            if accounts:
                for i, account in enumerate(accounts, 1):
                    print(f"   {i}. {account.get('name')} (ID: {account.get('id')})")
                    print(f"      الوصف: {account.get('description', 'لا يوجد')}")
                    print(f"      نشط: {'نعم' if account.get('is_active') else 'لا'}")
                    balances = account.get('balances', [])
                    if balances:
                        for balance in balances:
                            print(f"      الرصيد: {balance['balance']} {balance['symbol']}")
                    else:
                        print("      الرصيد: 0")
                    print()
                
                messagebox.showinfo("الحسابات الحالية", f"تم العثور على {len(accounts)} حساب\nتحقق من وحدة التحكم للتفاصيل")
            else:
                print("   لا توجد حسابات")
                messagebox.showinfo("الحسابات الحالية", "لا توجد حسابات حتى الآن")
        
        except Exception as e:
            print(f"❌ خطأ في عرض الحسابات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار إضافة حساب مبسط")
    print("=" * 50)
    
    # تشغيل الاختبار
    AddAccountTest()

if __name__ == "__main__":
    main()
