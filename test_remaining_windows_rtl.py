#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات RTL في النوافذ المتبقية
يختبر عرض النصوص العربية في نوافذ التحويلات والبحث والتقارير والإعدادات
"""

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, ARABIC_TEXT_STYLES, BUTTON_STYLES, CARD_STYLES

class RemainingWindowsRTLTest:
    """نافذة اختبار إصلاحات RTL في النوافذ المتبقية"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_test_interface()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        ctk.set_appearance_mode("light")
        
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاحات RTL - النوافذ المتبقية")
        self.window.geometry("1000x800")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 1000
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # العنوان الرئيسي
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاحات RTL - النوافذ المتبقية",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 10))
        
        # وصف الاختبار
        desc_label = create_rtl_label(
            self.window,
            text="اختبار النصوص العربية في نوافذ التحويلات والبحث والتقارير والإعدادات",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))
        
        # إطار التبويبات
        tabs_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        tabs_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # أزرار التبويبات
        buttons_frame = ctk.CTkFrame(tabs_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(0, 10))
        
        # أزرار النوافذ
        transfers_btn = create_rtl_button(
            buttons_frame,
            text="💸 التحويلات",
            command=lambda: self.show_transfers_test(),
            **BUTTON_STYLES['primary']
        )
        transfers_btn.pack(side="right", padx=5)
        
        search_btn = create_rtl_button(
            buttons_frame,
            text="🔍 البحث",
            command=lambda: self.show_search_test(),
            **BUTTON_STYLES['secondary']
        )
        search_btn.pack(side="right", padx=5)
        
        reports_btn = create_rtl_button(
            buttons_frame,
            text="📊 التقارير",
            command=lambda: self.show_reports_test(),
            **BUTTON_STYLES['secondary']
        )
        reports_btn.pack(side="right", padx=5)
        
        settings_btn = create_rtl_button(
            buttons_frame,
            text="⚙️ الإعدادات",
            command=lambda: self.show_settings_test(),
            **BUTTON_STYLES['secondary']
        )
        settings_btn.pack(side="right", padx=5)
        
        # إطار المحتوى
        self.content_frame = ctk.CTkScrollableFrame(tabs_frame, fg_color=COLORS['bg_card'])
        self.content_frame.pack(fill="both", expand=True)
        
        # عرض اختبار التحويلات افتراضياً
        self.show_transfers_test()
    
    def clear_content(self):
        """مسح المحتوى"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_transfers_test(self):
        """عرض اختبار نافذة التحويلات"""
        self.clear_content()
        
        # عنوان النافذة
        title = create_rtl_label(
            self.content_frame,
            text="التحويلات بين الحسابات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(30, 20))
        
        # زر إضافة تحويل
        add_btn = create_rtl_button(
            self.content_frame,
            text="+ إضافة تحويل جديد",
            command=self.show_add_transfer_dialog,
            **BUTTON_STYLES['primary']
        )
        add_btn.pack(pady=20)
        
        # بطاقة تحويل تجريبية
        transfer_card = ctk.CTkFrame(self.content_frame, **CARD_STYLES['elevated'])
        transfer_card.pack(fill="x", padx=30, pady=10)
        
        # معلومات التحويل
        transfer_info = create_rtl_label(
            transfer_card,
            text="1,500.00 ر.س  ->  البنك الأهلي إلى محفظة نقدية",
            font_size='header',
            text_color=COLORS['info'],
            **ARABIC_TEXT_STYLES['label']
        )
        transfer_info.pack(pady=15)
        
        transfer_desc = create_rtl_label(
            transfer_card,
            text="من: البنك الأهلي السعودي   |   إلى: محفظة نقدية",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        transfer_desc.pack(pady=(0, 10))
        
        delete_btn = create_rtl_button(
            transfer_card,
            text="🗑️ حذف",
            command=self.show_test_message,
            **BUTTON_STYLES['danger']
        )
        delete_btn.pack(pady=(0, 15))
    
    def show_search_test(self):
        """عرض اختبار نافذة البحث"""
        self.clear_content()
        
        # عنوان النافذة
        title = create_rtl_label(
            self.content_frame,
            text="البحث في المعاملات والتحويلات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(30, 20))
        
        # نوع البحث
        search_type_label = create_rtl_label(
            self.content_frame,
            text="نوع البحث:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        search_type_label.pack(pady=(0, 10))
        
        # أزرار البحث
        search_buttons_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        search_buttons_frame.pack(pady=10)
        
        simple_search_btn = create_rtl_button(
            search_buttons_frame,
            text="🔍 البحث العادي",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        simple_search_btn.pack(side="right", padx=5)
        
        advanced_search_btn = create_rtl_button(
            search_buttons_frame,
            text="🔧 البحث المتقدم",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        advanced_search_btn.pack(side="right", padx=5)
        
        # حقل البحث
        search_label = create_rtl_label(
            self.content_frame,
            text="كلمات البحث:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        search_label.pack(anchor="w", padx=30, pady=(20, 5))
        
        search_entry = create_rtl_entry(
            self.content_frame,
            placeholder_text="مثال: راتب، طعام، تحويل، إلخ...",
            height=50
        )
        search_entry.pack(fill="x", padx=30, pady=(0, 20))
        
        # زر البحث
        search_btn = create_rtl_button(
            self.content_frame,
            text="🔍 بحث",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        search_btn.pack(pady=10)
        
        # رسالة النتائج
        results_label = create_rtl_label(
            self.content_frame,
            text="أدخل كلمات البحث واضغط على زر البحث لعرض النتائج",
            font_size='header',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['title']
        )
        results_label.pack(pady=30)
    
    def show_reports_test(self):
        """عرض اختبار نافذة التقارير"""
        self.clear_content()
        
        # عنوان النافذة
        title = create_rtl_label(
            self.content_frame,
            text="التقارير والإحصائيات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(30, 20))
        
        # أنواع التقارير
        reports_label = create_rtl_label(
            self.content_frame,
            text="أنواع التقارير المتاحة:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        reports_label.pack(pady=(0, 15))
        
        # أزرار التقارير
        reports_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        reports_frame.pack(pady=10)
        
        monthly_btn = create_rtl_button(
            reports_frame,
            text="📅 التقرير الشهري",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        monthly_btn.pack(pady=5)
        
        yearly_btn = create_rtl_button(
            reports_frame,
            text="📊 التقرير السنوي",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        yearly_btn.pack(pady=5)
        
        custom_btn = create_rtl_button(
            reports_frame,
            text="🔧 تقرير مخصص",
            command=self.show_test_message,
            **BUTTON_STYLES['info']
        )
        custom_btn.pack(pady=5)
    
    def show_settings_test(self):
        """عرض اختبار نافذة الإعدادات"""
        self.clear_content()
        
        # عنوان النافذة
        title = create_rtl_label(
            self.content_frame,
            text="إعدادات التطبيق",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(30, 20))
        
        # إعدادات عامة
        general_label = create_rtl_label(
            self.content_frame,
            text="الإعدادات العامة:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        general_label.pack(anchor="w", padx=30, pady=(0, 10))
        
        # خيارات الإعدادات
        settings_frame = ctk.CTkFrame(self.content_frame, fg_color=COLORS['bg_light'])
        settings_frame.pack(fill="x", padx=30, pady=10)
        
        language_label = create_rtl_label(
            settings_frame,
            text="اللغة: العربية",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        language_label.pack(anchor="w", padx=20, pady=10)
        
        currency_label = create_rtl_label(
            settings_frame,
            text="العملة الافتراضية: ريال سعودي (ر.س)",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", padx=20, pady=10)
        
        # أزرار الإعدادات
        settings_buttons_frame = ctk.CTkFrame(self.content_frame, fg_color="transparent")
        settings_buttons_frame.pack(pady=20)
        
        backup_btn = create_rtl_button(
            settings_buttons_frame,
            text="💾 نسخ احتياطي",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        backup_btn.pack(pady=5)
        
        restore_btn = create_rtl_button(
            settings_buttons_frame,
            text="📥 استعادة البيانات",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        restore_btn.pack(pady=5)
    
    def show_add_transfer_dialog(self):
        """عرض نافذة إضافة تحويل جديد"""
        dialog = ctk.CTkToplevel(self.window)
        dialog.title("إضافة تحويل جديد")
        dialog.geometry("500x600")
        dialog.configure(fg_color=COLORS['bg_light'])
        
        # عنوان النافذة
        title = create_rtl_label(
            dialog,
            text="إضافة تحويل جديد",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(20, 30))
        
        # من
        from_label = create_rtl_label(
            dialog,
            text="من:",
            font_size='header',
            **ARABIC_TEXT_STYLES['title']
        )
        from_label.pack(pady=5)
        
        from_account_label = create_rtl_label(
            dialog,
            text="الحساب المصدر:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        from_account_label.pack(anchor="w", padx=30)
        
        # إلى
        to_label = create_rtl_label(
            dialog,
            text="إلى:",
            font_size='header',
            **ARABIC_TEXT_STYLES['title']
        )
        to_label.pack(pady=(20, 5))
        
        to_account_label = create_rtl_label(
            dialog,
            text="الحساب الهدف:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        to_account_label.pack(anchor="w", padx=30)
        
        # أزرار
        buttons_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        buttons_frame.pack(pady=30)
        
        save_btn = create_rtl_button(
            buttons_frame,
            text="✓ حفظ التحويل",
            command=dialog.destroy,
            **BUTTON_STYLES['primary']
        )
        save_btn.pack(side="left", padx=5)
        
        cancel_btn = create_rtl_button(
            buttons_frame,
            text="✗ إلغاء",
            command=dialog.destroy,
            **BUTTON_STYLES['secondary']
        )
        cancel_btn.pack(side="left", padx=5)
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        from tkinter import messagebox
        messagebox.showinfo("اختبار الزر", "تم النقر على الزر! النصوص العربية تظهر بـ RTL صحيح.")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار إصلاحات RTL في النوافذ المتبقية...")
    print("=" * 70)
    print("📋 ما يتم اختباره:")
    print("   ✅ نافذة التحويلات ونافذة إضافة تحويل جديد")
    print("   ✅ نافذة البحث (العادي والمتقدم)")
    print("   ✅ نافذة التقارير والإحصائيات")
    print("   ✅ نافذة الإعدادات")
    print("   ✅ جميع النصوص العربية والأزرار والحقول")
    print("=" * 70)
    
    try:
        app = RemainingWindowsRTLTest()
        app.run()
        print("✅ تم إنهاء الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
