import bcrypt
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from database.connection import db

class AuthManager:
    """مدير المصادقة والصلاحيات"""

    def __init__(self):
        self.current_user = None
        self.session_token = None

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        try:
            # تحويل كلمة المرور إلى bytes
            password_bytes = password.encode('utf-8')
            # إنشاء salt وتشفير كلمة المرور
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password_bytes, salt)
            return hashed.decode('utf-8')
        except Exception as e:
            logging.error(f"خطأ في تشفير كلمة المرور: {e}")
            return None

    def verify_password(self, password, hashed_password):
        """التحقق من كلمة المرور"""
        try:
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception as e:
            logging.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False

    def register_user(self, username, password, full_name, role='user'):
        """تسجيل مستخدم جديد - محدث لدعم المستخدمين المتعددين"""
        try:
            # التحقق من الاتصال بقاعدة البيانات أولاً
            if not db.is_connected():
                return False, "لا يوجد اتصال بقاعدة البيانات. تحقق من إعدادات MySQL"

            # التحقق من صلاحية المستخدم الحالي لإنشاء مستخدمين جدد
            if not self.current_user or self.current_user.get('role') != 'admin':
                return False, "يجب أن تكون مديراً لإنشاء مستخدمين جدد"

            # التحقق من صحة البيانات
            if not username or len(username.strip()) < 3:
                return False, "اسم المستخدم يجب أن يكون 3 أحرف على الأقل"

            if not password or len(password) < 6:
                return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"

            if not full_name or len(full_name.strip()) < 2:
                return False, "الاسم الكامل يجب أن يكون حرفين على الأقل"

            # التحقق من عدم وجود المستخدم
            if self.user_exists(username):
                return False, "اسم المستخدم موجود مسبقاً"

            # تشفير كلمة المرور
            hashed_password = self.hash_password(password)
            if not hashed_password:
                return False, "خطأ في تشفير كلمة المرور"

            # إدراج المستخدم الجديد
            from database.models import User
            success, result = User.create_user(
                username=username.strip(),
                password_hash=hashed_password,
                full_name=full_name.strip(),
                role=role,
                created_by=self.current_user['id']
            )

            if success:
                logging.info(f"تم تسجيل المستخدم {username} بنجاح بواسطة {self.current_user['username']}")
                return True, f"تم إنشاء حساب المستخدم {username} بنجاح"
            else:
                return False, result

        except Exception as e:
            logging.error(f"خطأ في تسجيل المستخدم: {e}")
            return False, f"خطأ في التسجيل: {str(e)}"

    def login(self, username, password):
        """تسجيل الدخول - محدث لدعم المستخدمين المتعددين"""
        try:
            # التحقق من صحة البيانات
            if not username or not password:
                return False, "يرجى إدخال اسم المستخدم وكلمة المرور"

            # التحقق من الاتصال بقاعدة البيانات أولاً
            if not db.is_connected():
                return False, "لا يوجد اتصال بقاعدة البيانات. تحقق من إعدادات MySQL"

            # التحقق من وجود جدول المستخدمين وإنشاؤه إذا لم يكن موجوداً
            try:
                # محاولة فحص وجود الجدول
                check_table_query = "SHOW TABLES LIKE 'users'"
                table_exists = db.execute_query(check_table_query)

                if not table_exists:
                    logging.info("جدول المستخدمين غير موجود - سيتم إنشاؤه")
                    success = self._create_users_table_and_admin()
                    if not success:
                        return False, "فشل في إنشاء جدول المستخدمين"
            except Exception as e:
                logging.error(f"خطأ في فحص جدول المستخدمين: {e}")
                return False, "خطأ في الوصول إلى قاعدة البيانات"

            # البحث عن المستخدم
            query = """
                SELECT id, username, password_hash, full_name, role, is_active
                FROM users
                WHERE username = %s AND is_active = TRUE
            """
            users = db.execute_query(query, (username,))

            if not users:
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة"

            user = users[0]

            # التحقق من كلمة المرور
            if not self.verify_password(password, user['password_hash']):
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة"

            # تحديث آخر تسجيل دخول
            try:
                from database.models import User
                User.update_last_login(user['id'])
            except Exception as e:
                logging.warning(f"فشل في تحديث آخر تسجيل دخول: {e}")

            # حفظ بيانات المستخدم الحالي
            self.current_user = {
                'id': user['id'],
                'username': user['username'],
                'full_name': user['full_name'],
                'role': user['role']
            }

            # إنشاء رمز الجلسة
            self.session_token = self.generate_session_token()

            # تسجيل العملية
            try:
                self.log_activity('login', 'users', user['id'])
            except Exception as e:
                logging.warning(f"فشل في تسجيل النشاط: {e}")

            logging.info(f"تم تسجيل دخول المستخدم {username} بنجاح")
            return True, "تم تسجيل الدخول بنجاح"

        except Exception as e:
            logging.error(f"خطأ في تسجيل الدخول: {e}")
            return False, f"خطأ في تسجيل الدخول: {str(e)}"

    def _create_users_table_and_admin(self):
        """إنشاء جدول المستخدمين والمستخدم الافتراضي"""
        try:
            # إنشاء جدول المستخدمين
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'user') DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                profile_image VARCHAR(255) NULL,
                created_by INT NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            )
            """

            db.execute_update(create_table_sql)
            logging.info("تم إنشاء جدول المستخدمين")

            # إنشاء المستخدم الافتراضي admin
            password_hash = self.hash_password("123456")
            if password_hash:
                insert_admin_sql = """
                INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, NOW())
                """

                user_id = db.execute_insert(insert_admin_sql, (
                    'admin', password_hash, 'المدير الافتراضي', 'admin', True
                ))

                if user_id > 0:
                    logging.info(f"تم إنشاء المستخدم admin بنجاح (ID: {user_id})")
                    return True

            return False

        except Exception as e:
            logging.error(f"خطأ في إنشاء جدول المستخدمين: {e}")
            return False

    def logout(self):
        """تسجيل الخروج"""
        try:
            if self.current_user:
                # تسجيل العملية
                self.log_activity('logout', 'users', self.current_user['id'])
                logging.info(f"تم تسجيل خروج المستخدم {self.current_user['username']}")

            # مسح بيانات الجلسة
            self.current_user = None
            self.session_token = None
            return True

        except Exception as e:
            logging.error(f"خطأ في تسجيل الخروج: {e}")
            return False

    def user_exists(self, username):
        """التحقق من وجود المستخدم"""
        try:
            # التحقق من الاتصال بقاعدة البيانات أولاً
            if not db.is_connected():
                logging.error("لا يوجد اتصال بقاعدة البيانات")
                return True  # إرجاع True للأمان

            query = """
                SELECT COUNT(*) as count
                FROM users
                WHERE username = %s
            """
            result = db.execute_query(query, (username,))

            return result[0]['count'] > 0 if result else False
        except Exception as e:
            logging.error(f"خطأ في التحقق من وجود المستخدم: {e}")
            return True  # إرجاع True للأمان

    def generate_session_token(self):
        """إنشاء رمز جلسة"""
        return secrets.token_urlsafe(32)

    def has_permission(self, permission):
        """التحقق من الصلاحية"""
        if not self.current_user:
            return False

        from config.settings import USER_ROLES
        user_role = self.current_user.get('role', 'user')
        role_permissions = USER_ROLES.get(user_role, {}).get('permissions', [])

        return permission in role_permissions

    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.current_user and self.current_user.get('role') == 'admin'

    def is_logged_in(self):
        """التحقق من تسجيل الدخول"""
        return self.current_user is not None

    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        return self.current_user

    def change_password(self, old_password, new_password):
        """تغيير كلمة المرور"""
        try:
            if not self.current_user:
                return False, "يجب تسجيل الدخول أولاً"

            # الحصول على كلمة المرور الحالية
            query = "SELECT password_hash FROM users WHERE id = %s"
            result = db.execute_query(query, (self.current_user['id'],))

            if not result:
                return False, "خطأ في الحصول على بيانات المستخدم"

            # التحقق من كلمة المرور القديمة
            if not self.verify_password(old_password, result[0]['password_hash']):
                return False, "كلمة المرور القديمة غير صحيحة"

            # تشفير كلمة المرور الجديدة
            new_hashed = self.hash_password(new_password)
            if not new_hashed:
                return False, "خطأ في تشفير كلمة المرور الجديدة"

            # تحديث كلمة المرور
            update_query = "UPDATE users SET password_hash = %s WHERE id = %s"
            rows_affected = db.execute_update(update_query, (new_hashed, self.current_user['id']))

            if rows_affected > 0:
                # تسجيل العملية
                self.log_activity('change_password', 'users', self.current_user['id'])
                return True, "تم تغيير كلمة المرور بنجاح"
            else:
                return False, "خطأ في تحديث كلمة المرور"

        except Exception as e:
            logging.error(f"خطأ في تغيير كلمة المرور: {e}")
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    def log_activity(self, action, table_name, record_id, description=None):
        """تسجيل نشاط المستخدم"""
        try:
            if not self.current_user:
                return

            query = """
                INSERT INTO activity_log (user_id, action_type, table_name, record_id, description)
                VALUES (%s, %s, %s, %s, %s)
            """
            db.execute_insert(query, (
                self.current_user['id'],
                action,
                table_name,
                record_id,
                description or f"المستخدم {action} في {table_name}"
            ))

        except Exception as e:
            logging.error(f"خطأ في تسجيل النشاط: {e}")

    def has_permission(self, permission):
        """التحقق من صلاحية المستخدم"""
        if not self.current_user:
            return False

        # المدير له جميع الصلاحيات
        if self.current_user.get('role') == 'admin':
            return True

        # يمكن إضافة منطق صلاحيات أكثر تعقيداً هنا
        return False

    def is_admin(self):
        """التحقق من كون المستخدم مديراً"""
        return self.current_user and self.current_user.get('role') == 'admin'

    def can_manage_users(self):
        """التحقق من إمكانية إدارة المستخدمين"""
        return self.is_admin()

    def update_user_data(self, user_id, **kwargs):
        """تحديث بيانات المستخدم مع التحقق من الصلاحيات"""
        try:
            if not self.can_manage_users():
                return False, "ليس لديك صلاحية لإدارة المستخدمين"

            from database.models import User
            return User.update_user(user_id, **kwargs)

        except Exception as e:
            logging.error(f"خطأ في تحديث بيانات المستخدم: {e}")
            return False, f"خطأ في تحديث البيانات: {str(e)}"

    def delete_user_account(self, user_id):
        """حذف حساب مستخدم مع التحقق من الصلاحيات"""
        try:
            if not self.can_manage_users():
                return False, "ليس لديك صلاحية لإدارة المستخدمين"

            # منع المستخدم من حذف نفسه
            if self.current_user['id'] == user_id:
                return False, "لا يمكنك حذف حسابك الخاص"

            from database.models import User
            return User.delete_user(user_id)

        except Exception as e:
            logging.error(f"خطأ في حذف المستخدم: {e}")
            return False, f"خطأ في حذف المستخدم: {str(e)}"

    def delete_user_permanently(self, user_id):
        """حذف المستخدم نهائياً من قاعدة البيانات"""
        try:
            if not self.can_manage_users():
                return False, "ليس لديك صلاحية لإدارة المستخدمين"

            # منع المستخدم من حذف نفسه
            if self.current_user['id'] == user_id:
                return False, "لا يمكنك حذف حسابك الخاص"

            from database.models import User
            return User.delete_user_permanently(user_id)

        except Exception as e:
            logging.error(f"خطأ في الحذف النهائي للمستخدم: {e}")
            return False, f"خطأ في الحذف النهائي: {str(e)}"

    def reset_user_password(self, user_id, new_password):
        """إعادة تعيين كلمة مرور المستخدم"""
        try:
            if not self.can_manage_users():
                return False, "ليس لديك صلاحية لإدارة المستخدمين"

            if len(new_password) < 6:
                return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"

            # تشفير كلمة المرور الجديدة
            new_password_hash = self.hash_password(new_password)
            if not new_password_hash:
                return False, "خطأ في تشفير كلمة المرور"

            from database.models import User
            return User.change_password(user_id, new_password_hash)

        except Exception as e:
            logging.error(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            return False, f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"

# إنشاء مثيل عام لمدير المصادقة
auth_manager = AuthManager()
