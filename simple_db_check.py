#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط لقاعدة البيانات
"""

import mysql.connector
from mysql.connector import Error

def check_database():
    """فحص قاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }

    try:
        print("محاولة الاتصال بقاعدة البيانات...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)

        print("✅ تم الاتصال بنجاح")

        # فحص الجداول
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()

        print(f"الجداول الموجودة ({len(tables)}):")
        for table in tables:
            table_name = list(table.values())[0]
            print(f"  - {table_name}")

        # فحص جدول المستخدمين إذا كان موجوداً
        if any('users' in str(table.values()) for table in tables):
            print("\nفحص جدول المستخدمين:")
            cursor.execute("SELECT username, full_name, role, is_active FROM users")
            users = cursor.fetchall()

            for user in users:
                print(f"  - {user['username']}: {user['full_name']} ({user['role']})")

        cursor.close()
        connection.close()

    except Error as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_database()
