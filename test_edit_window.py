#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تعديل المعاملات
"""

import sys
import os
import customtkinter as ctk

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_edit_window():
    """اختبار نافذة التعديل"""
    print("🧪 اختبار نافذة تعديل المعاملات...")
    
    try:
        # إعداد customtkinter
        ctk.set_appearance_mode("light")
        
        # إنشاء نافذة اختبار
        root = ctk.CTk()
        root.title("اختبار نافذة التعديل")
        root.geometry("400x300")
        
        # بيانات تجريبية للمعاملة
        test_transaction = {
            'id': 1,
            'amount': 1000.0,
            'account_name': 'حساب تجريبي',
            'currency_symbol': 'ر.س',
            'currency_id': 1,
            'currency_name': 'ريال سعودي',
            'category_name': 'راتب',
            'description': 'راتب شهر يناير',
            'transaction_date': '2024-01-15',
            'transaction_type': 'income'
        }
        
        # محاولة استيراد الواجهة الرئيسية
        try:
            from gui.main_window import MainWindow
            from utils.auth import auth_manager
            from config.colors import COLORS, BUTTON_STYLES
            
            # إعداد مستخدم تجريبي
            auth_manager.current_user = {'id': 1, 'full_name': 'مستخدم تجريبي'}
            
            print("✅ تم استيراد الواجهة الرئيسية بنجاح")
            
            # إنشاء كائن النافذة الرئيسية
            main_window = MainWindow()
            
            # زر لاختبار نافذة التعديل
            test_button = ctk.CTkButton(
                root,
                text="اختبار نافذة التعديل",
                font=ctk.CTkFont(size=16, weight="bold"),
                command=lambda: main_window.show_edit_transaction_dialog(test_transaction, "income")
            )
            test_button.pack(pady=50)
            
            info_label = ctk.CTkLabel(
                root,
                text="اضغط على الزر لاختبار نافذة التعديل\nيجب أن تظهر جميع الحقول:\n• المبلغ\n• الحساب\n• العملة\n• التصنيف\n• التاريخ\n• الوصف",
                font=ctk.CTkFont(size=12),
                justify="center"
            )
            info_label.pack(pady=20)
            
            print("✅ تم إنشاء نافذة الاختبار")
            print("اضغط على الزر لاختبار نافذة التعديل")
            
            # تشغيل النافذة
            root.mainloop()
            
        except ImportError as e:
            print(f"❌ خطأ في استيراد الواجهة: {e}")
            
            # إنشاء نافذة خطأ
            error_label = ctk.CTkLabel(
                root,
                text=f"خطأ في استيراد الواجهة:\n{str(e)}\n\nتأكد من وجود جميع الملفات المطلوبة",
                font=ctk.CTkFont(size=12),
                text_color="red"
            )
            error_label.pack(pady=50)
            
            root.mainloop()
            
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            
            # إنشاء نافذة خطأ
            error_label = ctk.CTkLabel(
                root,
                text=f"خطأ عام:\n{str(e)}",
                font=ctk.CTkFont(size=12),
                text_color="red"
            )
            error_label.pack(pady=50)
            
            root.mainloop()
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء نافذة الاختبار: {e}")

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    print("📁 التحقق من وجود الملفات المطلوبة...")
    
    required_files = [
        'gui/main_window.py',
        'database/models.py',
        'database/connection.py',
        'utils/auth.py',
        'config/colors.py',
        'config/settings.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات المطلوبة موجودة")
        return True

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نافذة تعديل المعاملات")
    print("=" * 60)
    
    # التحقق من وجود الملفات
    if not check_files():
        print("❌ بعض الملفات مفقودة!")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار النافذة
    test_edit_window()

if __name__ == "__main__":
    main()
