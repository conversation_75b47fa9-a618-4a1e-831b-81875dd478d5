#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات قسم التقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_reports_fixes():
    """اختبار إصلاحات قسم التقارير"""
    try:
        print("🧪 اختبار إصلاحات قسم التقارير")
        print("=" * 60)
        
        # تهيئة قاعدة البيانات
        from database.connection import db
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # اختبار 1: الملخص المالي متعدد العملات
        print("\n🔍 اختبار 1: الملخص المالي متعدد العملات...")
        
        # اختبار استعلام الأرصدة
        balance_query = """
            SELECT c.code, c.symbol, COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING total_balance != 0
            ORDER BY total_balance DESC
        """
        
        balance_results = db.execute_query(balance_query, (user_id,))
        if balance_results:
            print(f"   ✅ تم العثور على أرصدة في {len(balance_results)} عملة:")
            for balance in balance_results:
                balance_text = f"{float(balance['total_balance']):,.2f}"
                print(f"      {balance['code']}: {balance_text} {balance['symbol']}")
        else:
            print("   ⚠️ لا توجد أرصدة متاحة")
        
        # اختبار استعلام المعاملات الشهرية
        from datetime import datetime
        current_month = datetime.now().strftime('%Y-%m')
        
        transactions_query = """
            SELECT c.code, c.symbol,
                   COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as monthly_income,
                   COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as monthly_expense
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id 
                AND t.user_id = %s 
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING monthly_income > 0 OR monthly_expense > 0
            ORDER BY (monthly_income + monthly_expense) DESC
        """
        
        transactions_results = db.execute_query(transactions_query, (user_id, current_month))
        if transactions_results:
            print(f"   ✅ تم العثور على معاملات في {len(transactions_results)} عملة للشهر الحالي:")
            for trans in transactions_results:
                income_text = f"{float(trans['monthly_income']):,.2f}"
                expense_text = f"{float(trans['monthly_expense']):,.2f}"
                net = trans['monthly_income'] - trans['monthly_expense']
                net_text = f"{float(net):,.2f}"
                print(f"      {trans['code']}: واردات {income_text} - مصروفات {expense_text} = صافي {net_text} {trans['symbol']}")
        else:
            print("   ⚠️ لا توجد معاملات للشهر الحالي")
        
        # اختبار 2: تقرير الحسابات مع تنسيق الأرقام
        print("\n🔍 اختبار 2: تقرير الحسابات مع تنسيق الأرقام...")
        
        accounts_query = """
            SELECT a.id, a.name, at.name as type_name
            FROM accounts a
            JOIN account_types at ON a.account_type_id = at.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            ORDER BY a.name
        """
        accounts = db.execute_query(accounts_query, (user_id,))
        
        if accounts:
            print(f"   ✅ تم العثور على {len(accounts)} حساب:")
            for account in accounts:
                # الحصول على أرصدة الحساب
                balances_query = """
                    SELECT ab.balance, c.symbol
                    FROM account_balances ab
                    JOIN currencies c ON ab.currency_id = c.id
                    WHERE ab.account_id = %s AND ab.balance != 0
                    ORDER BY ab.balance DESC
                """
                balances = db.execute_query(balances_query, (account['id'],))
                
                # تنسيق الأرصدة بالأرقام الإنجليزية
                if balances:
                    balances_list = []
                    for balance in balances:
                        formatted_balance = f"{float(balance['balance']):,.2f}"
                        balances_list.append(f"{formatted_balance} {balance['symbol']}")
                    balances_text = ", ".join(balances_list)
                else:
                    balances_text = "0.00 ر.س"
                
                print(f"      {account['name']} ({account['type_name']}): {balances_text}")
        else:
            print("   ⚠️ لا توجد حسابات")
        
        # اختبار 3: المعاملات الشهرية متعددة العملات
        print("\n🔍 اختبار 3: المعاملات الشهرية متعددة العملات...")
        
        # اختبار آخر 3 أشهر
        for i in range(3):
            month_date = datetime.now().replace(day=1)
            for _ in range(i):
                if month_date.month == 1:
                    month_date = month_date.replace(year=month_date.year - 1, month=12)
                else:
                    month_date = month_date.replace(month=month_date.month - 1)

            month_str = month_date.strftime('%Y-%m')
            month_name = month_date.strftime('%Y/%m')

            # المعاملات لجميع العملات
            monthly_transactions_query = """
                SELECT c.code, c.symbol,
                       COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as income,
                       COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as expense
                FROM currencies c
                LEFT JOIN transactions t ON c.id = t.currency_id 
                    AND t.user_id = %s 
                    AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
                WHERE c.is_active = TRUE
                GROUP BY c.id, c.code, c.symbol
                HAVING income > 0 OR expense > 0
                ORDER BY (income + expense) DESC
            """
            monthly_results = db.execute_query(monthly_transactions_query, (user_id, month_str))
            
            if monthly_results:
                print(f"   📅 {month_name}:")
                for trans in monthly_results:
                    income_text = f"{float(trans['income']):,.0f}"
                    expense_text = f"{float(trans['expense']):,.0f}"
                    net = trans['income'] - trans['expense']
                    net_text = f"{float(net):,.0f}"
                    net_prefix = "+" if net >= 0 else ""
                    print(f"      {trans['code']}: واردات {income_text} - مصروفات {expense_text} = صافي {net_prefix}{net_text} {trans['symbol']}")
            else:
                print(f"   📅 {month_name}: لا توجد معاملات")
        
        # اختبار 4: فحص تنسيق الأرقام
        print("\n🔍 اختبار 4: فحص تنسيق الأرقام...")
        
        test_numbers = [1234.56, 1000000.789, 0.12, 999.99]
        print("   ✅ أمثلة على تنسيق الأرقام الإنجليزية:")
        for num in test_numbers:
            formatted = f"{float(num):,.2f}"
            print(f"      {num} → {formatted}")
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج اختبار إصلاحات التقارير:")
        print("✅ الملخص المالي يعرض جميع العملات")
        print("✅ المعاملات الشهرية تعرض جميع العملات")
        print("✅ تقرير الحسابات يستخدم الأرقام الإنجليزية")
        print("✅ جميع التقارير تستخدم تنسيق الأرقام الإنجليزية")
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("1. ✅ إصلاح الملخص المالي لعرض جميع العملات")
        print("2. ✅ إصلاح المعاملات الشهرية لعرض جميع العملات")
        print("3. ✅ توحيد تنسيق الأرقام بالأرقام الإنجليزية")
        print("4. ✅ تحسين تقرير الحسابات مع تنسيق صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاحات التقارير: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاحات قسم التقارير")
    print("=" * 70)
    
    success = test_reports_fixes()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 تم إصلاح جميع مشاكل قسم التقارير بنجاح!")
        
        print("\n📋 للاختبار من التطبيق:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى قسم 'التقارير'")
        print("4. تحقق من:")
        print("   • الملخص المالي يعرض جميع العملات")
        print("   • المعاملات الشهرية تعرض جميع العملات")
        print("   • تقرير الحسابات يستخدم الأرقام الإنجليزية")
        print("   • جميع الأرقام بالتنسيق الإنجليزي (1,234.56)")
        
    else:
        print("❌ هناك مشاكل تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
