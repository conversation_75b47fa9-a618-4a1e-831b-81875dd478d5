#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملفات Excel بسيطة للاختبار
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_simple_excel_files():
    """إنشاء ملفات Excel بسيطة للاختبار"""
    
    print("📁 إنشاء ملفات Excel بسيطة...")
    
    # بيانات الواردات
    income_data = {
        'المبلغ': [5000, 3000, 2000, 1500, 4000],
        'الحساب': ['البشرية', 'البشرية', 'البشرية', 'البشرية', 'البشرية'],
        'العملة': ['SAR', 'SAR', 'SAR', 'SAR', 'SAR'],
        'التاريخ': [
            '2024-12-20',
            '2024-12-21', 
            '2024-12-22',
            '2024-12-23',
            '2024-12-24'
        ],
        'الوصف': [
            'راتب شهر ديسمبر',
            'مكافأة نهاية السنة',
            'عمولة مبيعات',
            'إيجار عقار',
            'أرباح استثمار'
        ]
    }
    
    # بيانات المصروفات
    expense_data = {
        'المبلغ': [500, 300, 200, 150, 400],
        'الحساب': ['البشرية', 'البشرية', 'البشرية', 'البشرية', 'البشرية'],
        'العملة': ['SAR', 'SAR', 'SAR', 'SAR', 'SAR'],
        'التاريخ': [
            '2024-12-20',
            '2024-12-21',
            '2024-12-22', 
            '2024-12-23',
            '2024-12-24'
        ],
        'الوصف': [
            'فاتورة كهرباء',
            'فاتورة مياه',
            'تسوق بقالة',
            'وقود السيارة',
            'صيانة السيارة'
        ]
    }
    
    try:
        # إنشاء DataFrames
        income_df = pd.DataFrame(income_data)
        expense_df = pd.DataFrame(expense_data)
        
        # حفظ الملفات
        income_file = 'test_income_simple.xlsx'
        expense_file = 'test_expense_simple.xlsx'
        
        income_df.to_excel(income_file, index=False, engine='openpyxl')
        expense_df.to_excel(expense_file, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الواردات: {income_file}")
        print(f"✅ تم إنشاء ملف المصروفات: {expense_file}")
        
        print("\n📋 محتوى ملف الواردات:")
        print(income_df.to_string(index=False))
        
        print("\n📋 محتوى ملف المصروفات:")
        print(expense_df.to_string(index=False))
        
        print(f"\n🧪 خطوات الاختبار:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى قسم 'الواردات'")
        print(f"4. اضغط 'استيراد من Excel' واختر: {income_file}")
        print("5. تحقق من ظهور 5 عمليات واردة فوراً في القائمة")
        print("6. انتقل إلى قسم 'المصروفات'")
        print(f"7. اضغط 'استيراد من Excel' واختر: {expense_file}")
        print("8. تحقق من ظهور 5 عمليات مصروفات فوراً في القائمة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملفات Excel: {e}")
        return False

if __name__ == "__main__":
    create_simple_excel_files()
