# ألوان التصميم البنفسجي المتدرج
COLORS = {
    # الألوان الأساسية
    'primary': '#8B5CF6',           # بنفسجي أساسي
    'primary_dark': '#7C3AED',      # بنفسجي داكن
    'primary_light': '#A78BFA',     # بنفسجي فاتح
    'secondary': '#EC4899',         # وردي أرجواني
    'accent': '#F59E0B',            # ذهبي للتمييز
    
    # خلفيات متدرجة
    'bg_primary': '#1E1B4B',        # خلفية رئيسية داكنة
    'bg_secondary': '#312E81',      # خلفية ثانوية
    'bg_light': '#F8FAFC',          # خلفية فاتحة
    'bg_card': '#FFFFFF',           # خلفية البطاقات
    
    # النصوص
    'text_primary': '#1F2937',      # نص أساسي
    'text_secondary': '#6B7280',    # نص ثانوي
    'text_light': '#FFFFFF',        # نص أبيض
    'text_muted': '#9CA3AF',        # نص خافت

    # نصوص محسنة للتباين على الخلفيات الداكنة
    'text_on_dark': '#FFFFFF',      # نص أبيض للخلفيات الداكنة
    'text_on_dark_muted': '#E5E7EB', # نص خافت للخلفيات الداكنة
    'text_on_dark_secondary': '#D1D5DB', # نص ثانوي للخلفيات الداكنة
    
    # الحالات
    'success': '#10B981',           # أخضر للنجاح
    'warning': '#F59E0B',           # برتقالي للتحذير
    'error': '#EF4444',             # أحمر للخطأ
    'info': '#3B82F6',              # أزرق للمعلومات

    # ألوان الحالات للخلفيات الداكنة (تباين محسن)
    'success_on_dark': '#34D399',   # أخضر فاتح للخلفيات الداكنة
    'warning_on_dark': '#FBBF24',   # برتقالي فاتح للخلفيات الداكنة
    'error_on_dark': '#F87171',     # أحمر فاتح للخلفيات الداكنة
    'info_on_dark': '#60A5FA',      # أزرق فاتح للخلفيات الداكنة
    
    # الحدود والظلال
    'border': '#E5E7EB',            # حدود
    'border_focus': '#8B5CF6',      # حدود عند التركيز
    'shadow': 'rgba(139, 92, 246, 0.1)',  # ظل بنفسجي خفيف
    
    # التدرجات
    'gradient_primary': 'linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%)',
    'gradient_secondary': 'linear-gradient(135deg, #7C3AED 0%, #8B5CF6 100%)',
    'gradient_bg': 'linear-gradient(135deg, #1E1B4B 0%, #312E81 100%)',
}

# أنماط الأزرار
BUTTON_STYLES = {
    'primary': {
        'fg_color': COLORS['primary'],
        'hover_color': COLORS['primary_dark'],
        'text_color': COLORS['text_light'],
        'border_width': 0,
        'corner_radius': 12,
        'height': 50,
    },
    'secondary': {
        'fg_color': 'transparent',
        'hover_color': COLORS['bg_secondary'],
        'text_color': COLORS['primary'],
        'border_width': 2,
        'border_color': COLORS['primary'],
        'corner_radius': 12,
        'height': 45,
    },
    'success': {
        'fg_color': COLORS['success'],
        'hover_color': '#059669',
        'text_color': COLORS['text_light'],
        'border_width': 0,
        'corner_radius': 12,
        'height': 45,
    },
    'danger': {
        'fg_color': COLORS['error'],
        'hover_color': '#DC2626',
        'text_color': COLORS['text_light'],
        'border_width': 0,
        'corner_radius': 12,
        'height': 40,
    },
    'warning': {
        'fg_color': COLORS['warning'],
        'hover_color': '#D97706',
        'text_color': COLORS['text_light'],
        'border_width': 0,
        'corner_radius': 12,
        'height': 40,
    },
    'info': {
        'fg_color': COLORS['info'],
        'hover_color': '#2563EB',
        'text_color': COLORS['text_light'],
        'border_width': 0,
        'corner_radius': 12,
        'height': 40,
    }
}

# أنماط الإدخال
INPUT_STYLES = {
    'default': {
        'fg_color': COLORS['bg_card'],
        'border_color': COLORS['border'],
        'text_color': COLORS['text_primary'],
        'placeholder_text_color': COLORS['text_muted'],
        'corner_radius': 8,
        'border_width': 1,
        'justify': 'right',  # محاذاة النص لليمين للعربية
    },
    'focus': {
        'border_color': COLORS['border_focus'],
        'border_width': 2,
    },
    'rtl': {
        'fg_color': COLORS['bg_card'],
        'border_color': COLORS['border'],
        'text_color': COLORS['text_primary'],
        'placeholder_text_color': COLORS['text_muted'],
        'corner_radius': 8,
        'border_width': 1,
        'justify': 'right',  # محاذاة النص لليمين
    }
}

# أنماط البطاقات
CARD_STYLES = {
    'default': {
        'fg_color': COLORS['bg_card'],
        'corner_radius': 12,
        'border_width': 1,
        'border_color': COLORS['border'],
    },
    'elevated': {
        'fg_color': COLORS['bg_card'],
        'corner_radius': 12,
        'border_width': 0,
        # سيتم إضافة الظل في الكود
    }
}

# أنماط النصوص العربية مع دعم RTL
ARABIC_TEXT_STYLES = {
    'title': {
        'anchor': 'center',
        'justify': 'center',
    },
    'subtitle': {
        'anchor': 'center',
        'justify': 'center',
    },
    'label': {
        'anchor': 'e',  # الربط من الشرق (اليمين)
        'justify': 'right',
    },
    'button': {
        'anchor': 'center',
    },
    'entry': {
        'justify': 'right',
    },
    'header': {
        'anchor': 'e',
        'justify': 'right',
    }
}
