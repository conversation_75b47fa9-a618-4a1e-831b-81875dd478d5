#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح إضافة الحسابات الجديدة
"""

import sys
import os
import mysql.connector
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return None, None

def test_account_model_directly():
    """اختبار نموذج Account مباشرة"""
    print("🧪 اختبار نموذج Account مباشرة...")
    
    try:
        # استيراد النماذج
        from database.models import Account
        from utils.auth import auth_manager
        
        # تسجيل دخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("   ✅ تم تسجيل الدخول بنجاح")
        
        user_id = auth_manager.current_user['id']
        
        # إنشاء حساب تجريبي
        account_name = f"حساب تجريبي - {datetime.now().strftime('%H:%M:%S')}"
        
        print(f"   🔍 إنشاء حساب: {account_name}")
        
        # إنشاء الحساب بدون currency_id
        account_id = Account.create(
            user_id=user_id,
            name=account_name,
            description="حساب تجريبي للاختبار"
        )
        
        if account_id > 0:
            print(f"   ✅ تم إنشاء الحساب بنجاح (ID: {account_id})")
            
            # إضافة رصيد بالريال السعودي
            print("   🔍 إضافة رصيد بالريال السعودي...")
            
            balance_result = Account.add_currency_balance(account_id, 1, 1000.0)
            if balance_result:
                print("      ✅ تم إضافة الرصيد بنجاح")
                
                # فحص الرصيد
                balance = Account.get_currency_balance(account_id, 1)
                print(f"      💰 الرصيد: {balance:,.2f} ر.س")
            else:
                print("      ❌ فشل في إضافة الرصيد")
            
            # إضافة رصيد بالدرهم الإماراتي
            print("   🔍 إضافة رصيد بالدرهم الإماراتي...")
            
            aed_result = Account.add_currency_balance(account_id, 3, 500.0)
            if aed_result:
                print("      ✅ تم إضافة الرصيد بالدرهم الإماراتي")
                
                # فحص الرصيد
                aed_balance = Account.get_currency_balance(account_id, 3)
                print(f"      💰 الرصيد: {aed_balance:,.2f} د.إ")
            else:
                print("      ❌ فشل في إضافة الرصيد بالدرهم الإماراتي")
            
            # حذف الحساب التجريبي
            connection, cursor = connect_to_database()
            if connection:
                cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
                cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
                connection.commit()
                cursor.close()
                connection.close()
                print("   🗑️ تم حذف الحساب التجريبي")
            
            return True
        else:
            print("   ❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_account_creation_simulation():
    """محاكاة إنشاء حساب من الواجهة"""
    print("\n🧪 محاكاة إنشاء حساب من الواجهة...")
    
    try:
        # استيراد المكونات المطلوبة
        from utils.auth import auth_manager
        from gui.main_window import MainWindow
        import customtkinter as ctk
        import tkinter.messagebox as messagebox
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("   ✅ تم تسجيل الدخول بنجاح")
        
        # إنشاء نافذة التطبيق (مخفية)
        root = ctk.CTk()
        root.withdraw()
        
        # إنشاء نافذة إدارة الأموال
        app = MainWindow(root)
        
        # محاكاة عناصر الواجهة
        class MockEntry:
            def __init__(self, value):
                self.value = value
            def get(self):
                return self.value
        
        class MockCombo:
            def __init__(self, value):
                self.value = value
            def get(self):
                return self.value
        
        class MockText:
            def __init__(self, value):
                self.value = value
            def get(self, start, end):
                return self.value
        
        class MockDialog:
            def destroy(self):
                pass
        
        # إنشاء عناصر وهمية
        mock_dialog = MockDialog()
        mock_name_entry = MockEntry("حساب تجريبي من الواجهة")
        mock_currency_combo = MockCombo("1 - الريال السعودي")
        mock_balance_entry = MockEntry("750.0")
        mock_desc_entry = MockText("حساب تجريبي للاختبار من الواجهة")
        
        # تسجيل رسائل النجاح والخطأ
        success_called = False
        error_called = False
        error_message = ""
        
        original_showinfo = messagebox.showinfo
        original_showerror = messagebox.showerror
        
        def mock_showinfo(title, message):
            nonlocal success_called
            success_called = True
            print(f"      ✅ رسالة نجاح: {message}")
        
        def mock_showerror(title, message):
            nonlocal error_called, error_message
            error_called = True
            error_message = message
            print(f"      ❌ رسالة خطأ: {message}")
        
        messagebox.showinfo = mock_showinfo
        messagebox.showerror = mock_showerror
        
        # اختبار دالة save_new_account المصححة
        print("   🔍 اختبار دالة save_new_account المصححة...")
        
        try:
            app.save_new_account(mock_dialog, mock_name_entry, mock_currency_combo, mock_balance_entry, mock_desc_entry)
            
            if success_called:
                print("   ✅ تم إنشاء الحساب بنجاح من الواجهة")
                result = True
            elif error_called:
                print(f"   ❌ فشل في إنشاء الحساب: {error_message}")
                result = False
            else:
                print("   ⚠️ لم يتم استدعاء أي رسالة")
                result = False
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الواجهة: {e}")
            import traceback
            traceback.print_exc()
            result = False
        
        # استعادة الدوال الأصلية
        messagebox.showinfo = original_showinfo
        messagebox.showerror = original_showerror
        
        # تنظيف
        root.destroy()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في محاكاة الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_currency_account_creation():
    """اختبار إنشاء حسابات بعملات مختلفة"""
    print("\n🧪 اختبار إنشاء حسابات بعملات مختلفة...")
    
    try:
        from database.models import Account
        from utils.auth import auth_manager
        
        # تسجيل دخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        
        # العملات للاختبار
        currencies = [
            (1, "SAR", "ريال سعودي", 1000.0),
            (2, "AED", "درهم إماراتي", 500.0),
            (3, "YER", "ريال يمني", 25000.0),
            (4, "USD", "دولار أمريكي", 200.0)
        ]
        
        created_accounts = []
        
        for currency_id, currency_code, currency_name, initial_balance in currencies:
            account_name = f"حساب تجريبي {currency_code} - {datetime.now().strftime('%H:%M:%S')}"
            
            print(f"   🔍 إنشاء حساب {currency_name}: {account_name}")
            
            # إنشاء الحساب
            account_id = Account.create(
                user_id=user_id,
                name=account_name,
                description=f"حساب تجريبي بعملة {currency_name}"
            )
            
            if account_id > 0:
                print(f"      ✅ تم إنشاء الحساب (ID: {account_id})")
                
                # إضافة الرصيد
                balance_result = Account.add_currency_balance(account_id, currency_id, initial_balance)
                if balance_result:
                    print(f"      ✅ تم إضافة رصيد: {initial_balance:,.2f} {currency_code}")
                    created_accounts.append(account_id)
                else:
                    print(f"      ❌ فشل في إضافة الرصيد")
            else:
                print(f"      ❌ فشل في إنشاء الحساب")
        
        # حذف الحسابات التجريبية
        if created_accounts:
            connection, cursor = connect_to_database()
            if connection:
                for account_id in created_accounts:
                    cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
                    cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
                
                connection.commit()
                cursor.close()
                connection.close()
                print(f"   🗑️ تم حذف {len(created_accounts)} حساب تجريبي")
        
        return len(created_accounts) == len(currencies)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملات المتعددة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار إصلاح إضافة الحسابات الجديدة")
    print("="*60)
    
    tests = [
        ("اختبار نموذج Account مباشرة", test_account_model_directly),
        ("محاكاة إنشاء حساب من الواجهة", test_gui_account_creation_simulation),
        ("اختبار إنشاء حسابات بعملات مختلفة", test_multi_currency_account_creation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ تم إصلاح مشكلة إضافة الحسابات بنجاح")
        
        print("\n💡 الإصلاحات المطبقة:")
        print("   ✅ إزالة معامل currency_id من دالة Account.create")
        print("   ✅ استخدام Account.add_currency_balance لإضافة الأرصدة")
        print("   ✅ دعم إضافة حسابات بجميع العملات الأربع")
        print("   ✅ تحديث لوحة التحكم عند إضافة حسابات جديدة")
        
        print("\n🚀 اختبر الآن:")
        print("   1. شغّل التطبيق: python main.py")
        print("   2. سجل الدخول: admin / 123456")
        print("   3. اذهب إلى 'الحسابات'")
        print("   4. اضغط 'إضافة حساب جديد'")
        print("   5. املأ البيانات واختر العملة")
        print("   6. اضغط 'حفظ'")
        print("   7. تحقق من ظهور الحساب الجديد")
        
    else:
        print(f"\n⚠️ {total_tests - passed_tests} اختبارات فشلت")
        print("💡 تأكد من:")
        print("   - تشغيل XAMPP أو MySQL Server")
        print("   - صحة كلمة مرور قاعدة البيانات (mohdam)")
        print("   - وجود قاعدة البيانات money_manager")

if __name__ == "__main__":
    main()
