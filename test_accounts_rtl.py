#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات RTL في نافذة الحسابات
يختبر عرض النصوص العربية في نافذة إدارة الحسابات
"""

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button
from config.colors import COLORS, ARABIC_TEXT_STYLES, BUTTON_STYLES, CARD_STYLES

class AccountsRTLTestWindow:
    """نافذة اختبار إصلاحات RTL في نافذة الحسابات"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_test_interface()
    
    def setup_window(self):
        """إعداد النافذة"""
        ctk.set_appearance_mode("light")
        
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاحات RTL - نافذة الحسابات")
        self.window.geometry("900x700")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 900
        height = 700
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # العنوان الرئيسي
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاحات RTL - نافذة الحسابات",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 10))
        
        # وصف الاختبار
        desc_label = create_rtl_label(
            self.window,
            text="هذا الاختبار يحاكي نافذة إدارة الحسابات مع النصوص العربية بـ RTL",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))
        
        # إطار المحتوى الرئيسي
        content_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_card'])
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # عنوان الصفحة
        title_label = create_rtl_label(
            content_frame,
            text="إدارة الحسابات",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))
        
        # زر إضافة حساب جديد
        add_button = create_rtl_button(
            content_frame,
            text="+ إضافة حساب جديد",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        add_button.pack(pady=20)
        
        # إطار قائمة الحسابات
        accounts_frame = ctk.CTkScrollableFrame(
            content_frame,
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        accounts_frame.pack(fill="both", expand=True, padx=30, pady=(0, 30))
        
        # إنشاء بطاقات حسابات تجريبية
        self.create_test_account_cards(accounts_frame)
    
    def create_test_account_cards(self, parent):
        """إنشاء بطاقات حسابات تجريبية"""
        # بيانات حسابات تجريبية
        test_accounts = [
            {
                'name': 'البنك الأهلي السعودي',
                'account_type_name': 'حساب جاري',
                'balances': [
                    {'balance': 15250.75, 'symbol': 'ر.س'},
                    {'balance': 1200.50, 'symbol': '$'}
                ],
                'description': 'الحساب الرئيسي للراتب والمصروفات الشهرية',
                'is_active': True
            },
            {
                'name': 'محفظة نقدية',
                'account_type_name': 'نقدي',
                'balances': [
                    {'balance': 850.00, 'symbol': 'ر.س'}
                ],
                'description': 'المحفظة النقدية للمصروفات اليومية',
                'is_active': True
            },
            {
                'name': 'حساب التوفير',
                'account_type_name': 'توفير',
                'balances': [
                    {'balance': 25000.00, 'symbol': 'ر.س'}
                ],
                'description': 'حساب التوفير طويل المدى',
                'is_active': False
            }
        ]
        
        for account in test_accounts:
            self.create_test_account_card(parent, account)
    
    def create_test_account_card(self, parent, account):
        """إنشاء بطاقة حساب تجريبية"""
        card = ctk.CTkFrame(
            parent,
            **CARD_STYLES['elevated']
        )
        card.pack(fill="x", padx=20, pady=10)
        
        # معلومات الحساب
        info_frame = ctk.CTkFrame(card, fg_color="transparent")
        info_frame.pack(fill="x", padx=20, pady=15)
        
        # اسم الحساب ونوعه
        name_label = create_rtl_label(
            info_frame,
            text=account['name'],
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w")
        
        type_label = create_rtl_label(
            info_frame,
            text=f"النوع: {account['account_type_name']}",
            font_size='small',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        type_label.pack(anchor="w")
        
        # إطار عرض الأرصدة
        balances_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        balances_frame.pack(anchor="w", fill="x", pady=(5, 0))
        
        if not account.get('balances'):
            balance_label = create_rtl_label(
                balances_frame,
                text="الرصيد: 0.00",
                font_size='body',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            balance_label.pack(anchor="w")
        else:
            # عرض كل رصيد في سطر منفصل
            for balance in account['balances']:
                balance_color = COLORS['success'] if balance['balance'] >= 0 else COLORS['error']
                balance_label = create_rtl_label(
                    balances_frame,
                    text=f"- {balance['balance']:,.2f} {balance['symbol']}",
                    font_size='body',
                    text_color=balance_color,
                    **ARABIC_TEXT_STYLES['label']
                )
                balance_label.pack(anchor="w")
        
        # الوصف إذا وجد
        if account.get('description'):
            desc_label = create_rtl_label(
                info_frame,
                text=account['description'],
                font_size='small',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            desc_label.pack(anchor="w", pady=(2, 0))
        
        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(info_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=(10, 0))
        
        # زر التعديل
        edit_button = create_rtl_button(
            buttons_frame,
            text="✏️ تعديل",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        edit_button.pack(side="left", padx=(0, 5))
        
        # زر التفعيل/إلغاء التفعيل
        if account.get('is_active', True):
            toggle_button = create_rtl_button(
                buttons_frame,
                text="🚫 إلغاء تفعيل",
                command=self.show_test_message,
                **BUTTON_STYLES['secondary']
            )
        else:
            toggle_button = create_rtl_button(
                buttons_frame,
                text="✅ تفعيل",
                command=self.show_test_message,
                **BUTTON_STYLES['primary']
            )
        toggle_button.pack(side="left", padx=5)
        
        # زر الحذف
        delete_button = create_rtl_button(
            buttons_frame,
            text="🗑️ حذف",
            command=self.show_test_message,
            **BUTTON_STYLES['danger']
        )
        delete_button.pack(side="right")
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        from tkinter import messagebox
        messagebox.showinfo("اختبار الزر", "تم النقر على الزر! النصوص العربية تظهر بـ RTL صحيح.")
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار إصلاحات RTL في نافذة الحسابات...")
    print("=" * 60)
    print("📋 ما يتم اختباره:")
    print("   ✅ عرض النصوص العربية بـ RTL في عنوان الصفحة")
    print("   ✅ عرض النصوص العربية بـ RTL في أزرار الإجراءات")
    print("   ✅ عرض النصوص العربية بـ RTL في بطاقات الحسابات")
    print("   ✅ محاذاة أسماء الحسابات والأنواع والأرصدة")
    print("   ✅ محاذاة الأوصاف والتفاصيل")
    print("   ✅ محاذاة أزرار التعديل والحذف والتفعيل")
    print("=" * 60)
    
    try:
        app = AccountsRTLTestWindow()
        app.run()
        print("✅ تم إنهاء الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
