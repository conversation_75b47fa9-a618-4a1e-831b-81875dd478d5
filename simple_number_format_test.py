#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لتنسيق الأرقام
"""

def test_number_formatting():
    """اختبار تنسيق الأرقام"""
    print("🧪 اختبار تنسيق الأرقام في التقارير")
    print("=" * 50)
    
    # أمثلة على الأرقام
    test_numbers = [
        1234.56,
        1000000.789,
        0.12,
        999999.99,
        12345678.123,
        500296960.00,
        110001568050.00
    ]
    
    print("🔢 اختبار التنسيق المستخدم في التقارير:")
    print()
    
    for num in test_numbers:
        # التنسيق المستخدم في التقارير
        formatted_2_decimal = f"{float(num):,.2f}"
        formatted_0_decimal = f"{float(num):,.0f}"
        
        print(f"الرقم الأصلي: {num}")
        print(f"  تنسيق بعلامتين عشريتين: {formatted_2_decimal}")
        print(f"  تنسيق بدون علامات عشرية: {formatted_0_decimal}")
        
        # فحص وجود أرقام عربية
        if '،' in formatted_2_decimal or '،' in formatted_0_decimal:
            print("  ❌ يحتوي على فاصلة عربية")
        else:
            print("  ✅ تنسيق إنجليزي صحيح")
        
        print()
    
    print("=" * 50)
    print("📊 النتائج:")
    print("✅ جميع الأرقام تستخدم التنسيق الإنجليزي")
    print("✅ الفواصل الإنجليزية (,) للآلاف")
    print("✅ النقاط العشرية (.) للكسور")
    print("✅ لا توجد فواصل عربية (،)")
    
    print("\n🎯 التنسيق المستخدم في الكود:")
    print("• للأرصدة: f\"{float(balance):,.2f}\"")
    print("• للمعاملات الشهرية: f\"{float(amount):,.2f}\" أو f\"{float(amount):,.0f}\"")
    print("• لتقرير الحسابات: f\"{float(balance):,.2f}\"")
    
    print("\n📋 هذا التنسيق صحيح ولا يحتاج تعديل!")

if __name__ == "__main__":
    test_number_formatting()
