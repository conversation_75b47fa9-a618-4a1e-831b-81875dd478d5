#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات النصوص العربية ودعم RTL
يختبر عرض النصوص العربية بالاتجاه الصحيح من اليمين إلى اليسار
"""

import customtkinter as ctk
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config.colors import COLORS, BUTTON_STYLES, INPUT_STYLES, ARABIC_TEXT_STYLES
    from config.settings import APP_CONFIG, ARABIC_FONT_CONFIG
    from config.fonts import (
        get_title_font, get_subtitle_font, get_header_font, get_body_font,
        create_rtl_label, create_rtl_button, create_rtl_entry, print_font_info
    )
    print("✅ تم تحميل إعدادات الخطوط العربية بنجاح")
    print_font_info()
except ImportError as e:
    print(f"❌ خطأ في تحميل إعدادات الخطوط: {e}")
    sys.exit(1)

class ArabicRTLTestWindow:
    """نافذة اختبار النصوص العربية مع دعم RTL"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_test_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        self.window = ctk.CTk()
        self.window.title("اختبار النصوص العربية - دعم RTL")
        self.window.geometry("800x600")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = 800
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_widgets(self):
        """إنشاء عناصر الاختبار"""
        # الحاوية الرئيسية
        main_frame = ctk.CTkScrollableFrame(
            self.window,
            fg_color=COLORS['bg_card'],
            corner_radius=20
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_label = create_rtl_label(
            main_frame,
            text="🧪 اختبار النصوص العربية مع دعم RTL",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))
        
        # العنوان الفرعي
        subtitle_label = create_rtl_label(
            main_frame,
            text="هذا اختبار لعرض النصوص العربية بالاتجاه الصحيح من اليمين إلى اليسار",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['subtitle']
        )
        subtitle_label.pack(pady=(0, 30))
        
        # قسم اختبار التسميات
        self.create_labels_test_section(main_frame)
        
        # قسم اختبار الأزرار
        self.create_buttons_test_section(main_frame)
        
        # قسم اختبار حقول الإدخال
        self.create_entries_test_section(main_frame)
        
        # قسم اختبار النصوص المختلطة
        self.create_mixed_text_section(main_frame)
    
    def create_labels_test_section(self, parent):
        """قسم اختبار التسميات"""
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        section_frame.pack(fill="x", padx=20, pady=10)
        
        section_title = create_rtl_label(
            section_frame,
            text="📝 اختبار التسميات (Labels)",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['header']
        )
        section_title.pack(pady=15)
        
        # تسميات بأحجام مختلفة
        labels_data = [
            ("عنوان كبير", 'title'),
            ("عنوان فرعي", 'subtitle'),
            ("رأس قسم", 'header'),
            ("نص عادي", 'body'),
            ("نص صغير", 'small')
        ]
        
        for text, size in labels_data:
            label = create_rtl_label(
                section_frame,
                text=f"{text} - حجم {size}",
                font_size=size,
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            label.pack(fill="x", padx=20, pady=2)
    
    def create_buttons_test_section(self, parent):
        """قسم اختبار الأزرار"""
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        section_frame.pack(fill="x", padx=20, pady=10)
        
        section_title = create_rtl_label(
            section_frame,
            text="🔘 اختبار الأزرار (Buttons)",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['header']
        )
        section_title.pack(pady=15)
        
        # أزرار بأنماط مختلفة
        buttons_frame = ctk.CTkFrame(section_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)
        
        primary_btn = create_rtl_button(
            buttons_frame,
            text="زر أساسي",
            command=lambda: self.show_message("تم النقر على الزر الأساسي"),
            **BUTTON_STYLES['primary']
        )
        primary_btn.pack(fill="x", pady=5)
        
        secondary_btn = create_rtl_button(
            buttons_frame,
            text="زر ثانوي",
            command=lambda: self.show_message("تم النقر على الزر الثانوي"),
            **BUTTON_STYLES['secondary']
        )
        secondary_btn.pack(fill="x", pady=5)
    
    def create_entries_test_section(self, parent):
        """قسم اختبار حقول الإدخال"""
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        section_frame.pack(fill="x", padx=20, pady=10)
        
        section_title = create_rtl_label(
            section_frame,
            text="✏️ اختبار حقول الإدخال (Entry Fields)",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['header']
        )
        section_title.pack(pady=15)
        
        entries_frame = ctk.CTkFrame(section_frame, fg_color="transparent")
        entries_frame.pack(fill="x", padx=20, pady=10)
        
        # حقل نص عادي
        text_label = create_rtl_label(
            entries_frame,
            text="اكتب نصاً عربياً:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        text_label.pack(fill="x", pady=(0, 5))
        
        text_entry = create_rtl_entry(
            entries_frame,
            placeholder_text="أدخل نصاً عربياً هنا...",
            **INPUT_STYLES['rtl']
        )
        text_entry.pack(fill="x", pady=(0, 15))
        
        # حقل كلمة مرور
        password_label = create_rtl_label(
            entries_frame,
            text="كلمة المرور:",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        password_label.pack(fill="x", pady=(0, 5))
        
        password_entry = create_rtl_entry(
            entries_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            **INPUT_STYLES['rtl']
        )
        password_entry.pack(fill="x", pady=(0, 15))
    
    def create_mixed_text_section(self, parent):
        """قسم اختبار النصوص المختلطة"""
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        section_frame.pack(fill="x", padx=20, pady=10)
        
        section_title = create_rtl_label(
            section_frame,
            text="🔤 اختبار النصوص المختلطة (عربي + English + 123)",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['header']
        )
        section_title.pack(pady=15)
        
        mixed_texts = [
            "النص العربي مع English text و أرقام 123",
            "المبلغ: 1,500.50 ريال سعودي",
            "البريد الإلكتروني: <EMAIL>",
            "التاريخ: 2024/01/15 - الساعة 14:30",
            "رقم الهاتف: +966 50 123 4567"
        ]
        
        for text in mixed_texts:
            label = create_rtl_label(
                section_frame,
                text=text,
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            label.pack(fill="x", padx=20, pady=2)
    
    def show_message(self, message):
        """عرض رسالة اختبار"""
        from tkinter import messagebox
        messagebox.showinfo("اختبار الزر", message)
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النصوص العربية مع دعم RTL...")
    
    try:
        app = ArabicRTLTestWindow()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
