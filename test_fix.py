#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 اختبار إصلاح مشكلة إدارة قاعدة البيانات...")

try:
    print("1. اختبار استيراد الأنماط...")
    from config.colors import BUTTON_STYLES
    print(f"✅ تم استيراد BUTTON_STYLES: {list(BUTTON_STYLES.keys())}")
    
    print("\n2. اختبار استيراد customtkinter...")
    import customtkinter as ctk
    print("✅ تم استيراد customtkinter")
    
    print("\n3. اختبار استيراد create_rtl_button...")
    from config.fonts import create_rtl_button
    print("✅ تم استيراد create_rtl_button")
    
    print("\n4. اختبار إنشاء زر...")
    test_window = ctk.CTk()
    test_window.withdraw()
    test_frame = ctk.CTkFrame(test_window)
    
    test_button = create_rtl_button(
        test_frame,
        text="اختبار",
        **BUTTON_STYLES['primary']
    )
    print("✅ تم إنشاء زر بنجاح")
    
    test_window.destroy()
    
    print("\n5. اختبار استيراد MainWindow...")
    from gui.main_window import MainWindow
    print("✅ تم استيراد MainWindow")
    
    print("\n🎉 جميع الاختبارات الأساسية نجحت!")
    print("المشكلة تم إصلاحها بنجاح.")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
