#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات على نافذة تعديل المستخدم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_edit_user_functionality():
    """اختبار وظائف نافذة تعديل المستخدم المحسنة"""
    print("🚀 اختبار التحسينات على نافذة تعديل المستخدم")
    print("=" * 60)
    
    try:
        # 1. اختبار تسجيل الدخول
        print("1. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        # 2. إنشاء مستخدم تجريبي للاختبار
        print("\n2. إنشاء مستخدم تجريبي للاختبار...")
        success, result = auth_manager.register_user(
            username="test_edit_user",
            password="original123",
            full_name="مستخدم اختبار التعديل",
            role="user"
        )
        
        if success:
            print("✅ تم إنشاء المستخدم التجريبي")
        else:
            print(f"❌ فشل إنشاء المستخدم التجريبي: {result}")
            return False
        
        # 3. اختبار تحديث اسم المستخدم
        print("\n3. اختبار تحديث اسم المستخدم...")
        from database.models import User
        
        test_user = User.get_by_username("test_edit_user")
        if test_user:
            success, message = auth_manager.update_user_data(
                user_id=test_user['id'],
                username="test_edit_user_updated"
            )
            
            if success:
                print("✅ تم تحديث اسم المستخدم بنجاح")
                
                # التحقق من التحديث
                updated_user = User.get_by_id(test_user['id'])
                if updated_user['username'] == "test_edit_user_updated":
                    print("✅ تم التحقق من تحديث اسم المستخدم")
                else:
                    print("❌ فشل في التحقق من تحديث اسم المستخدم")
                    return False
            else:
                print(f"❌ فشل تحديث اسم المستخدم: {message}")
                return False
        
        # 4. اختبار منع تكرار اسم المستخدم
        print("\n4. اختبار منع تكرار اسم المستخدم...")
        success, message = auth_manager.update_user_data(
            user_id=test_user['id'],
            username="admin2"  # محاولة استخدام اسم موجود
        )
        
        if not success and "موجود مسبقاً" in message:
            print("✅ تم منع تكرار اسم المستخدم بنجاح")
        else:
            print("❌ لم يتم منع تكرار اسم المستخدم")
            return False
        
        # 5. اختبار تحديث كلمة المرور
        print("\n5. اختبار تحديث كلمة المرور...")
        success, message = auth_manager.reset_user_password(
            test_user['id'],
            "new_password123"
        )
        
        if success:
            print("✅ تم تحديث كلمة المرور بنجاح")
        else:
            print(f"❌ فشل تحديث كلمة المرور: {message}")
            return False
        
        # 6. اختبار تحديث البيانات الأخرى
        print("\n6. اختبار تحديث البيانات الأخرى...")
        success, message = auth_manager.update_user_data(
            user_id=test_user['id'],
            full_name="مستخدم اختبار التعديل المحدث",
            role="admin",
            is_active=True
        )
        
        if success:
            print("✅ تم تحديث البيانات الأخرى بنجاح")
        else:
            print(f"❌ فشل تحديث البيانات الأخرى: {message}")
            return False
        
        # 7. اختبار استيراد نافذة التعديل المحسنة
        print("\n7. اختبار استيراد نافذة التعديل المحسنة...")
        try:
            from gui.user_management_windows import EditUserWindow
            print("✅ تم استيراد EditUserWindow المحسنة بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد EditUserWindow: {e}")
            return False
        
        # 8. اختبار إنشاء نافذة التعديل (بدون عرض)
        print("\n8. اختبار إنشاء نافذة التعديل...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة مؤقتة للاختبار
            test_window = ctk.CTk()
            test_window.withdraw()  # إخفاء النافذة
            
            # الحصول على بيانات المستخدم المحدثة
            updated_user = User.get_by_id(test_user['id'])
            
            # إنشاء نافذة التعديل (بدون عرض)
            edit_window = EditUserWindow(test_window, updated_user)
            edit_window.window.withdraw()  # إخفاء النافذة
            
            # التحقق من وجود الحقول الجديدة
            if hasattr(edit_window, 'username_entry'):
                print("✅ حقل اسم المستخدم موجود")
            else:
                print("❌ حقل اسم المستخدم مفقود")
                return False
            
            if hasattr(edit_window, 'new_password_entry'):
                print("✅ حقل كلمة المرور الجديدة موجود")
            else:
                print("❌ حقل كلمة المرور الجديدة مفقود")
                return False
            
            if hasattr(edit_window, 'confirm_new_password_entry'):
                print("✅ حقل تأكيد كلمة المرور موجود")
            else:
                print("❌ حقل تأكيد كلمة المرور مفقود")
                return False
            
            # إغلاق النوافذ التجريبية
            edit_window.window.destroy()
            test_window.destroy()
            
            print("✅ تم إنشاء نافذة التعديل المحسنة بنجاح")
            
        except Exception as e:
            print(f"❌ فشل إنشاء نافذة التعديل: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 9. تنظيف البيانات التجريبية
        print("\n9. تنظيف البيانات التجريبية...")
        from database.connection import db
        db.execute_update("DELETE FROM users WHERE username = 'test_edit_user_updated'", ())
        print("✅ تم تنظيف البيانات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحسينات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_functions():
    """اختبار دوال التحقق المحسنة"""
    print("\n🔍 اختبار دوال التحقق المحسنة")
    print("=" * 40)
    
    try:
        # اختبار regex للتحقق من اسم المستخدم
        import re
        
        # أسماء مستخدمين صحيحة
        valid_usernames = ["user123", "admin_user", "test_123", "user_name_123"]
        for username in valid_usernames:
            if re.match("^[a-zA-Z0-9_]+$", username):
                print(f"✅ اسم المستخدم صحيح: {username}")
            else:
                print(f"❌ اسم المستخدم خاطئ: {username}")
                return False
        
        # أسماء مستخدمين خاطئة
        invalid_usernames = ["user-123", "user@name", "user name", "user.name", "مستخدم"]
        for username in invalid_usernames:
            if not re.match("^[a-zA-Z0-9_]+$", username):
                print(f"✅ تم رفض اسم المستخدم الخاطئ: {username}")
            else:
                print(f"❌ لم يتم رفض اسم المستخدم الخاطئ: {username}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل للتحسينات على نافذة تعديل المستخدم")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار الوظائف المحسنة
    if test_enhanced_edit_user_functionality():
        tests_passed += 1
        print("\n✅ اختبار الوظائف المحسنة نجح")
    else:
        print("\n❌ اختبار الوظائف المحسنة فشل")
    
    # اختبار دوال التحقق
    if test_validation_functions():
        tests_passed += 1
        print("\n✅ اختبار دوال التحقق نجح")
    else:
        print("\n❌ اختبار دوال التحقق فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع التحسينات تعمل بشكل صحيح!")
        print("\n📋 الميزات الجديدة:")
        print("✅ تعديل اسم المستخدم مع التحقق من عدم التكرار")
        print("✅ تغيير كلمة المرور الاختياري")
        print("✅ تحسين واجهة المستخدم مع إطار قابل للتمرير")
        print("✅ تحسين دوال التحقق والأمان")
        print("✅ مربعات حوار تأكيد للتغييرات الحساسة")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
