#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار لوحة التحكم متعددة العملات
"""

import sys
import os
import mysql.connector
from mysql.connector import Error

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def test_new_dashboard_stats(cursor):
    """اختبار دالة الإحصائيات الجديدة"""
    print("🏠 اختبار دالة الإحصائيات الجديدة...")
    
    user_id = 1
    current_month = "2025-01"
    
    try:
        # الأرصدة بجميع العملات
        balance_query = """
            SELECT 
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id
            WHERE c.is_active = TRUE
            AND (a.user_id = %s AND a.is_active = TRUE OR a.id IS NULL)
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING total_balance > 0
            ORDER BY total_balance DESC
        """
        cursor.execute(balance_query, (user_id,))
        balance_results = cursor.fetchall()
        
        print(f"💰 الأرصدة بجميع العملات ({len(balance_results)} عملة):")
        for balance in balance_results:
            print(f"   - {balance['name']} ({balance['code']}): {balance['total_balance']:,.2f} {balance['symbol']}")

        # المعاملات الشهرية بجميع العملات
        transactions_query = """
            SELECT 
                c.code,
                c.symbol,
                t.type,
                COALESCE(SUM(t.amount), 0) as total_amount
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id 
                AND t.user_id = %s 
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol, t.type
            HAVING total_amount > 0
            ORDER BY c.code, t.type
        """
        cursor.execute(transactions_query, (user_id, current_month))
        transactions_results = cursor.fetchall()

        print(f"\n📊 المعاملات الشهرية ({len(transactions_results)} نوع معاملة):")
        for trans in transactions_results:
            type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
            print(f"   - {trans['code']} {type_text}: {trans['total_amount']:,.2f} {trans['symbol']}")

        # عدد الحسابات
        accounts_query = """
            SELECT COUNT(*) as accounts_count
            FROM accounts
            WHERE user_id = %s AND is_active = TRUE
        """
        cursor.execute(accounts_query, (user_id,))
        accounts_result = cursor.fetchone()
        accounts_count = accounts_result['accounts_count'] if accounts_result else 0

        print(f"\n🏦 عدد الحسابات: {accounts_count}")

        return {
            'currency_balances': balance_results or [],
            'currency_transactions': transactions_results or [],
            'accounts_count': accounts_count
        }

    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {e}")
        return None

def simulate_dashboard_display(stats):
    """محاكاة عرض لوحة التحكم"""
    print("\n🖥️ محاكاة عرض لوحة التحكم:")
    print("="*50)
    
    # عرض عدد الحسابات
    print(f"🏦 عدد الحسابات: {stats['accounts_count']}")
    
    # عرض الأرصدة
    print("\n💰 الأرصدة الحالية:")
    if stats['currency_balances']:
        for balance in stats['currency_balances']:
            if balance['total_balance'] > 0:
                print(f"   📊 {balance['name']} ({balance['code']}): {balance['total_balance']:,.0f} {balance['symbol']}")
    else:
        print("   ⚠️ لا توجد أرصدة")
    
    # عرض المعاملات الشهرية
    print("\n📈 المعاملات الشهرية:")
    if stats['currency_transactions']:
        # تجميع المعاملات حسب العملة
        currency_groups = {}
        for trans in stats['currency_transactions']:
            currency_key = trans['code']
            if currency_key not in currency_groups:
                currency_groups[currency_key] = {'symbol': trans['symbol'], 'income': 0, 'expense': 0}
            
            if trans['type'] == 'income':
                currency_groups[currency_key]['income'] = trans['total_amount']
            elif trans['type'] == 'expense':
                currency_groups[currency_key]['expense'] = trans['total_amount']
        
        for currency_code, data in currency_groups.items():
            if data['income'] > 0 or data['expense'] > 0:
                print(f"   💱 {currency_code}:")
                if data['income'] > 0:
                    print(f"      📈 الواردات: +{data['income']:,.0f} {data['symbol']}")
                if data['expense'] > 0:
                    print(f"      📉 المصروفات: -{data['expense']:,.0f} {data['symbol']}")
    else:
        print("   ⚠️ لا توجد معاملات في الشهر الحالي")
    
    print("="*50)

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("💱 اختبار لوحة التحكم متعددة العملات")
    print("="*60)
    
    # الاتصال بقاعدة البيانات
    connection, cursor = connect_to_database()
    
    if not connection:
        print("❌ لا يمكن المتابعة بدون اتصال بقاعدة البيانات")
        return
    
    try:
        # اختبار الدالة الجديدة
        stats = test_new_dashboard_stats(cursor)
        
        if stats:
            # محاكاة العرض
            simulate_dashboard_display(stats)
            
            print("\n✅ تم اختبار لوحة التحكم متعددة العملات بنجاح!")
            print("💡 يمكنك الآن تشغيل التطبيق لرؤية التحسينات:")
            print("   python main.py")
        else:
            print("❌ فشل في اختبار الإحصائيات")
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    main()
