#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح اتجاه النص العربي في تقرير الحسابات
"""

import customtkinter as ctk
from config.colors import COLORS
from config.fonts import create_rtl_label, ARABIC_TEXT_STYLES, CARD_STYLES

class AccountsReportRTLTest:
    """اختبار تقرير الحسابات مع دعم RTL"""
    
    def __init__(self):
        """تهيئة نافذة الاختبار"""
        self.window = ctk.CTk()
        self.window.title("اختبار تقرير الحسابات - RTL")
        self.window.geometry("800x600")
        self.window.configure(fg_color=COLORS['bg_primary'])
        
        self.create_test_interface()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        
        # العنوان الرئيسي
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاح تقرير الحسابات - RTL",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 10))
        
        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(
            self.window,
            fg_color=COLORS['bg_light'],
            corner_radius=15
        )
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # محاكاة تقرير الحسابات المحسن
        self.create_accounts_report_test(scroll_frame)
        
        # ملاحظة الاختبار
        note_label = create_rtl_label(
            self.window,
            text="✅ تم إصلاح: عنوان تقرير الحسابات، بيانات الحسابات، رسالة عدم الوجود",
            font_size='body',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['label']
        )
        note_label.pack(pady=(10, 20))
    
    def create_accounts_report_test(self, parent):
        """محاكاة تقرير الحسابات المحسن"""
        
        # عنوان التقرير (محسن)
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)
        
        title_label = create_rtl_label(
            title_frame,
            text="🏦 تقرير الحسابات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)
        
        # بيانات الحسابات التجريبية
        test_accounts = [
            {
                'name': 'البنك الأهلي',
                'type_name': 'حساب جاري',
                'balances_text': '15,000.00 ر.س'
            },
            {
                'name': 'حساب الدولار',
                'type_name': 'حساب توفير',
                'balances_text': '2,500.00 $'
            },
            {
                'name': 'بنك الإمارات',
                'type_name': 'حساب جاري',
                'balances_text': '8,000.00 د.إ'
            },
            {
                'name': 'البنك اليمني',
                'type_name': 'حساب توفير',
                'balances_text': '450,000.00 ر.ي'
            },
            {
                'name': 'محفظة نقدية',
                'type_name': 'نقد',
                'balances_text': '1,250.00 ر.س'
            }
        ]
        
        # إطار البيانات
        data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        data_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # عرض الحسابات مع RTL محسن
        for account in test_accounts:
            account_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
            account_frame.pack(fill="x", pady=2)
            
            balances_text = account['balances_text']
            account_info = create_rtl_label(
                account_frame,
                text=f"{account['name']} ({account['type_name']}): {balances_text}",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            account_info.pack(anchor="e", padx=10, pady=5)  # محاذاة RTL صحيحة
        
        # اختبار حالة عدم وجود حسابات
        self.create_empty_accounts_test(parent)
    
    def create_empty_accounts_test(self, parent):
        """اختبار حالة عدم وجود حسابات"""
        
        # عنوان الاختبار
        empty_title = create_rtl_label(
            parent,
            text="🧪 اختبار حالة عدم وجود حسابات:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        empty_title.pack(pady=(30, 10))
        
        # إطار تقرير فارغ
        empty_title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        empty_title_frame.pack(fill="x", padx=20, pady=10)
        
        # عنوان التقرير
        empty_report_title = create_rtl_label(
            empty_title_frame,
            text="🏦 تقرير الحسابات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        empty_report_title.pack(pady=15)
        
        # رسالة عدم وجود حسابات (الإضافة الجديدة)
        no_accounts_label = create_rtl_label(
            empty_title_frame,
            text="لا توجد حسابات مسجلة حتى الآن",
            font_size='body',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        no_accounts_label.pack(pady=20)
        
        # مقارنة قبل وبعد
        self.create_comparison_section(parent)
    
    def create_comparison_section(self, parent):
        """قسم مقارنة قبل وبعد الإصلاح"""
        
        # عنوان المقارنة
        comparison_title = create_rtl_label(
            parent,
            text="📊 مقارنة قبل وبعد الإصلاح:",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        comparison_title.pack(pady=(30, 15))
        
        # إطار المقارنة
        comparison_frame = ctk.CTkFrame(parent, fg_color="transparent")
        comparison_frame.pack(fill="x", padx=20)
        
        # قبل الإصلاح
        before_frame = ctk.CTkFrame(comparison_frame, **CARD_STYLES['elevated'])
        before_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        before_title = create_rtl_label(
            before_frame,
            text="❌ قبل الإصلاح (LTR)",
            font_size='header',
            text_color=COLORS['error'],
            **ARABIC_TEXT_STYLES['title']
        )
        before_title.pack(pady=15)
        
        before_desc = create_rtl_label(
            before_frame,
            text="• النص معكوس (LTR)\n• محاذاة غربية\n• لا توجد رسالة للحسابات الفارغة",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        before_desc.pack(padx=15, pady=(0, 15))
        
        # بعد الإصلاح
        after_frame = ctk.CTkFrame(comparison_frame, **CARD_STYLES['elevated'])
        after_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        after_title = create_rtl_label(
            after_frame,
            text="✅ بعد الإصلاح (RTL)",
            font_size='header',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['title']
        )
        after_title.pack(pady=15)
        
        after_desc = create_rtl_label(
            after_frame,
            text="• النص صحيح (RTL)\n• محاذاة شرقية\n• رسالة واضحة للحسابات الفارغة",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        after_desc.pack(padx=15, pady=(0, 15))
    
    def run(self):
        """تشغيل نافذة الاختبار"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار إصلاح تقرير الحسابات RTL...")
    
    try:
        app = AccountsReportRTLTest()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
