#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات RTL في نافذة الإعدادات
يختبر عرض النصوص العربية في نافذة الإعدادات فقط
"""

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, ARABIC_TEXT_STYLES, BUTTON_STYLES, CARD_STYLES

class SettingsWindowRTLTest:
    """نافذة اختبار إصلاحات RTL في نافذة الإعدادات"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_settings_test()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        ctk.set_appearance_mode("light")
        
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاحات RTL - نافذة الإعدادات")
        self.window.geometry("800x900")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 800
        height = 900
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_settings_test(self):
        """إنشاء نافذة الاختبار التي تحاكي نافذة الإعدادات"""
        # العنوان الرئيسي للاختبار
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار نافذة الإعدادات",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 10))
        
        # وصف الاختبار
        desc_label = create_rtl_label(
            self.window,
            text="هذا الاختبار يحاكي نافذة الإعدادات مع النصوص العربية بـ RTL",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))
        
        # إطار النافذة المحاكية
        settings_frame = ctk.CTkScrollableFrame(
            self.window, 
            fg_color=COLORS['bg_light'],
            corner_radius=10
        )
        settings_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))
        
        # عنوان النافذة
        title_label = create_rtl_label(
            settings_frame,
            text="إعدادات النظام",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(30, 20))
        
        # قسم معلومات المستخدم
        self.create_user_info_section(settings_frame)
        
        # قسم إدارة البيانات
        self.create_data_management_section(settings_frame)
        
        # قسم إعدادات النظام
        self.create_system_settings_section(settings_frame)
        
        # ملاحظة اختبار
        test_note = create_rtl_label(
            settings_frame,
            text="📝 ملاحظة: جميع النصوص العربية يجب أن تظهر بالاتجاه الصحيح من اليمين إلى اليسار",
            font_size='small',
            text_color=COLORS['info'],
            **ARABIC_TEXT_STYLES['title']
        )
        test_note.pack(pady=(20, 30))
    
    def create_user_info_section(self, parent):
        """إنشاء قسم معلومات المستخدم"""
        # عنوان القسم
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)
        
        title_label = create_rtl_label(
            title_frame,
            text="👤 معلومات المستخدم",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)
        
        # معلومات المستخدم التجريبية
        data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        data_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        info_items = [
            ("اسم المستخدم", "ahmed_user"),
            ("الاسم الكامل", "أحمد محمد علي"),
            ("البريد الإلكتروني", "<EMAIL>"),
            ("الصلاحية", "مستخدم"),
        ]
        
        for label, value in info_items:
            info_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
            info_frame.pack(fill="x", pady=2)
            
            info_label = create_rtl_label(
                info_frame,
                text=f"{label}: {value}",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            info_label.pack(anchor="w", padx=10, pady=5)
    
    def create_data_management_section(self, parent):
        """إنشاء قسم إدارة البيانات"""
        # عنوان القسم
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)
        
        title_label = create_rtl_label(
            title_frame,
            text="🗄️ إدارة البيانات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)
        
        # أزرار إدارة البيانات
        buttons_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # زر النسخ الاحتياطي
        backup_button = create_rtl_button(
            buttons_frame,
            text="📥 إنشاء نسخة احتياطية",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        backup_button.configure(height=40)
        backup_button.pack(fill="x", pady=5)

        # زر استعادة النسخة الاحتياطية
        restore_button = create_rtl_button(
            buttons_frame,
            text="📤 استعادة نسخة احتياطية",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        restore_button.configure(height=40)
        restore_button.pack(fill="x", pady=5)

        # زر تصدير البيانات
        export_button = create_rtl_button(
            buttons_frame,
            text="📊 تصدير البيانات إلى Excel",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        export_button.configure(height=40)
        export_button.pack(fill="x", pady=5)
    
    def create_system_settings_section(self, parent):
        """إنشاء قسم إعدادات النظام"""
        # عنوان القسم
        title_frame = ctk.CTkFrame(parent, **CARD_STYLES['elevated'])
        title_frame.pack(fill="x", padx=20, pady=10)
        
        title_label = create_rtl_label(
            title_frame,
            text="⚙️ إعدادات النظام",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=15)
        
        # معلومات النظام
        data_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        data_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # إحصائيات قاعدة البيانات التجريبية
        stats_items = [
            ("عدد الحسابات", "5"),
            ("عدد المعاملات", "127"),
            ("عدد التحويلات", "23"),
            ("إصدار البرنامج", "1.0.0"),
        ]
        
        for label, value in stats_items:
            info_frame = ctk.CTkFrame(data_frame, fg_color=COLORS['bg_light'], corner_radius=5)
            info_frame.pack(fill="x", pady=2)
            
            info_label = create_rtl_label(
                info_frame,
                text=f"{label}: {value}",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            info_label.pack(anchor="w", padx=10, pady=5)
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        from tkinter import messagebox
        messagebox.showinfo(
            "اختبار الزر", 
            "تم النقر على الزر!\n\nالنصوص العربية تظهر بـ RTL صحيح في:\n• عنوان نافذة الإعدادات\n• عناوين الأقسام الفرعية\n• معلومات المستخدم\n• أزرار إدارة البيانات\n• إحصائيات النظام"
        )
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار إصلاحات RTL في نافذة الإعدادات...")
    print("=" * 70)
    print("📋 ما يتم اختباره:")
    print("   ✅ عنوان نافذة الإعدادات: 'إعدادات النظام'")
    print("   ✅ قسم معلومات المستخدم:")
    print("      - عنوان القسم: '👤 معلومات المستخدم'")
    print("      - معلومات المستخدم: اسم المستخدم، الاسم الكامل، البريد، الصلاحية")
    print("   ✅ قسم إدارة البيانات:")
    print("      - عنوان القسم: '🗄️ إدارة البيانات'")
    print("      - أزرار: النسخ الاحتياطي، الاستعادة، التصدير")
    print("   ✅ قسم إعدادات النظام:")
    print("      - عنوان القسم: '⚙️ إعدادات النظام'")
    print("      - إحصائيات: عدد الحسابات، المعاملات، التحويلات، الإصدار")
    print("   ✅ محاذاة جميع العناصر بـ RTL")
    print("=" * 70)
    
    try:
        app = SettingsWindowRTLTest()
        app.run()
        print("✅ تم إنهاء الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
