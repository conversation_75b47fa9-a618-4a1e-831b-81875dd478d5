#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظائف المعاملات المالية
فحص الواردات، المصروفات، والتحويلات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from database.connection import db
from database.models import Account, Transaction, Transfer
from utils.auth import auth_manager
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, BUTTON_STYLES
from config.styles import ARABIC_TEXT_STYLES
import tkinter.messagebox as messagebox
from datetime import datetime, date

class TransactionsFunctionalityTester:
    """اختبار وظائف المعاملات المالية"""
    
    def __init__(self):
        # تسجيل دخول تلقائي
        users = db.execute_query("SELECT * FROM users LIMIT 1")
        if users:
            auth_manager.current_user = users[0]
            print(f"✅ تم تسجيل الدخول كـ: {users[0].get('username')}")
        
        # إنشاء النافذة
        self.window = ctk.CTk()
        self.window.title("اختبار وظائف المعاملات المالية")
        self.window.geometry("800x900")
        self.window.configure(fg_color=COLORS['bg_primary'])
        
        self.create_test_interface()
        
        # تشغيل النافذة
        self.window.mainloop()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # عنوان الاختبار
        title_label = create_rtl_label(
            self.window,
            text="🧪 اختبار وظائف المعاملات المالية",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))
        
        # معلومات الاختبار
        info_label = create_rtl_label(
            self.window,
            text="اختبار شامل لوظائف الواردات، المصروفات، والتحويلات",
            font_size='body',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['label']
        )
        info_label.pack(pady=(0, 20))
        
        # إطار الأزرار الرئيسية
        main_buttons_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        main_buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # أزرار الاختبار الرئيسية
        test_buttons = [
            ("💰 اختبار الواردات", self.test_income_transactions),
            ("💸 اختبار المصروفات", self.test_expense_transactions),
            ("🔄 اختبار التحويلات", self.test_transfers),
            ("📊 عرض الحسابات", self.show_accounts_summary),
            ("📋 عرض المعاملات", self.show_transactions_summary)
        ]
        
        for i, (text, command) in enumerate(test_buttons):
            button = create_rtl_button(
                main_buttons_frame,
                text=text,
                command=command,
                **BUTTON_STYLES['primary']
            )
            button.pack(fill="x", padx=15, pady=5)
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        form_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        form_title = create_rtl_label(
            form_frame,
            text="📝 نموذج اختبار المعاملات:",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        form_title.pack(pady=(15, 10))
        
        # اختيار الحساب
        account_label = create_rtl_label(
            form_frame,
            text="الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="w", padx=15, pady=(0, 5))
        
        # الحصول على الحسابات
        accounts = self.get_user_accounts()
        account_options = [f"{acc['id']} - {acc['name']}" for acc in accounts] if accounts else ["لا توجد حسابات"]
        
        self.account_combo = ctk.CTkComboBox(
            form_frame,
            values=account_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.account_combo.pack(fill="x", padx=15, pady=(0, 10))
        if account_options and account_options[0] != "لا توجد حسابات":
            self.account_combo.set(account_options[0])
        
        # المبلغ
        amount_label = create_rtl_label(
            form_frame,
            text="المبلغ:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(anchor="w", padx=15, pady=(0, 5))
        
        self.amount_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: 1000.00",
            height=40
        )
        self.amount_entry.pack(fill="x", padx=15, pady=(0, 10))
        
        # الوصف
        desc_label = create_rtl_label(
            form_frame,
            text="الوصف:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", padx=15, pady=(0, 5))
        
        self.desc_entry = create_rtl_entry(
            form_frame,
            placeholder_text="وصف المعاملة",
            height=40
        )
        self.desc_entry.pack(fill="x", padx=15, pady=(0, 15))
        
        # أزرار إضافة المعاملات
        action_buttons_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        action_buttons_frame.pack(pady=(0, 15))
        
        add_income_btn = create_rtl_button(
            action_buttons_frame,
            text="➕ إضافة وارد",
            command=self.add_income_transaction,
            **BUTTON_STYLES['primary']
        )
        add_income_btn.pack(side="left", padx=5)
        
        add_expense_btn = create_rtl_button(
            action_buttons_frame,
            text="➖ إضافة مصروف",
            command=self.add_expense_transaction,
            **BUTTON_STYLES['secondary']
        )
        add_expense_btn.pack(side="left", padx=5)
        
        # منطقة النتائج
        results_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_light'])
        results_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        results_title = create_rtl_label(
            results_frame,
            text="📊 نتائج الاختبار:",
            font_size='subtitle',
            **ARABIC_TEXT_STYLES['title']
        )
        results_title.pack(pady=(15, 10))
        
        self.results_text = ctk.CTkTextbox(
            results_frame,
            height=200,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # رسالة ترحيبية
        self.log_result("🎯 جاهز لاختبار وظائف المعاملات المالية!")
        self.log_result("📝 اختر نوع الاختبار من الأزرار أعلاه")
    
    def log_result(self, message):
        """إضافة رسالة إلى منطقة النتائج"""
        self.results_text.insert("end", f"{message}\n")
        self.results_text.see("end")
        self.window.update()
    
    def get_user_accounts(self):
        """الحصول على حسابات المستخدم"""
        try:
            user_id = auth_manager.current_user['id']
            accounts = Account.get_by_user(user_id)
            return accounts if accounts else []
        except Exception as e:
            self.log_result(f"❌ خطأ في الحصول على الحسابات: {e}")
            return []
    
    def test_income_transactions(self):
        """اختبار معاملات الواردات"""
        self.log_result("\n💰 اختبار معاملات الواردات...")
        
        try:
            accounts = self.get_user_accounts()
            if not accounts:
                self.log_result("❌ لا توجد حسابات للاختبار")
                return
            
            # اختبار إضافة وارد
            test_account = accounts[0]
            test_amount = 5000.0
            test_description = "اختبار وارد تلقائي"
            
            self.log_result(f"🔄 إضافة وارد تجريبي...")
            self.log_result(f"   الحساب: {test_account['name']}")
            self.log_result(f"   المبلغ: {test_amount}")
            self.log_result(f"   الوصف: {test_description}")
            
            # الحصول على الرصيد قبل المعاملة
            old_balances = test_account.get('balances', [])
            old_balance = old_balances[0]['balance'] if old_balances else 0
            
            # إنشاء المعاملة
            user_id = auth_manager.current_user['id']
            transaction_id = Transaction.create(
                user_id=user_id,
                account_id=test_account['id'],
                currency_id=test_account.get('currency_id', 1),
                transaction_type='income',
                amount=test_amount,
                description=test_description,
                transaction_date=date.today()
            )
            
            if transaction_id > 0:
                self.log_result(f"✅ تم إنشاء الوارد بنجاح (ID: {transaction_id})")
                
                # التحقق من تحديث الرصيد
                updated_account = Account.get_by_id(test_account['id'])
                new_balances = updated_account.get('balances', [])
                new_balance = new_balances[0]['balance'] if new_balances else 0
                
                expected_balance = float(old_balance) + test_amount
                if abs(float(new_balance) - expected_balance) < 0.01:
                    self.log_result(f"✅ تم تحديث الرصيد بشكل صحيح")
                    self.log_result(f"   الرصيد السابق: {old_balance}")
                    self.log_result(f"   الرصيد الجديد: {new_balance}")
                else:
                    self.log_result(f"❌ خطأ في تحديث الرصيد")
                    self.log_result(f"   متوقع: {expected_balance}, فعلي: {new_balance}")
            else:
                self.log_result("❌ فشل في إنشاء الوارد")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار الواردات: {e}")
    
    def test_expense_transactions(self):
        """اختبار معاملات المصروفات"""
        self.log_result("\n💸 اختبار معاملات المصروفات...")
        
        try:
            accounts = self.get_user_accounts()
            if not accounts:
                self.log_result("❌ لا توجد حسابات للاختبار")
                return
            
            # اختبار إضافة مصروف
            test_account = accounts[0]
            test_amount = 2000.0
            test_description = "اختبار مصروف تلقائي"
            
            self.log_result(f"🔄 إضافة مصروف تجريبي...")
            self.log_result(f"   الحساب: {test_account['name']}")
            self.log_result(f"   المبلغ: {test_amount}")
            self.log_result(f"   الوصف: {test_description}")
            
            # الحصول على الرصيد قبل المعاملة
            old_balances = test_account.get('balances', [])
            old_balance = old_balances[0]['balance'] if old_balances else 0
            
            # إنشاء المعاملة
            user_id = auth_manager.current_user['id']
            transaction_id = Transaction.create(
                user_id=user_id,
                account_id=test_account['id'],
                currency_id=test_account.get('currency_id', 1),
                transaction_type='expense',
                amount=test_amount,
                description=test_description,
                transaction_date=date.today()
            )
            
            if transaction_id > 0:
                self.log_result(f"✅ تم إنشاء المصروف بنجاح (ID: {transaction_id})")
                
                # التحقق من تحديث الرصيد
                updated_account = Account.get_by_id(test_account['id'])
                new_balances = updated_account.get('balances', [])
                new_balance = new_balances[0]['balance'] if new_balances else 0
                
                expected_balance = float(old_balance) - test_amount
                if abs(float(new_balance) - expected_balance) < 0.01:
                    self.log_result(f"✅ تم خصم الرصيد بشكل صحيح")
                    self.log_result(f"   الرصيد السابق: {old_balance}")
                    self.log_result(f"   الرصيد الجديد: {new_balance}")
                else:
                    self.log_result(f"❌ خطأ في خصم الرصيد")
                    self.log_result(f"   متوقع: {expected_balance}, فعلي: {new_balance}")
            else:
                self.log_result("❌ فشل في إنشاء المصروف")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار المصروفات: {e}")
    
    def test_transfers(self):
        """اختبار التحويلات"""
        self.log_result("\n🔄 اختبار التحويلات...")
        
        try:
            accounts = self.get_user_accounts()
            if len(accounts) < 2:
                self.log_result("❌ يجب وجود حسابين على الأقل لاختبار التحويلات")
                return
            
            # اختيار حسابين للتحويل
            from_account = accounts[0]
            to_account = accounts[1]
            test_amount = 1000.0
            test_description = "اختبار تحويل تلقائي"
            
            self.log_result(f"🔄 إنشاء تحويل تجريبي...")
            self.log_result(f"   من: {from_account['name']}")
            self.log_result(f"   إلى: {to_account['name']}")
            self.log_result(f"   المبلغ: {test_amount}")
            
            # الحصول على الأرصدة قبل التحويل
            from_old_balances = from_account.get('balances', [])
            from_old_balance = from_old_balances[0]['balance'] if from_old_balances else 0
            
            to_old_balances = to_account.get('balances', [])
            to_old_balance = to_old_balances[0]['balance'] if to_old_balances else 0
            
            # إنشاء التحويل
            user_id = auth_manager.current_user['id']
            transfer_id = Transfer.create(
                user_id=user_id,
                from_account_id=from_account['id'],
                to_account_id=to_account['id'],
                from_amount=test_amount,
                from_currency_id=from_account.get('currency_id', 1),
                to_amount=test_amount,
                to_currency_id=to_account.get('currency_id', 1),
                description=test_description,
                transfer_date=date.today()
            )
            
            if transfer_id > 0:
                self.log_result(f"✅ تم إنشاء التحويل بنجاح (ID: {transfer_id})")
                
                # التحقق من تحديث الأرصدة
                updated_from_account = Account.get_by_id(from_account['id'])
                updated_to_account = Account.get_by_id(to_account['id'])
                
                from_new_balances = updated_from_account.get('balances', [])
                from_new_balance = from_new_balances[0]['balance'] if from_new_balances else 0
                
                to_new_balances = updated_to_account.get('balances', [])
                to_new_balance = to_new_balances[0]['balance'] if to_new_balances else 0
                
                self.log_result(f"📊 تحديث الأرصدة:")
                self.log_result(f"   {from_account['name']}: {from_old_balance} → {from_new_balance}")
                self.log_result(f"   {to_account['name']}: {to_old_balance} → {to_new_balance}")
                
                # التحقق من صحة التحويل
                expected_from = float(from_old_balance) - test_amount
                expected_to = float(to_old_balance) + test_amount
                
                if (abs(float(from_new_balance) - expected_from) < 0.01 and 
                    abs(float(to_new_balance) - expected_to) < 0.01):
                    self.log_result("✅ التحويل تم بشكل صحيح")
                else:
                    self.log_result("❌ خطأ في التحويل")
            else:
                self.log_result("❌ فشل في إنشاء التحويل")
            
        except Exception as e:
            self.log_result(f"❌ خطأ في اختبار التحويلات: {e}")
    
    def add_income_transaction(self):
        """إضافة معاملة وارد من النموذج"""
        try:
            # التحقق من البيانات
            account_text = self.account_combo.get()
            if not account_text or account_text == "لا توجد حسابات":
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return
            
            account_id = int(account_text.split(' - ')[0])
            
            try:
                amount = float(self.amount_entry.get())
                if amount <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return
            
            description = self.desc_entry.get().strip()
            if not description:
                description = "وارد من النموذج"
            
            # إنشاء المعاملة
            user_id = auth_manager.current_user['id']
            
            # الحصول على معرف العملة للحساب
            account = Account.get_by_id(account_id)
            if not account:
                messagebox.showerror("خطأ", "الحساب غير موجود")
                return
            
            currency_id = account.get('currency_id', 1)
            
            transaction_id = Transaction.create(
                user_id=user_id,
                account_id=account_id,
                currency_id=currency_id,
                transaction_type='income',
                amount=amount,
                description=description,
                transaction_date=date.today()
            )
            
            if transaction_id > 0:
                messagebox.showinfo("نجح", f"تم إضافة الوارد بنجاح!\nمعرف المعاملة: {transaction_id}")
                self.log_result(f"✅ تم إضافة وارد: {amount} - {description}")
                
                # مسح النموذج
                self.amount_entry.delete(0, 'end')
                self.desc_entry.delete(0, 'end')
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الوارد")
                self.log_result("❌ فشل في إضافة الوارد")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
            self.log_result(f"❌ خطأ في إضافة الوارد: {e}")
    
    def add_expense_transaction(self):
        """إضافة معاملة مصروف من النموذج"""
        try:
            # التحقق من البيانات
            account_text = self.account_combo.get()
            if not account_text or account_text == "لا توجد حسابات":
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return
            
            account_id = int(account_text.split(' - ')[0])
            
            try:
                amount = float(self.amount_entry.get())
                if amount <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return
            
            description = self.desc_entry.get().strip()
            if not description:
                description = "مصروف من النموذج"
            
            # إنشاء المعاملة
            user_id = auth_manager.current_user['id']
            
            # الحصول على معرف العملة للحساب
            account = Account.get_by_id(account_id)
            if not account:
                messagebox.showerror("خطأ", "الحساب غير موجود")
                return
            
            currency_id = account.get('currency_id', 1)
            
            transaction_id = Transaction.create(
                user_id=user_id,
                account_id=account_id,
                currency_id=currency_id,
                transaction_type='expense',
                amount=amount,
                description=description,
                transaction_date=date.today()
            )
            
            if transaction_id > 0:
                messagebox.showinfo("نجح", f"تم إضافة المصروف بنجاح!\nمعرف المعاملة: {transaction_id}")
                self.log_result(f"✅ تم إضافة مصروف: {amount} - {description}")
                
                # مسح النموذج
                self.amount_entry.delete(0, 'end')
                self.desc_entry.delete(0, 'end')
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المصروف")
                self.log_result("❌ فشل في إضافة المصروف")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
            self.log_result(f"❌ خطأ في إضافة المصروف: {e}")
    
    def show_accounts_summary(self):
        """عرض ملخص الحسابات"""
        self.log_result("\n🏦 ملخص الحسابات:")
        
        try:
            accounts = self.get_user_accounts()
            if not accounts:
                self.log_result("❌ لا توجد حسابات")
                return
            
            self.log_result(f"📊 إجمالي الحسابات: {len(accounts)}")
            
            for i, account in enumerate(accounts, 1):
                balances = account.get('balances', [])
                balance_text = ", ".join([f"{b['balance']} {b['symbol']}" for b in balances]) if balances else "0"
                
                self.log_result(f"{i:2d}. {account['name']}")
                self.log_result(f"     الرصيد: {balance_text}")
                self.log_result(f"     الحالة: {'نشط' if account.get('is_active') else 'غير نشط'}")
        
        except Exception as e:
            self.log_result(f"❌ خطأ في عرض الحسابات: {e}")
    
    def show_transactions_summary(self):
        """عرض ملخص المعاملات"""
        self.log_result("\n📋 ملخص المعاملات:")
        
        try:
            user_id = auth_manager.current_user['id']
            
            # المعاملات
            transactions = db.execute_query("SELECT * FROM transactions WHERE user_id = %s ORDER BY transaction_date DESC LIMIT 10", (user_id,))
            income_count = len([t for t in transactions if t['transaction_type'] == 'income'])
            expense_count = len([t for t in transactions if t['transaction_type'] == 'expense'])
            
            self.log_result(f"💰 المعاملات الأخيرة (آخر 10):")
            self.log_result(f"   - الواردات: {income_count}")
            self.log_result(f"   - المصروفات: {expense_count}")
            
            if transactions:
                self.log_result("\n📋 تفاصيل المعاملات الأخيرة:")
                for transaction in transactions[:5]:  # عرض آخر 5 معاملات
                    type_icon = "💰" if transaction['transaction_type'] == 'income' else "💸"
                    self.log_result(f"   {type_icon} {transaction['amount']} - {transaction['description']}")
                    self.log_result(f"      التاريخ: {transaction['transaction_date']}")
            
            # التحويلات
            transfers = db.execute_query("SELECT * FROM transfers WHERE user_id = %s ORDER BY transfer_date DESC LIMIT 5", (user_id,))
            self.log_result(f"\n🔄 التحويلات الأخيرة: {len(transfers)}")
            
            if transfers:
                for transfer in transfers:
                    self.log_result(f"   🔄 {transfer['from_amount']} - {transfer['description']}")
                    self.log_result(f"      التاريخ: {transfer['transfer_date']}")
        
        except Exception as e:
            self.log_result(f"❌ خطأ في عرض المعاملات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧪 بدء اختبار وظائف المعاملات المالية")
    print("=" * 50)
    
    # تشغيل الاختبار
    TransactionsFunctionalityTester()

if __name__ == "__main__":
    main()
