import sys
import os
import mysql.connector
from decimal import Decimal
from datetime import datetime

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_transfer_database():
    """اختبار قاعدة البيانات مباشرة للتحويلات"""
    
    print("🔍 اختبار قاعدة البيانات للتحويلات...")
    
    try:
        # الاتصال بقاعدة البيانات مباشرة
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='money_manager'
        )
        cursor = connection.cursor(dictionary=True)
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # فحص هيكل جدول التحويلات
        print("\n📋 فحص هيكل جدول transfers...")
        cursor.execute("DESCRIBE transfers")
        columns = cursor.fetchall()
        
        print("أعمدة جدول transfers:")
        for col in columns:
            print(f"  - {col['Field']}: {col['Type']} {'(NULL)' if col['Null'] == 'YES' else '(NOT NULL)'}")
        
        # فحص التحويلات الموجودة
        print("\n🔄 فحص التحويلات الموجودة...")
        cursor.execute("SELECT COUNT(*) as count FROM transfers")
        count_result = cursor.fetchone()
        print(f"📊 عدد التحويلات في قاعدة البيانات: {count_result['count']}")
        
        if count_result['count'] > 0:
            print("\n📝 آخر 5 تحويلات:")
            cursor.execute("""
                SELECT t.*, 
                       fa.name as from_account_name, 
                       ta.name as to_account_name,
                       fc.name as from_currency_name, 
                       tc.name as to_currency_name
                FROM transfers t
                LEFT JOIN accounts fa ON t.from_account_id = fa.id
                LEFT JOIN accounts ta ON t.to_account_id = ta.id
                LEFT JOIN currencies fc ON t.from_currency_id = fc.id
                LEFT JOIN currencies tc ON t.to_currency_id = tc.id
                ORDER BY t.created_at DESC
                LIMIT 5
            """)
            transfers = cursor.fetchall()
            
            for transfer in transfers:
                print(f"  ID: {transfer['id']}")
                print(f"    من: {transfer['from_account_name']} ({transfer['from_currency_name']})")
                print(f"    إلى: {transfer['to_account_name']} ({transfer['to_currency_name']})")
                print(f"    المبلغ: {transfer['from_amount']} -> {transfer['to_amount']}")
                print(f"    التاريخ: {transfer['transfer_date']}")
                print(f"    الوصف: {transfer.get('description', 'لا يوجد')}")
                print()
        
        # فحص الحسابات المتاحة
        print("\n📊 فحص الحسابات المتاحة...")
        cursor.execute("SELECT COUNT(*) as count FROM accounts WHERE is_active = TRUE")
        accounts_count = cursor.fetchone()
        print(f"📈 عدد الحسابات النشطة: {accounts_count['count']}")
        
        if accounts_count['count'] > 0:
            cursor.execute("""
                SELECT a.id, a.name, a.user_id
                FROM accounts a 
                WHERE a.is_active = TRUE 
                ORDER BY a.created_at DESC 
                LIMIT 5
            """)
            accounts = cursor.fetchall()
            
            print("📝 الحسابات المتاحة:")
            for account in accounts:
                print(f"  - ID: {account['id']}, الاسم: {account['name']}, المستخدم: {account['user_id']}")
        
        # فحص العملات المتاحة
        print("\n💱 فحص العملات المتاحة...")
        cursor.execute("SELECT COUNT(*) as count FROM currencies WHERE is_active = TRUE")
        currencies_count = cursor.fetchone()
        print(f"💰 عدد العملات النشطة: {currencies_count['count']}")
        
        if currencies_count['count'] > 0:
            cursor.execute("SELECT id, name, code, symbol FROM currencies WHERE is_active = TRUE")
            currencies = cursor.fetchall()
            
            print("💱 العملات المتاحة:")
            for currency in currencies:
                print(f"  - ID: {currency['id']}, {currency['name']} ({currency['code']}) - {currency['symbol']}")
        
        # فحص المستخدمين
        print("\n👥 فحص المستخدمين...")
        cursor.execute("SELECT id, username, full_name FROM users WHERE is_active = TRUE")
        users = cursor.fetchall()
        
        print("👤 المستخدمون المتاحون:")
        for user in users:
            print(f"  - ID: {user['id']}, اسم المستخدم: {user['username']}, الاسم: {user['full_name']}")
        
        # محاولة إنشاء تحويل تجريبي إذا كانت البيانات متاحة
        if accounts_count['count'] >= 2 and currencies_count['count'] >= 1:
            print("\n🧪 محاولة إنشاء تحويل تجريبي...")
            
            # الحصول على أول حسابين
            cursor.execute("SELECT id FROM accounts WHERE is_active = TRUE LIMIT 2")
            test_accounts = cursor.fetchall()
            
            # الحصول على أول عملة
            cursor.execute("SELECT id FROM currencies WHERE is_active = TRUE LIMIT 1")
            test_currency = cursor.fetchone()
            
            if len(test_accounts) >= 2 and test_currency:
                from_account_id = test_accounts[0]['id']
                to_account_id = test_accounts[1]['id']
                currency_id = test_currency['id']
                test_amount = Decimal('5.00')
                
                print(f"📤 من الحساب ID: {from_account_id}")
                print(f"📥 إلى الحساب ID: {to_account_id}")
                print(f"💰 العملة ID: {currency_id}")
                print(f"💵 المبلغ: {test_amount}")
                
                # إدراج التحويل التجريبي
                insert_query = """
                    INSERT INTO transfers (user_id, from_account_id, to_account_id, 
                                         from_amount, from_currency_id, to_amount, to_currency_id, 
                                         exchange_rate, description, transfer_date)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    users[0]['id'],  # أول مستخدم
                    from_account_id,
                    to_account_id,
                    test_amount,
                    currency_id,
                    test_amount,
                    currency_id,
                    1.0000,
                    "تحويل تجريبي من اختبار قاعدة البيانات",
                    datetime.now().date()
                ))
                
                connection.commit()
                transfer_id = cursor.lastrowid
                
                print(f"✅ تم إنشاء التحويل التجريبي بنجاح! ID: {transfer_id}")
                
                # التحقق من التحويل المنشأ
                cursor.execute("""
                    SELECT t.*, 
                           fa.name as from_account_name, 
                           ta.name as to_account_name
                    FROM transfers t
                    LEFT JOIN accounts fa ON t.from_account_id = fa.id
                    LEFT JOIN accounts ta ON t.to_account_id = ta.id
                    WHERE t.id = %s
                """, (transfer_id,))
                
                created_transfer = cursor.fetchone()
                if created_transfer:
                    print("✅ تم التحقق من التحويل في قاعدة البيانات:")
                    print(f"  - ID: {created_transfer['id']}")
                    print(f"  - من: {created_transfer['from_account_name']}")
                    print(f"  - إلى: {created_transfer['to_account_name']}")
                    print(f"  - المبلغ: {created_transfer['from_amount']}")
                    print(f"  - الوصف: {created_transfer['description']}")
                else:
                    print("❌ لم يتم العثور على التحويل المنشأ")
        
    except mysql.connector.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            if 'cursor' in locals():
                cursor.close()
            if 'connection' in locals():
                connection.close()
            print("\n🔌 تم إغلاق الاتصال بقاعدة البيانات")
        except:
            pass

if __name__ == "__main__":
    test_transfer_database()
