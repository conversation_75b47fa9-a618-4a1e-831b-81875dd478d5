#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإصلاح استيراد Excel
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_excel_import_fix():
    """اختبار مبسط لإصلاح استيراد Excel"""
    try:
        print("🧪 اختبار مبسط لإصلاح استيراد Excel")
        print("=" * 50)
        
        # تهيئة قاعدة البيانات
        from database.connection import db
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # اختبار استعلام الواردات
        print("\n📊 اختبار استعلام الواردات...")
        
        income_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'income'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        income_results = db.execute_query(income_query, (user_id,))
        print(f"   📈 عدد الواردات: {len(income_results) if income_results else 0}")
        
        if income_results:
            for income in income_results:
                print(f"      ID: {income['id']} - {income['amount']:,.2f} {income['currency_symbol']}")
                print(f"         الحساب: {income['account_name']}")
                print(f"         التاريخ: {income['transaction_date']}")
        
        # اختبار استعلام المصروفات
        print("\n📊 اختبار استعلام المصروفات...")
        
        expense_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'expense'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        expense_results = db.execute_query(expense_query, (user_id,))
        print(f"   📉 عدد المصروفات: {len(expense_results) if expense_results else 0}")
        
        if expense_results:
            for expense in expense_results:
                print(f"      ID: {expense['id']} - {expense['amount']:,.2f} {expense['currency_symbol']}")
                print(f"         الحساب: {expense['account_name']}")
                print(f"         التاريخ: {expense['transaction_date']}")
        
        # اختبار الإصلاحات المطبقة
        print("\n🔧 الإصلاحات المطبقة في gui/main_window.py:")
        print("✅ تحديث ذكي للواجهة بعد الاستيراد")
        print("✅ تحديث لوحة التحكم تلقائياً")
        print("✅ تحديث الصفحة الحالية فقط إذا كانت مطابقة")
        print("✅ مسح ذاكرة التخزين المؤقت")
        print("✅ رسائل تشخيص مفصلة")
        
        print("\n📋 الكود المُحسن:")
        print("""
        # في دالة process_excel_import (السطر 3609-3631):
        if successful_imports > 0:
            print(f"🔄 تحديث الواجهة بعد استيراد {successful_imports} معاملة من نوع {transaction_type}")
            
            # تحديث لوحة التحكم إذا كانت نشطة
            self.refresh_dashboard_if_active()
            
            # تحديث الصفحة الحالية إذا كانت مطابقة لنوع المعاملة
            if hasattr(self, 'current_page'):
                if (transaction_type == "income" and self.current_page == "income") or \\
                   (transaction_type == "expense" and self.current_page == "expense"):
                    print(f"🔄 تحديث صفحة {transaction_type} الحالية...")
                    if transaction_type == "income":
                        self.show_income()
                    else:
                        self.show_expense()
                else:
                    print(f"📝 المستخدم في صفحة {getattr(self, 'current_page', 'غير معروفة')}، لا حاجة لتحديث صفحة {transaction_type}")
            
            # تحديث ذاكرة التخزين المؤقت
            self.clear_performance_cache()
            
            print("✅ تم تحديث الواجهة بنجاح")
        """)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح واجهة استيراد Excel")
    print("=" * 60)
    
    success = test_excel_import_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إصلاح مشكلة عرض العمليات المستوردة!")
        print("\n🚀 النتيجة المتوقعة:")
        print("• عند استيراد ملف Excel للواردات:")
        print("  - ستظهر رسالة نجاح")
        print("  - ستتحدث لوحة التحكم تلقائياً")
        print("  - إذا كان المستخدم في صفحة 'الواردات'، ستتحدث القائمة فوراً")
        print("  - ستظهر العمليات المستوردة في أعلى القائمة")
        
        print("\n• عند استيراد ملف Excel للمصروفات:")
        print("  - ستظهر رسالة نجاح")
        print("  - ستتحدث لوحة التحكم تلقائياً")
        print("  - إذا كان المستخدم في صفحة 'المصروفات'، ستتحدث القائمة فوراً")
        print("  - ستظهر العمليات المستوردة في أعلى القائمة")
        
        print("\n📋 للاختبار:")
        print("1. python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى 'الواردات' أو 'المصروفات'")
        print("4. اضغط 'استيراد من Excel'")
        print("5. اختر ملف Excel وأكمل الاستيراد")
        print("6. تحقق من ظهور العمليات فوراً في القائمة")
        
    else:
        print("❌ هناك مشاكل تحتاج مراجعة")

if __name__ == "__main__":
    main()
