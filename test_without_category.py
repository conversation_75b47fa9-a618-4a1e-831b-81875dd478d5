#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق بدون حقل التصنيف
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔌 اختبار الاتصال بقاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        if db.connect():
            print("✅ الاتصال بقاعدة البيانات نشط")
            
            # اختبار استعلام
            result = db.execute_query("SELECT DATABASE() as db_name")
            if result:
                print(f"   قاعدة البيانات: {result[0]['db_name']}")
            
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_transaction_update_without_category():
    """اختبار تحديث المعاملة بدون التصنيف"""
    print("\n🧪 اختبار تحديث المعاملة بدون التصنيف...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        
        # البحث عن معاملة للاختبار
        transactions = db.execute_query("SELECT id, description FROM transactions LIMIT 1")
        
        if not transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            return True
        
        transaction_id = transactions[0]['id']
        original_description = transactions[0]['description']
        test_description = f"اختبار بدون تصنيف - {original_description}"
        
        print(f"📝 اختبار المعاملة رقم: {transaction_id}")
        print(f"   الوصف الأصلي: '{original_description}'")
        print(f"   الوصف الجديد: '{test_description}'")
        
        # تحديث بدون التصنيف
        success = Transaction.update(
            transaction_id=transaction_id,
            description=test_description
        )
        
        print(f"   نتيجة التحديث: {success}")
        
        if success:
            # التحقق من التحديث
            updated = db.execute_query(
                "SELECT description FROM transactions WHERE id = %s",
                (transaction_id,)
            )
            
            if updated and updated[0]['description'] == test_description:
                print("   ✅ التحديث بدون التصنيف نجح")
                
                # إعادة الوصف الأصلي
                Transaction.update(transaction_id=transaction_id, description=original_description)
                print("   ✅ تم إعادة الوصف الأصلي")
                return True
            else:
                print("   ❌ التحديث لم يتم حفظه")
                return False
        else:
            print("   ❌ فشل في التحديث")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_queries():
    """اختبار استعلامات المعاملات بدون التصنيف"""
    print("\n📊 اختبار استعلامات المعاملات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        from utils.auth import auth_manager
        
        # تعيين مستخدم تجريبي
        users = db.execute_query("SELECT id FROM users LIMIT 1")
        if users:
            auth_manager.current_user = {'id': users[0]['id']}
            user_id = users[0]['id']
            print(f"✅ المستخدم: {user_id}")
        else:
            print("❌ لا يوجد مستخدمون")
            return False
        
        # اختبار استعلام الواردات
        print("\n   🔍 اختبار استعلام الواردات...")
        income_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'income'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        incomes = db.execute_query(income_query, (user_id,))
        print(f"      عدد الواردات: {len(incomes) if incomes else 0}")
        
        if incomes:
            for income in incomes[:2]:  # عرض أول معاملتين
                print(f"      - {income['amount']} {income['currency_symbol']} - {income['description']}")
        
        # اختبار استعلام المصروفات
        print("\n   🔍 اختبار استعلام المصروفات...")
        expense_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'expense'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        expenses = db.execute_query(expense_query, (user_id,))
        print(f"      عدد المصروفات: {len(expenses) if expenses else 0}")
        
        if expenses:
            for expense in expenses[:2]:  # عرض أول معاملتين
                print(f"      - {expense['amount']} {expense['currency_symbol']} - {expense['description']}")
        
        print("   ✅ جميع الاستعلامات تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستعلامات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_creation():
    """اختبار إنشاء معاملة جديدة بدون التصنيف"""
    print("\n➕ اختبار إنشاء معاملة جديدة...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        from utils.auth import auth_manager
        from datetime import date
        
        # التأكد من وجود مستخدم
        if not auth_manager.current_user:
            users = db.execute_query("SELECT id FROM users LIMIT 1")
            if users:
                auth_manager.current_user = {'id': users[0]['id']}
            else:
                print("❌ لا يوجد مستخدمون")
                return False
        
        user_id = auth_manager.current_user['id']
        
        # البحث عن حساب
        accounts = db.execute_query("SELECT id FROM accounts WHERE user_id = %s LIMIT 1", (user_id,))
        if not accounts:
            print("❌ لا توجد حسابات")
            return False
        
        account_id = accounts[0]['id']
        
        print(f"📝 إنشاء معاملة تجريبية...")
        print(f"   المستخدم: {user_id}")
        print(f"   الحساب: {account_id}")
        print(f"   المبلغ: 100.00")
        print(f"   العملة: 1")
        print(f"   الوصف: 'اختبار بدون تصنيف'")
        
        # إنشاء معاملة تجريبية
        result = Transaction.create(
            user_id=user_id,
            account_id=account_id,
            transaction_type='income',
            amount=100.00,
            currency_id=1,
            category_id=None,
            description='اختبار بدون تصنيف',
            transaction_date=str(date.today())
        )
        
        print(f"   نتيجة الإنشاء: {result}")
        
        if result > 0:
            print("   ✅ تم إنشاء المعاملة بنجاح")
            
            # حذف المعاملة التجريبية
            db.execute_update("DELETE FROM transactions WHERE id = %s", (result,))
            print("   ✅ تم حذف المعاملة التجريبية")
            
            return True
        else:
            print("   ❌ فشل في إنشاء المعاملة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإنشاء: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التطبيق بدون حقل التصنيف")
    print("=" * 60)
    
    # 1. اختبار الاتصال
    db_ok = test_database_connection()
    
    # 2. اختبار التحديث
    update_ok = test_transaction_update_without_category()
    
    # 3. اختبار الاستعلامات
    queries_ok = test_transaction_queries()
    
    # 4. اختبار الإنشاء
    create_ok = test_transaction_creation()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 ملخص الاختبارات:")
    print(f"   🔌 قاعدة البيانات: {'✅ متصلة' if db_ok else '❌ مشكلة'}")
    print(f"   🔄 تحديث المعاملات: {'✅ يعمل' if update_ok else '❌ مشكلة'}")
    print(f"   📊 استعلامات المعاملات: {'✅ تعمل' if queries_ok else '❌ مشكلة'}")
    print(f"   ➕ إنشاء المعاملات: {'✅ يعمل' if create_ok else '❌ مشكلة'}")
    
    if db_ok and update_ok and queries_ok and create_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("   التطبيق جاهز للعمل بدون حقل التصنيف")
        
        print("\n🚀 يمكنك الآن:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. إضافة معاملات جديدة (واردات ومصروفات)")
        print("3. تعديل المعاملات الموجودة")
        print("4. جميع العمليات ستعمل بدون مشاكل")
        
        print("\n✅ التحسينات المطبقة:")
        print("- ✅ إزالة حقل التصنيف من نوافذ الإضافة والتعديل")
        print("- ✅ إزالة التصنيف من عرض قوائم المعاملات")
        print("- ✅ تبسيط استعلامات قاعدة البيانات")
        print("- ✅ تحسين أداء التطبيق")
        
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        if not db_ok:
            print("   🔧 تأكد من تشغيل XAMPP وخدمة MySQL")
        if not update_ok:
            print("   🔧 راجع دالة Transaction.update")
        if not queries_ok:
            print("   🔧 راجع استعلامات قاعدة البيانات")
        if not create_ok:
            print("   🔧 راجع دالة Transaction.create")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
