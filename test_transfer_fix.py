#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة التحويل بين الحسابات
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_transfer_functionality():
    """اختبار وظيفة التحويل بعد الإصلاح"""
    try:
        print("🧪 اختبار وظيفة التحويل بعد الإصلاح...")
        print("=" * 60)
        
        from database.connection import db
        from utils.auth import auth_manager
        from database.models import Account, Transfer
        from datetime import datetime
        from decimal import Decimal
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # 1. فحص الحسابات المتاحة
        print(f"\n1️⃣ فحص الحسابات المتاحة:")
        accounts = Account.get_by_user(user_id)
        
        if not accounts or len(accounts) < 2:
            print("   ❌ لا توجد حسابات كافية للتحويل")
            return False
        
        print(f"   📊 عدد الحسابات: {len(accounts)}")
        
        # اختيار أول حسابين
        account1 = accounts[0]
        account2 = accounts[1]
        
        print(f"   الحساب المصدر: {account1['name']} (ID: {account1['id']})")
        print(f"   الحساب الهدف: {account2['name']} (ID: {account2['id']})")
        
        # 2. تحضير الأرصدة
        print(f"\n2️⃣ تحضير الأرصدة:")
        
        # التأكد من وجود رصيد كافي في الحساب المصدر
        current_balance = Account.get_currency_balance(account1['id'], 1)
        transfer_amount = Decimal("500.00")
        
        print(f"   الرصيد الحالي للحساب المصدر: {current_balance:,.2f} ر.س")
        print(f"   المبلغ المطلوب للتحويل: {transfer_amount:,.2f} ر.س")
        
        if current_balance < transfer_amount:
            print("   ⚠️ إضافة رصيد إضافي...")
            Account.add_currency_balance(account1['id'], 1, float(transfer_amount + 1000))
            current_balance = Account.get_currency_balance(account1['id'], 1)
            print(f"   ✅ الرصيد الجديد: {current_balance:,.2f} ر.س")
        
        # الحصول على رصيد الحساب الهدف قبل التحويل
        target_balance_before = Account.get_currency_balance(account2['id'], 1)
        print(f"   رصيد الحساب الهدف قبل التحويل: {target_balance_before:,.2f} ر.س")
        
        # 3. تنفيذ التحويل
        print(f"\n3️⃣ تنفيذ التحويل:")
        
        print(f"   🔄 تحويل {transfer_amount:,.2f} ر.س من {account1['name']} إلى {account2['name']}")
        
        transfer_id = Transfer.create(
            user_id=user_id,
            from_account_id=account1['id'],
            to_account_id=account2['id'],
            from_amount=transfer_amount,
            from_currency_id=1,  # SAR
            to_amount=transfer_amount,
            to_currency_id=1,    # SAR
            description="تحويل اختبار بعد الإصلاح",
            transfer_date=datetime.now().date()
        )
        
        if transfer_id > 0:
            print(f"   ✅ نجح التحويل! معرف التحويل: {transfer_id}")
            
            # 4. التحقق من النتائج
            print(f"\n4️⃣ التحقق من النتائج:")
            
            source_balance_after = Account.get_currency_balance(account1['id'], 1)
            target_balance_after = Account.get_currency_balance(account2['id'], 1)
            
            print(f"   رصيد الحساب المصدر بعد التحويل: {source_balance_after:,.2f} ر.س")
            print(f"   رصيد الحساب الهدف بعد التحويل: {target_balance_after:,.2f} ر.س")
            
            # حساب التغيير المتوقع
            expected_source = current_balance - transfer_amount
            expected_target = target_balance_before + transfer_amount
            
            source_diff = abs(source_balance_after - expected_source)
            target_diff = abs(target_balance_after - expected_target)
            
            if source_diff < 0.01 and target_diff < 0.01:
                print("   ✅ التحويل تم بشكل صحيح!")
                
                # عرض تفاصيل التحويل
                transfer_query = """
                    SELECT t.*, 
                           a1.name as from_account_name,
                           a2.name as to_account_name,
                           c.symbol as currency_symbol
                    FROM transfers t
                    JOIN accounts a1 ON t.from_account_id = a1.id
                    JOIN accounts a2 ON t.to_account_id = a2.id
                    JOIN currencies c ON t.from_currency_id = c.id
                    WHERE t.id = %s
                """
                transfer_details = db.execute_query(transfer_query, (transfer_id,))
                
                if transfer_details:
                    transfer = transfer_details[0]
                    print(f"\n   📋 تفاصيل التحويل:")
                    print(f"      معرف التحويل: {transfer['id']}")
                    print(f"      من: {transfer['from_account_name']}")
                    print(f"      إلى: {transfer['to_account_name']}")
                    print(f"      المبلغ: {transfer['from_amount']:,.2f} {transfer['currency_symbol']}")
                    print(f"      التاريخ: {transfer['transfer_date']}")
                    print(f"      الوصف: {transfer['description']}")
                
                # حذف التحويل التجريبي
                print(f"\n   🗑️ حذف التحويل التجريبي...")
                Transfer.delete(transfer_id)
                print("   ✅ تم حذف التحويل التجريبي")
                
                result = True
            else:
                print("   ❌ خطأ في حساب الأرصدة!")
                print(f"      فرق المصدر: {source_diff:,.2f}")
                print(f"      فرق الهدف: {target_diff:,.2f}")
                result = False
        else:
            print("   ❌ فشل في إنشاء التحويل")
            result = False
        
        # تسجيل الخروج
        auth_manager.logout()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transfer_window_data_loading():
    """اختبار تحميل البيانات في نافذة التحويل"""
    try:
        print("\n🖥️ اختبار تحميل البيانات في نافذة التحويل...")
        print("-" * 50)
        
        from utils.auth import auth_manager
        from database.models import Account, Currency
        from database.connection import db
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        
        # محاكاة تحميل البيانات كما في TransferWindow
        print("   📊 تحميل الحسابات...")
        accounts = Account.get_by_user(user_id)
        
        if accounts:
            print(f"   ✅ تم تحميل {len(accounts)} حساب")
            
            # تحميل أرصدة كل حساب
            for account in accounts[:3]:  # أول 3 حسابات فقط للاختبار
                account_id = account['id']
                
                balances_query = """
                    SELECT ab.currency_id, ab.balance, c.code, c.symbol
                    FROM account_balances ab
                    JOIN currencies c ON ab.currency_id = c.id
                    WHERE ab.account_id = %s AND ab.balance > 0
                    ORDER BY c.code
                """
                balances = db.execute_query(balances_query, (account_id,))
                account['balances'] = balances if balances else []
                
                print(f"      الحساب {account['name']}: {len(account['balances'])} عملة")
                for balance in account['balances']:
                    print(f"         - {balance['code']}: {balance['balance']:,.2f} {balance['symbol']}")
        else:
            print("   ❌ لا توجد حسابات")
        
        # تحميل العملات
        print("   💱 تحميل العملات...")
        currencies = Currency.get_all()
        
        if currencies:
            print(f"   ✅ تم تحميل {len(currencies)} عملة")
        else:
            print("   ❌ لا توجد عملات")
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة التحويل بين الحسابات")
    print("=" * 70)
    
    # اختبار وظيفة التحويل الأساسية
    transfer_test_ok = test_transfer_functionality()
    
    # اختبار تحميل البيانات في نافذة التحويل
    data_loading_ok = test_transfer_window_data_loading()
    
    print("\n" + "=" * 70)
    print("📊 ملخص الاختبارات:")
    print(f"   وظيفة التحويل الأساسية: {'✅' if transfer_test_ok else '❌'}")
    print(f"   تحميل البيانات في النافذة: {'✅' if data_loading_ok else '❌'}")
    
    if transfer_test_ok and data_loading_ok:
        print("\n🎉 تم إصلاح مشكلة التحويل بنجاح!")
        print("✅ وظيفة التحويل تعمل بشكل صحيح الآن")
        print("🔄 أعد تشغيل التطبيق لاختبار التحويل من الواجهة")
        
        print("\n📋 التحسينات المطبقة:")
        print("   - تحسين تحميل أرصدة الحسابات")
        print("   - إضافة تسجيل مفصل للتشخيص")
        print("   - تحسين التحقق من صحة البيانات")
        print("   - تحسين معالجة الأخطاء")
        print("   - التحقق من كفاية الرصيد")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى مراجعة إضافية")
        
        if not transfer_test_ok:
            print("   - مشكلة في وظيفة التحويل الأساسية")
        if not data_loading_ok:
            print("   - مشكلة في تحميل البيانات")

if __name__ == "__main__":
    main()
