#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص شامل لمشكلة فقدان البيانات في تطبيق إدارة الأموال
"""

import sys
import os
import json
import mysql.connector
from mysql.connector import Error
from datetime import datetime
import sqlite3

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 80)
    print(f"📋 {title}")
    print("=" * 80)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n🔍 {title}")
    print("-" * 60)

def check_mysql_connection():
    """فحص الاتصال بـ MySQL"""
    print_section("فحص الاتصال بقاعدة بيانات MySQL")
    
    # قراءة إعدادات قاعدة البيانات
    configs_to_try = []
    
    # 1. من ملف database_settings.json
    try:
        with open('config/database_settings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            configs_to_try.append(("database_settings.json", config))
            print(f"✅ تم العثور على إعدادات في database_settings.json")
    except Exception as e:
        print(f"❌ لم يتم العثور على database_settings.json: {e}")
    
    # 2. من ملف settings.py
    try:
        from config.settings import DATABASE_CONFIG
        configs_to_try.append(("settings.py", DATABASE_CONFIG))
        print(f"✅ تم العثور على إعدادات في settings.py")
    except Exception as e:
        print(f"❌ لم يتم العثور على إعدادات في settings.py: {e}")
    
    # 3. إعدادات افتراضية
    default_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    configs_to_try.append(("افتراضية", default_config))
    
    mysql_working = False
    working_config = None
    
    for config_name, config in configs_to_try:
        print(f"\n🔄 اختبار الإعدادات من {config_name}:")
        print(f"   المضيف: {config.get('host', 'غير محدد')}")
        print(f"   المنفذ: {config.get('port', 'غير محدد')}")
        print(f"   المستخدم: {config.get('user', 'غير محدد')}")
        print(f"   قاعدة البيانات: {config.get('database', 'غير محدد')}")
        
        try:
            # محاولة الاتصال
            connection = mysql.connector.connect(
                host=config.get('host', 'localhost'),
                port=config.get('port', 3306),
                user=config.get('user', 'root'),
                password=config.get('password', ''),
                database=config.get('database', 'money_manager'),
                charset=config.get('charset', 'utf8mb4'),
                autocommit=config.get('autocommit', True)
            )
            
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()
                print(f"   ✅ الاتصال ناجح! إصدار MySQL: {version[0]}")
                
                # فحص الجداول
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"   📊 عدد الجداول: {len(tables)}")
                
                if tables:
                    print("   📋 الجداول الموجودة:")
                    total_records = 0
                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        total_records += count
                        print(f"      - {table[0]}: {count} سجل")
                    print(f"   📈 إجمالي السجلات: {total_records}")
                
                cursor.close()
                connection.close()
                mysql_working = True
                working_config = (config_name, config)
                break
                
        except Error as e:
            if "Unknown database" in str(e):
                print(f"   ⚠️ قاعدة البيانات غير موجودة: {config.get('database')}")
            elif "Access denied" in str(e):
                print(f"   ❌ خطأ في كلمة المرور أو المستخدم")
            elif "Can't connect to MySQL server" in str(e):
                print(f"   ❌ لا يمكن الاتصال بخادم MySQL")
            else:
                print(f"   ❌ خطأ في الاتصال: {e}")
        except Exception as e:
            print(f"   ❌ خطأ غير متوقع: {e}")
    
    return mysql_working, working_config

def check_sqlite_files():
    """فحص ملفات SQLite"""
    print_section("فحص ملفات قاعدة بيانات SQLite")
    
    sqlite_files = []
    
    # البحث عن ملفات SQLite في المجلد الحالي
    for file in os.listdir('.'):
        if file.endswith('.db') or file.endswith('.sqlite'):
            sqlite_files.append(file)
    
    # البحث في مجلد backups
    if os.path.exists('backups'):
        for file in os.listdir('backups'):
            if file.endswith('.db') or file.endswith('.sqlite'):
                sqlite_files.append(os.path.join('backups', file))
    
    if not sqlite_files:
        print("❌ لم يتم العثور على أي ملفات SQLite")
        return False, None
    
    print(f"✅ تم العثور على {len(sqlite_files)} ملف SQLite:")
    
    working_sqlite = None
    
    for db_file in sqlite_files:
        print(f"\n📁 فحص الملف: {db_file}")
        try:
            # فحص حجم الملف
            size = os.path.getsize(db_file)
            print(f"   📏 حجم الملف: {size:,} بايت")
            
            if size == 0:
                print("   ⚠️ الملف فارغ")
                continue
            
            # محاولة الاتصال
            connection = sqlite3.connect(db_file)
            cursor = connection.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"   📊 عدد الجداول: {len(tables)}")
            
            if tables:
                print("   📋 الجداول الموجودة:")
                total_records = 0
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        total_records += count
                        print(f"      - {table[0]}: {count} سجل")
                    except Exception as e:
                        print(f"      - {table[0]}: خطأ في القراءة - {e}")
                
                print(f"   📈 إجمالي السجلات: {total_records}")
                
                if total_records > 0 and working_sqlite is None:
                    working_sqlite = db_file
                    print("   ⭐ هذا الملف يحتوي على بيانات!")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            print(f"   ❌ خطأ في قراءة الملف: {e}")
    
    return len(sqlite_files) > 0, working_sqlite

def check_json_files():
    """فحص ملفات JSON للبيانات"""
    print_section("فحص ملفات JSON للبيانات")
    
    json_files = ['accounts.json', 'transactions.json', 'users.json']
    found_data = False
    
    for json_file in json_files:
        if os.path.exists(json_file):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    count = len(data)
                elif isinstance(data, dict):
                    count = len(data.keys())
                else:
                    count = 1
                
                print(f"✅ {json_file}: {count} عنصر")
                
                # عرض عينة من البيانات
                if count > 0:
                    found_data = True
                    if isinstance(data, list) and len(data) > 0:
                        print(f"   📋 عينة من البيانات: {list(data[0].keys()) if isinstance(data[0], dict) else 'بيانات بسيطة'}")
                    elif isinstance(data, dict):
                        print(f"   📋 المفاتيح: {list(data.keys())[:5]}")
                
            except Exception as e:
                print(f"❌ خطأ في قراءة {json_file}: {e}")
        else:
            print(f"❌ {json_file}: غير موجود")
    
    return found_data

def check_backup_files():
    """فحص ملفات النسخ الاحتياطية"""
    print_section("فحص ملفات النسخ الاحتياطية")
    
    if not os.path.exists('backups'):
        print("❌ مجلد النسخ الاحتياطية غير موجود")
        return False
    
    backup_files = os.listdir('backups')
    if not backup_files:
        print("❌ لا توجد ملفات نسخ احتياطية")
        return False
    
    print(f"✅ تم العثور على {len(backup_files)} ملف نسخ احتياطي:")
    
    # تصنيف الملفات
    sql_backups = [f for f in backup_files if f.endswith('.sql')]
    db_backups = [f for f in backup_files if f.endswith('.db')]
    zip_backups = [f for f in backup_files if f.endswith('.zip')]
    json_backups = [f for f in backup_files if f.endswith('.json')]
    
    if sql_backups:
        print(f"   📄 ملفات SQL: {len(sql_backups)}")
        for backup in sorted(sql_backups)[-3:]:  # آخر 3 ملفات
            size = os.path.getsize(os.path.join('backups', backup))
            print(f"      - {backup}: {size:,} بايت")
    
    if db_backups:
        print(f"   🗄️ ملفات قاعدة البيانات: {len(db_backups)}")
        for backup in sorted(db_backups)[-3:]:
            size = os.path.getsize(os.path.join('backups', backup))
            print(f"      - {backup}: {size:,} بايت")
    
    if zip_backups:
        print(f"   📦 ملفات مضغوطة: {len(zip_backups)}")
        for backup in sorted(zip_backups)[-3:]:
            size = os.path.getsize(os.path.join('backups', backup))
            print(f"      - {backup}: {size:,} بايت")
    
    if json_backups:
        print(f"   📋 ملفات JSON: {len(json_backups)}")
        for backup in sorted(json_backups)[-3:]:
            size = os.path.getsize(os.path.join('backups', backup))
            print(f"      - {backup}: {size:,} بايت")
    
    return True

def main():
    """الدالة الرئيسية"""
    print_header("تشخيص شامل لمشكلة فقدان البيانات")
    
    print("🎯 الهدف: تحديد سبب فقدان البيانات وإيجاد حلول للاسترداد")
    print(f"📅 وقت التشخيص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. فحص MySQL
    mysql_working, mysql_config = check_mysql_connection()
    
    # 2. فحص SQLite
    sqlite_found, sqlite_file = check_sqlite_files()
    
    # 3. فحص ملفات JSON
    json_data_found = check_json_files()
    
    # 4. فحص النسخ الاحتياطية
    backups_found = check_backup_files()
    
    # 5. توليد التقرير النهائي
    print_header("ملخص التشخيص")
    
    print("📊 نتائج الفحص:")
    print(f"   🔗 MySQL: {'✅ يعمل' if mysql_working else '❌ لا يعمل'}")
    if mysql_working:
        print(f"      📋 الإعدادات العاملة: {mysql_config[0]}")
    
    print(f"   🗄️ SQLite: {'✅ موجود' if sqlite_found else '❌ غير موجود'}")
    if sqlite_file:
        print(f"      📁 الملف الأفضل: {sqlite_file}")
    
    print(f"   📋 بيانات JSON: {'✅ موجودة' if json_data_found else '❌ غير موجودة'}")
    print(f"   💾 نسخ احتياطية: {'✅ موجودة' if backups_found else '❌ غير موجودة'}")
    
    return {
        'mysql_working': mysql_working,
        'mysql_config': mysql_config,
        'sqlite_found': sqlite_found,
        'sqlite_file': sqlite_file,
        'json_data_found': json_data_found,
        'backups_found': backups_found
    }

if __name__ == "__main__":
    main()
