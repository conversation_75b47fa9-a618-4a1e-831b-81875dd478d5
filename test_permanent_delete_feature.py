#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة الحذف النهائي للمستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_permanent_delete_functionality():
    """اختبار وظيفة الحذف النهائي"""
    print("🧪 اختبار ميزة الحذف النهائي للمستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول
        print("\n2. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # 3. التحقق من وجود الوظائف الجديدة
        print("\n3. التحقق من وجود الوظائف الجديدة...")
        
        # اختبار وجود دالة delete_user_permanently في auth_manager
        if hasattr(auth_manager, 'delete_user_permanently'):
            print("✅ دالة delete_user_permanently موجودة في auth_manager")
        else:
            print("❌ دالة delete_user_permanently مفقودة في auth_manager")
            return False
        
        # اختبار وجود دالة delete_user_permanently في User model
        from database.models import User
        if hasattr(User, 'delete_user_permanently'):
            print("✅ دالة delete_user_permanently موجودة في User model")
        else:
            print("❌ دالة delete_user_permanently مفقودة في User model")
            return False
        
        # 4. إنشاء مستخدم تجريبي للحذف
        print("\n4. إنشاء مستخدم تجريبي للحذف...")
        test_username = "test_permanent_delete"
        
        # التحقق من عدم وجود المستخدم مسبقاً
        existing_user = db.execute_query("SELECT * FROM users WHERE username = %s", (test_username,))
        if existing_user:
            print("⚠️ المستخدم التجريبي موجود مسبقاً - سيتم حذفه أولاً")
            db.execute_update("DELETE FROM users WHERE username = %s", (test_username,))
        
        # إنشاء المستخدم التجريبي
        success, result = auth_manager.register_user(
            username=test_username,
            password="test123",
            full_name="مستخدم تجريبي للحذف النهائي",
            role="user"
        )
        
        if success:
            print("✅ تم إنشاء المستخدم التجريبي")
            
            # الحصول على معرف المستخدم
            test_user = User.get_by_username(test_username)
            if test_user:
                test_user_id = test_user['id']
                print(f"✅ معرف المستخدم التجريبي: {test_user_id}")
            else:
                print("❌ لم يتم العثور على المستخدم التجريبي")
                return False
        else:
            print(f"❌ فشل إنشاء المستخدم التجريبي: {result}")
            return False
        
        # 5. اختبار دالة delete_user_permanently في models
        print("\n5. اختبار دالة delete_user_permanently في models...")
        
        # التحقق من وجود المستخدم قبل الحذف
        user_before_delete = User.get_by_id(test_user_id)
        if not user_before_delete:
            print("❌ المستخدم التجريبي غير موجود قبل الحذف")
            return False
        
        print(f"✅ المستخدم موجود قبل الحذف: {user_before_delete['username']}")
        
        # اختبار الحذف النهائي
        success, message = User.delete_user_permanently(test_user_id)
        if success:
            print(f"✅ تم حذف المستخدم نهائياً: {message}")
            
            # التحقق من الحذف
            user_after_delete = User.get_by_id(test_user_id)
            if user_after_delete is None:
                print("✅ تم التحقق من الحذف النهائي - المستخدم غير موجود")
            else:
                print("❌ المستخدم لا يزال موجوداً بعد الحذف")
                return False
        else:
            print(f"❌ فشل الحذف النهائي: {message}")
            return False
        
        # 6. اختبار دالة delete_user_permanently في auth_manager
        print("\n6. اختبار دالة delete_user_permanently في auth_manager...")
        
        # إنشاء مستخدم تجريبي آخر
        test_username2 = "test_permanent_delete2"
        success, result = auth_manager.register_user(
            username=test_username2,
            password="test123",
            full_name="مستخدم تجريبي للحذف النهائي 2",
            role="user"
        )
        
        if success:
            test_user2 = User.get_by_username(test_username2)
            test_user_id2 = test_user2['id']
            print("✅ تم إنشاء المستخدم التجريبي الثاني")
            
            # اختبار الحذف عبر auth_manager
            success, message = auth_manager.delete_user_permanently(test_user_id2)
            if success:
                print(f"✅ تم حذف المستخدم عبر auth_manager: {message}")
                
                # التحقق من الحذف
                user_after_delete = User.get_by_id(test_user_id2)
                if user_after_delete is None:
                    print("✅ تم التحقق من الحذف عبر auth_manager")
                else:
                    print("❌ المستخدم لا يزال موجوداً بعد الحذف عبر auth_manager")
                    return False
            else:
                print(f"❌ فشل الحذف عبر auth_manager: {message}")
                return False
        else:
            print(f"❌ فشل إنشاء المستخدم التجريبي الثاني: {result}")
            return False
        
        # 7. اختبار منع حذف المدير لنفسه
        print("\n7. اختبار منع حذف المدير لنفسه...")
        
        # الحصول على معرف المدير الحالي
        admin_id = auth_manager.current_user['id']
        
        # محاولة حذف المدير (يجب أن تفشل)
        success, message = auth_manager.delete_user_permanently(admin_id)
        if not success and "لا يمكنك حذف حسابك الخاص" in message:
            print("✅ تم منع المدير من حذف نفسه")
        else:
            print("❌ لم يتم منع المدير من حذف نفسه")
            return False
        
        # 8. اختبار مكونات واجهة المستخدم
        print("\n8. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            print("✅ مكونات واجهة المستخدم متاحة")
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        # 9. محاكاة إنشاء زر الحذف النهائي
        print("\n9. محاكاة إنشاء زر الحذف النهائي...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة مؤقتة
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # محاكاة إنشاء إطار الأزرار
            buttons_frame = ctk.CTkFrame(test_window, fg_color="transparent")
            
            # محاكاة إنشاء زر الحذف النهائي
            permanent_delete_button = create_rtl_button(
                buttons_frame,
                text="🗑️ حذف نهائياً",
                width=80,
                **BUTTON_STYLES['danger']
            )
            
            print("✅ تم إنشاء زر الحذف النهائي بنجاح")
            
            # إغلاق النافذة التجريبية
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء زر الحذف النهائي: {e}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحذف النهائي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_integration():
    """اختبار تكامل واجهة المستخدم"""
    print("\n🎨 اختبار تكامل واجهة المستخدم")
    print("=" * 40)
    
    try:
        # 1. اختبار تسجيل الدخول
        print("1. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # 2. اختبار جلب المستخدمين
        print("\n2. اختبار جلب المستخدمين...")
        from database.models import User
        
        users = User.get_all()
        if users and len(users) > 0:
            print(f"✅ تم جلب {len(users)} مستخدم")
            
            # محاكاة إنشاء بطاقات المستخدمين مع زر الحذف النهائي
            for i, user in enumerate(users):
                username = user.get('username', f'مستخدم_{i+1}')
                role = user.get('role', 'user')
                
                print(f"   📋 بطاقة المستخدم: {username}")
                print(f"      - الدور: {'مدير' if role == 'admin' else 'مستخدم'}")
                
                # محاكاة شرط عرض زر الحذف النهائي
                if auth_manager.is_admin() and auth_manager.current_user['id'] != user.get('id'):
                    print(f"      - زر الحذف النهائي: ✅ متاح")
                else:
                    print(f"      - زر الحذف النهائي: ❌ غير متاح")
        else:
            print("❌ لا توجد مستخدمين")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لميزة الحذف النهائي للمستخدمين")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار وظيفة الحذف النهائي
    if test_permanent_delete_functionality():
        tests_passed += 1
        print("\n✅ اختبار وظيفة الحذف النهائي نجح")
    else:
        print("\n❌ اختبار وظيفة الحذف النهائي فشل")
    
    # اختبار تكامل واجهة المستخدم
    if test_ui_integration():
        tests_passed += 1
        print("\n✅ اختبار تكامل واجهة المستخدم نجح")
    else:
        print("\n❌ اختبار تكامل واجهة المستخدم فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 ميزة الحذف النهائي تعمل بشكل مثالي!")
        
        print("\n✨ الميزات الجديدة:")
        print("   🗑️ زر الحذف النهائي للمستخدمين")
        print("   🔒 يظهر فقط للمديرين")
        print("   🚫 منع المستخدم من حذف نفسه")
        print("   ⚠️ مربعات حوار تأكيد متعددة")
        print("   📝 تسجيل عمليات الحذف في سجل الأنشطة")
        print("   🔄 تحديث قائمة المستخدمين تلقائياً")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin / 123456")
        print("3. اذهب إلى إدارة المستخدمين")
        print("4. ستجد زر '🗑️ حذف نهائياً' بجانب كل مستخدم (للمديرين فقط)")
        print("5. انقر على الزر واتبع مربعات الحوار التأكيدية")
        
        print("\n⚠️ تحذيرات مهمة:")
        print("   • الحذف النهائي لا يمكن التراجع عنه")
        print("   • يحذف المستخدم نهائياً من قاعدة البيانات")
        print("   • لا يمكن للمستخدم حذف نفسه")
        print("   • يتطلب تأكيد مزدوج قبل الحذف")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
