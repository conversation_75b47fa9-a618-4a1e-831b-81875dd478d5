#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة استيراد Excel
"""

import pandas as pd
import os
from datetime import datetime

def test_excel_functions():
    """اختبار الوظائف المساعدة لاستيراد Excel"""
    
    print("🧪 اختبار وظائف استيراد Excel...")
    
    # اختبار تحويل المبالغ
    def parse_amount(amount_value):
        try:
            if pd.isna(amount_value):
                return 0
            amount_str = str(amount_value).replace(',', '').replace('ر.س', '').replace('$', '').strip()
            return float(amount_str)
        except:
            return 0
    
    # اختبار تحويل التواريخ
    def parse_date(date_value):
        try:
            if pd.isna(date_value):
                return None
            
            date_str = str(date_value).strip()
            date_formats = [
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%d-%m-%Y',
                '%Y/%m/%d'
            ]
            
            for date_format in date_formats:
                try:
                    return datetime.strptime(date_str, date_format).date()
                except:
                    continue
            
            if hasattr(date_value, 'date'):
                return date_value.date()
            
            return None
        except:
            return None
    
    # اختبار المبالغ
    print("\n💰 اختبار تحويل المبالغ:")
    test_amounts = [1500.50, "2000", "3,500.75", "4000 ر.س", "500$", "", None, "غير صحيح"]
    for amount in test_amounts:
        result = parse_amount(amount)
        print(f"   {amount} -> {result}")
    
    # اختبار التواريخ
    print("\n📅 اختبار تحويل التواريخ:")
    test_dates = ["2024-01-15", "15/01/2024", "01/15/2024", "15-01-2024", "2024/01/15", "تاريخ خاطئ", "", None]
    for date in test_dates:
        result = parse_date(date)
        print(f"   {date} -> {result}")
    
    # اختبار قراءة ملف Excel
    print("\n📊 اختبار قراءة ملف Excel:")
    sample_file = "sample_excel_files/نموذج_واردات.xlsx"
    if os.path.exists(sample_file):
        try:
            df = pd.read_excel(sample_file)
            print(f"   ✅ تم قراءة الملف بنجاح")
            print(f"   📋 عدد الصفوف: {len(df)}")
            print(f"   📋 الأعمدة: {list(df.columns)}")
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['المبلغ', 'الحساب', 'العملة', 'التاريخ', 'الوصف']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"   ❌ أعمدة مفقودة: {missing_columns}")
            else:
                print(f"   ✅ جميع الأعمدة المطلوبة موجودة")
                
                # اختبار معالجة البيانات
                print(f"\n   🔍 معاينة البيانات:")
                for index, row in df.head(3).iterrows():
                    amount = parse_amount(row.get('المبلغ', 0))
                    date = parse_date(row.get('التاريخ', ''))
                    print(f"   الصف {index + 1}: مبلغ={amount}, تاريخ={date}")
                    
        except Exception as e:
            print(f"   ❌ خطأ في قراءة الملف: {e}")
    else:
        print(f"   ⚠️ الملف غير موجود: {sample_file}")
    
    print("\n✅ انتهى الاختبار!")

if __name__ == "__main__":
    test_excel_functions()
