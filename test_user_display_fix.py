#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة عرض المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_display_fix():
    """اختبار إصلاح مشكلة عرض المستخدمين"""
    print("🧪 اختبار إصلاح مشكلة عرض المستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول
        print("\n2. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # 3. اختبار جلب المستخدمين
        print("\n3. اختبار جلب المستخدمين...")
        from database.models import User
        
        users = User.get_all()
        if not users:
            print("❌ لا توجد مستخدمين")
            return False
        
        print(f"✅ تم جلب {len(users)} مستخدم")
        
        # 4. محاكاة دالة load_users_list المحسنة
        print("\n4. محاكاة دالة load_users_list المحسنة...")
        
        successful_cards = 0
        skipped_cards = 0
        
        for i, user in enumerate(users):
            try:
                username = user.get('username', f'مستخدم_{i+1}') if user else f'مستخدم_{i+1}'
                print(f"   معالجة المستخدم {i+1}: {username}")
                
                # التحقق من البيانات الأساسية
                if user and isinstance(user, dict) and user.get('id'):
                    # محاكاة إنشاء البطاقة
                    print(f"      ✅ بيانات صحيحة - سيتم إنشاء البطاقة")
                    successful_cards += 1
                else:
                    print(f"      ⚠️ بيانات ناقصة - سيتم تجاهله")
                    skipped_cards += 1
                    
            except Exception as e:
                print(f"      ❌ مشكلة في المعالجة: {e}")
                skipped_cards += 1

        print(f"   📊 النتائج: {successful_cards} نجح، {skipped_cards} تم تجاهله")
        
        # 5. محاكاة دالة create_user_card المحسنة
        print("\n5. محاكاة دالة create_user_card المحسنة...")
        
        card_creation_errors = 0
        successful_card_creation = 0
        
        for i, user in enumerate(users):
            try:
                username = user.get('username', 'غير معروف')
                print(f"   محاكاة إنشاء بطاقة للمستخدم: {username}")
                
                # محاكاة التحقق من البيانات
                if not user or not isinstance(user, dict):
                    print(f"      ⚠️ بيانات غير صحيحة - سيتم تجاهله")
                    continue
                
                # محاكاة استخراج البيانات
                user_id = user.get('id')
                fullname = user.get('full_name', 'غير محدد')
                role = user.get('role', 'user')
                is_active = user.get('is_active', True)
                
                # محاكاة معالجة تاريخ الإنشاء
                created_at = user.get('created_at')
                if created_at:
                    try:
                        if hasattr(created_at, 'strftime'):
                            created_at_text = created_at.strftime("%Y-%m-%d %H:%M")
                        else:
                            created_at_text = str(created_at)
                    except:
                        created_at_text = "غير محدد"
                else:
                    created_at_text = "غير محدد"
                
                print(f"      ✅ البطاقة ستعرض: {username} - {fullname} - {role}")
                successful_card_creation += 1
                
            except Exception as e:
                print(f"      ⚠️ مشكلة في إنشاء البطاقة: {e}")
                print(f"      💡 سيتم استخدام البطاقة المبسطة أو تجاهل المستخدم")
                card_creation_errors += 1
        
        print(f"   📊 نتائج إنشاء البطاقات: {successful_card_creation} نجح، {card_creation_errors} مشكلة")
        
        # 6. اختبار مكونات واجهة المستخدم
        print("\n6. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            
            # التحقق من الألوان المطلوبة
            required_colors = ['bg_card', 'border', 'text_primary', 'text_secondary', 'error', 'warning']
            available_colors = []
            missing_colors = []
            
            for color in required_colors:
                if color in COLORS:
                    available_colors.append(color)
                else:
                    missing_colors.append(color)
            
            print(f"      ✅ ألوان متاحة: {len(available_colors)}")
            if missing_colors:
                print(f"      ⚠️ ألوان مفقودة: {missing_colors}")
            
            print("✅ مكونات واجهة المستخدم متاحة")
            
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        # 7. محاكاة إنشاء البطاقة المبسطة
        print("\n7. محاكاة إنشاء البطاقة المبسطة...")
        try:
            # محاكاة بيانات مستخدم بمشاكل
            problematic_user = {'username': 'test_user', 'full_name': None, 'role': None}
            
            print("   محاكاة إنشاء بطاقة مبسطة لمستخدم بمشاكل...")
            
            # محاكاة معالجة البيانات في البطاقة المبسطة
            username = str(problematic_user.get('username', 'مستخدم غير معروف'))
            fullname = str(problematic_user.get('full_name', 'غير محدد'))
            
            print(f"      ✅ البطاقة المبسطة ستعرض: {username} - {fullname}")
            print("      ⚠️ مع تحذير: بيانات غير مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة البطاقة المبسطة: {e}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_error_handling():
    """اختبار معالجة أخطاء واجهة المستخدم"""
    print("\n🎨 اختبار معالجة أخطاء واجهة المستخدم")
    print("=" * 40)
    
    try:
        import customtkinter as ctk
        from config.colors import COLORS, BUTTON_STYLES
        from config.fonts import create_rtl_label, create_rtl_button
        
        # إنشاء نافذة تجريبية
        test_window = ctk.CTk()
        test_window.withdraw()
        
        # اختبار إنشاء مكونات مع قيم افتراضية آمنة
        test_frame = ctk.CTkFrame(test_window, fg_color="transparent")
        
        # اختبار إنشاء label مع ألوان آمنة
        test_label = create_rtl_label(
            test_frame,
            text="اختبار",
            font_size='body',
            text_color=COLORS.get('text_primary', '#ffffff')
        )
        
        # اختبار إنشاء زر مع أنماط آمنة
        test_button = create_rtl_button(
            test_frame,
            text="اختبار",
            **BUTTON_STYLES.get('primary', {})
        )
        
        print("✅ جميع مكونات واجهة المستخدم تعمل مع القيم الافتراضية الآمنة")
        
        # إغلاق النافذة التجريبية
        test_window.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة أخطاء واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لإصلاح مشكلة عرض المستخدمين")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار إصلاح عرض المستخدمين
    if test_user_display_fix():
        tests_passed += 1
        print("\n✅ اختبار إصلاح عرض المستخدمين نجح")
    else:
        print("\n❌ اختبار إصلاح عرض المستخدمين فشل")
    
    # اختبار معالجة أخطاء واجهة المستخدم
    if test_ui_error_handling():
        tests_passed += 1
        print("\n✅ اختبار معالجة أخطاء واجهة المستخدم نجح")
    else:
        print("\n❌ اختبار معالجة أخطاء واجهة المستخدم فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 تم إصلاح مشكلة عرض المستخدمين بنجاح!")
        
        print("\n✨ الإصلاحات المطبقة:")
        print("   🔧 إزالة رسائل الخطأ غير الضرورية")
        print("   🛡️ تحسين معالجة الأخطاء في create_user_card")
        print("   📋 إضافة بطاقة مبسطة للمستخدمين بمشاكل")
        print("   🔄 تحسين دالة load_users_list")
        print("   ⚠️ تحويل رسائل الخطأ إلى تحذيرات في وحدة التحكم")
        print("   🎯 عرض رسائل خطأ مفيدة فقط للمشاكل الحرجة")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin / 123456")
        print("3. اذهب إلى إدارة المستخدمين")
        print("4. يجب ألا تظهر رسالة 'خطأ في عرض بيانات المستخدم'")
        print("5. جميع المستخدمين يجب أن يظهروا بشكل صحيح")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
