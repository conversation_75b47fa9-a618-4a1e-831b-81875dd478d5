#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات RTL في نافذة إضافة حساب جديد
يختبر عرض النصوص العربية في نافذة إضافة حساب جديد
"""

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
from config.colors import COLORS, ARABIC_TEXT_STYLES, BUTTON_STYLES

class AddAccountDialogRTLTest:
    """نافذة اختبار إصلاحات RTL في نافذة إضافة حساب جديد"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_test_dialog()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        ctk.set_appearance_mode("light")
        
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاحات RTL - نافذة إضافة حساب جديد")
        self.window.geometry("600x800")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 600
        height = 800
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_dialog(self):
        """إنشاء نافذة الاختبار التي تحاكي نافذة إضافة حساب جديد"""
        # العنوان الرئيسي للاختبار
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار نافذة إضافة حساب جديد",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 10))
        
        # وصف الاختبار
        desc_label = create_rtl_label(
            self.window,
            text="هذا الاختبار يحاكي نافذة إضافة حساب جديد مع النصوص العربية بـ RTL",
            font_size='subtitle',
            text_color=COLORS['text_secondary'],
            **ARABIC_TEXT_STYLES['title']
        )
        desc_label.pack(pady=(0, 20))
        
        # إطار النافذة المحاكية
        dialog_frame = ctk.CTkFrame(self.window, fg_color=COLORS['bg_card'])
        dialog_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))
        
        # عنوان النافذة
        title_label = create_rtl_label(
            dialog_frame,
            text="إضافة حساب جديد",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 30))
        
        # إطار النموذج
        form_frame = ctk.CTkFrame(dialog_frame, fg_color="transparent")
        form_frame.pack(fill="x", padx=30, pady=(0, 10))
        
        # اسم الحساب
        name_label = create_rtl_label(
            form_frame,
            text="اسم الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        name_label.pack(anchor="w", pady=(0, 5))
        
        name_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: حساب الراجحي",
            height=40
        )
        name_entry.pack(fill="x", pady=(0, 15))
        
        # نوع الحساب
        type_label = create_rtl_label(
            form_frame,
            text="نوع الحساب:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        type_label.pack(anchor="w", pady=(0, 5))
        
        type_entry = create_rtl_entry(
            form_frame,
            placeholder_text="مثال: حساب جاري، محفظة نقدية، بطاقة ائتمان، إلخ...",
            height=40
        )
        type_entry.pack(fill="x", pady=(0, 15))
        
        # العملة
        currency_label = create_rtl_label(
            form_frame,
            text="العملة:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        currency_label.pack(anchor="w", pady=(0, 5))
        
        # محاكاة ComboBox للعملة
        currency_options = ["1 - ريال سعودي (ر.س)", "2 - دولار أمريكي ($)", "3 - يورو (€)"]
        currency_combo = ctk.CTkComboBox(
            form_frame,
            values=currency_options,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        currency_combo.pack(fill="x", pady=(0, 15))
        currency_combo.set(currency_options[0])
        
        # الرصيد الابتدائي
        balance_label = create_rtl_label(
            form_frame,
            text="الرصيد الابتدائي:",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        balance_label.pack(anchor="w", pady=(0, 5))
        
        balance_entry = create_rtl_entry(
            form_frame,
            placeholder_text="0.00",
            height=40
        )
        balance_entry.pack(fill="x", pady=(0, 15))
        
        # الوصف
        desc_label = create_rtl_label(
            form_frame,
            text="الوصف (اختياري):",
            font_size='body',
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="w", pady=(0, 5))
        
        desc_entry = ctk.CTkTextbox(
            form_frame,
            height=60,
            font=ctk.CTkFont(size=14)
        )
        desc_entry.pack(fill="x", pady=(0, 5))
        
        # إدراج نص تجريبي في مربع الوصف
        desc_entry.insert("1.0", "هذا مثال على وصف الحساب باللغة العربية مع دعم RTL")
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ctk.CTkFrame(dialog_frame, fg_color="transparent")
        buttons_frame.pack(pady=(20, 30))
        
        save_button = create_rtl_button(
            buttons_frame,
            text="حفظ",
            command=self.show_test_message,
            **BUTTON_STYLES['primary']
        )
        save_button.pack(side="left", padx=(0, 10))
        
        cancel_button = create_rtl_button(
            buttons_frame,
            text="إلغاء",
            command=self.show_test_message,
            **BUTTON_STYLES['secondary']
        )
        cancel_button.pack(side="left")
        
        # إضافة ملاحظة اختبار
        test_note = create_rtl_label(
            dialog_frame,
            text="📝 ملاحظة: جميع النصوص العربية يجب أن تظهر بالاتجاه الصحيح من اليمين إلى اليسار",
            font_size='small',
            text_color=COLORS['info'],
            **ARABIC_TEXT_STYLES['title']
        )
        test_note.pack(pady=(0, 20))
    
    def show_test_message(self):
        """عرض رسالة اختبار"""
        from tkinter import messagebox
        messagebox.showinfo(
            "اختبار الزر", 
            "تم النقر على الزر!\n\nالنصوص العربية تظهر بـ RTL صحيح في:\n• عنوان النافذة\n• تسميات الحقول\n• حقول الإدخال\n• الأزرار"
        )
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار إصلاحات RTL في نافذة إضافة حساب جديد...")
    print("=" * 70)
    print("📋 ما يتم اختباره:")
    print("   ✅ عنوان النافذة: 'إضافة حساب جديد'")
    print("   ✅ تسميات الحقول: 'اسم الحساب'، 'نوع الحساب'، 'العملة'، إلخ")
    print("   ✅ حقول الإدخال مع النصوص التوضيحية العربية")
    print("   ✅ قائمة العملات المنسدلة")
    print("   ✅ مربع النص للوصف")
    print("   ✅ أزرار 'حفظ' و 'إلغاء'")
    print("   ✅ محاذاة جميع العناصر بـ RTL")
    print("=" * 70)
    
    try:
        app = AddAccountDialogRTLTest()
        app.run()
        print("✅ تم إنهاء الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
