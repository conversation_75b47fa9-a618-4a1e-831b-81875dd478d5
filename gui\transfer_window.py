import customtkinter as ctk
from tkinter import messagebox
from decimal import Decimal, InvalidOperation
from datetime import datetime
from database.models import Account, Currency, Transfer
from utils.auth import auth_manager
from utils.exchange_calculator import exchange_calculator

class TransferWindow(ctk.CTkToplevel):
    def __init__(self, parent, on_close_callback):
        super().__init__(parent)
        self.on_close_callback = on_close_callback

        self.title("إضافة تحويل جديد")
        self.geometry("600x550")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()

        self.load_data()
        self.create_widgets()
        self.update_from_currencies() # Initial population

    def load_data(self):
        """تحميل البيانات اللازمة من قاعدة البيانات"""
        print("🔍 [DEBUG] TransferWindow: تحميل البيانات...")

        self.accounts = Account.get_by_user(auth_manager.current_user['id'])
        self.all_currencies = Currency.get_all()

        print(f"🔍 [DEBUG] عدد الحسابات: {len(self.accounts) if self.accounts else 0}")
        print(f"🔍 [DEBUG] عدد العملات: {len(self.all_currencies) if self.all_currencies else 0}")

        # تحميل أرصدة الحسابات
        if self.accounts:
            for account in self.accounts:
                account_id = account['id']
                # الحصول على أرصدة الحساب بجميع العملات
                balances_query = """
                    SELECT ab.currency_id, ab.balance, c.code, c.symbol
                    FROM account_balances ab
                    JOIN currencies c ON ab.currency_id = c.id
                    WHERE ab.account_id = %s AND ab.balance > 0
                    ORDER BY c.code
                """
                from database.connection import db
                balances = db.execute_query(balances_query, (account_id,))
                account['balances'] = balances if balances else []

                print(f"🔍 [DEBUG] الحساب {account['name']}: {len(account['balances'])} عملة")

        self.account_map = {f"{acc['id']} - {acc['name']}": acc for acc in self.accounts}
        self.currency_map = {f"{c['id']} - {c['name']} ({c['symbol']})": c for c in self.all_currencies}

    def create_widgets(self):
        """إنشاء واجهة المستخدم للنافذة"""
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # --- From Section ---
        from_frame = ctk.CTkFrame(main_frame)
        from_frame.pack(fill="x", pady=(0, 10))
        ctk.CTkLabel(from_frame, text="من", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

        ctk.CTkLabel(from_frame, text="الحساب المصدر:").pack()
        self.from_account_combo = ctk.CTkComboBox(from_frame, values=list(self.account_map.keys()), command=self.update_from_currencies)
        self.from_account_combo.pack(fill="x", padx=10)

        ctk.CTkLabel(from_frame, text="العملة والمبلغ:").pack(pady=(10, 5))
        
        currency_amount_frame = ctk.CTkFrame(from_frame, fg_color="transparent")
        currency_amount_frame.pack(fill="x", padx=10)
        
        self.from_amount_entry = ctk.CTkEntry(currency_amount_frame, placeholder_text="المبلغ المرسل")
        self.from_amount_entry.pack(side="left", expand=True, padx=(0, 5))
        
        self.from_currency_combo = ctk.CTkComboBox(currency_amount_frame, values=[], width=180)
        self.from_currency_combo.pack(side="right")

        # --- To Section ---
        to_frame = ctk.CTkFrame(main_frame)
        to_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(to_frame, text="إلى", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

        ctk.CTkLabel(to_frame, text="الحساب الهدف:").pack(pady=(5, 5))
        self.to_account_combo = ctk.CTkComboBox(to_frame, values=list(self.account_map.keys()))
        self.to_account_combo.pack(fill="x", padx=10, pady=(0, 10))

        ctk.CTkLabel(to_frame, text="المبلغ المستقبل:").pack(pady=(5, 5))
        
        self.to_amount_entry = ctk.CTkEntry(to_frame, placeholder_text="المبلغ المستقبل")
        self.to_amount_entry.pack(fill="x", padx=10, pady=(0, 10))
        
        # Exchange Rate Section
        exchange_frame = ctk.CTkFrame(main_frame)
        exchange_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(exchange_frame, text="حاسبة سعر الصرف", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        buttons_frame = ctk.CTkFrame(exchange_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        calculate_btn = ctk.CTkButton(buttons_frame, text="احسب المبلغ المحول تلقائياً", command=self.auto_calculate_amount)
        calculate_btn.pack(side="left", padx=(0, 5))
        
        self.exchange_rate_label = ctk.CTkLabel(buttons_frame, text="")
        self.exchange_rate_label.pack(side="right")
        
        # Description
        desc_frame = ctk.CTkFrame(main_frame)
        desc_frame.pack(fill="x", pady=10)
        ctk.CTkLabel(desc_frame, text="وصف التحويل:").pack(pady=(5, 5))
        self.description_entry = ctk.CTkEntry(desc_frame, placeholder_text="وصف اختياري للتحويل")
        self.description_entry.pack(fill="x", padx=10, pady=(0, 10))

        # --- Buttons --- #
        buttons_bottom_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_bottom_frame.pack(fill="x", pady=20)
        
        # إنشاء إطار للأزرار بجانب بعض
        buttons_row = ctk.CTkFrame(buttons_bottom_frame, fg_color="transparent")
        buttons_row.pack()
        
        save_button = ctk.CTkButton(buttons_row, text="✓ حفظ التحويل", command=self.save_transfer, width=150)
        save_button.pack(side="left", padx=(0, 10))
        
        cancel_button = ctk.CTkButton(buttons_row, text="✗ إلغاء", command=self.destroy, width=100, fg_color="gray")
        cancel_button.pack(side="right")

    def update_from_currencies(self, *args):
        """تحديث قائمة العملات المتاحة بناءً على الحساب المختار"""
        try:
            selected_key = self.from_account_combo.get()
            print(f"🔍 [DEBUG] تحديث العملات للحساب: {selected_key}")

            account = self.account_map.get(selected_key)
            if account:
                balances = account.get('balances', [])
                print(f"🔍 [DEBUG] عدد الأرصدة: {len(balances)}")

                if balances:
                    currency_options = [f"{b['currency_id']} - {b['code']} (الرصيد: {b['balance']:,.2f})" for b in balances]
                else:
                    # إذا لم توجد أرصدة، عرض العملة الافتراضية
                    currency_options = ["1 - SAR (الرصيد: 0.00)"]
                    print("⚠️ [DEBUG] لا توجد أرصدة، استخدام العملة الافتراضية")

                self.from_currency_combo.configure(values=currency_options)
                self.from_currency_combo.set(currency_options[0] if currency_options else "")

                print(f"🔍 [DEBUG] خيارات العملات: {currency_options}")
            else:
                print("❌ [DEBUG] لم يتم العثور على الحساب")
                self.from_currency_combo.configure(values=[])
                self.from_currency_combo.set("")
        except Exception as e:
            print(f"❌ [DEBUG] خطأ في تحديث العملات: {e}")
            import traceback
            traceback.print_exc()
    
    def auto_calculate_amount(self):
        """حساب المبلغ المحول تلقائياً بناءً على سعر الصرف"""
        try:
            if not self.from_amount_entry.get() or not self.from_currency_combo.get():
                messagebox.showwarning("تحذير", "يرجى إدخال المبلغ واختيار العملة أولاً", parent=self)
                return

            from_currency_id = int(self.from_currency_combo.get().split(' - ')[0])
            from_amount = Decimal(self.from_amount_entry.get())
            
            # للتبسيط، نفترض أن التحويل بنفس العملة (1:1)
            # يمكن تطوير هذا لاحقاً لاستخدام أسعار صرف حقيقية
            to_amount = from_amount
            
            self.to_amount_entry.delete(0, 'end')
            self.to_amount_entry.insert(0, str(to_amount))
            
            self.exchange_rate_label.configure(text=f"سعر الصرف: 1:1")
            
        except (ValueError, InvalidOperation):
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح", parent=self)
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}", parent=self)

    def save_transfer(self):
        """حفظ التحويل الجديد مع تسجيل مفصل"""
        try:
            print("🔍 [DEBUG] TransferWindow: بدء عملية حفظ التحويل...")

            # التحقق من البيانات الأساسية
            if not self.from_account_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار الحساب المصدر", parent=self)
                return

            if not self.to_account_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار الحساب الهدف", parent=self)
                return

            if not self.from_currency_combo.get():
                messagebox.showerror("خطأ", "يرجى اختيار العملة", parent=self)
                return

            if not self.from_amount_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال المبلغ", parent=self)
                return

            # استخراج البيانات
            from_account_id = int(self.from_account_combo.get().split(' - ')[0])
            from_currency_id = int(self.from_currency_combo.get().split(' - ')[0])
            from_amount = Decimal(self.from_amount_entry.get().strip())

            to_account_id = int(self.to_account_combo.get().split(' - ')[0])
            to_currency_id = from_currency_id  # استخدام نفس عملة المرسل
            to_amount = Decimal(self.to_amount_entry.get().strip()) if self.to_amount_entry.get().strip() else from_amount

            print(f"🔍 [DEBUG] بيانات التحويل:")
            print(f"   من الحساب: {from_account_id}")
            print(f"   إلى الحساب: {to_account_id}")
            print(f"   العملة: {from_currency_id}")
            print(f"   المبلغ: {from_amount}")

            # التحقق من صحة البيانات
            if from_amount <= 0:
                messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر", parent=self)
                return

            if from_account_id == to_account_id:
                messagebox.showerror("خطأ", "لا يمكن التحويل إلى نفس الحساب", parent=self)
                return

            # التحقق من كفاية الرصيد
            current_balance = Account.get_currency_balance(from_account_id, from_currency_id)
            print(f"🔍 [DEBUG] الرصيد الحالي: {current_balance}")

            if current_balance < from_amount:
                messagebox.showerror("خطأ", f"الرصيد غير كافي. الرصيد المتاح: {current_balance:,.2f}", parent=self)
                return

            print("🔍 [DEBUG] استدعاء Transfer.create...")

            transfer_id = Transfer.create(
                user_id=auth_manager.current_user['id'],
                from_account_id=from_account_id,
                to_account_id=to_account_id,
                from_amount=from_amount,
                from_currency_id=from_currency_id,
                to_amount=to_amount,
                to_currency_id=to_currency_id,
                description=f"تحويل من حساب {from_account_id} إلى حساب {to_account_id}",
                transfer_date=datetime.now().date()
            )

            print(f"🔍 [DEBUG] نتيجة Transfer.create: {transfer_id}")

            if transfer_id > 0:
                print(f"✅ TransferWindow: تم إنشاء التحويل بنجاح! ID: {transfer_id}")
                messagebox.showinfo("نجح", f"تم إجراء التحويل بنجاح\nمعرف التحويل: {transfer_id}", parent=self)
                print("🔄 TransferWindow: استدعاء callback وإغلاق النافذة...")
                self.on_close_callback()
                self.destroy()
                print("✅ TransferWindow: تم إغلاق النافذة")
            else:
                print("❌ TransferWindow: فشل في إنشاء التحويل (transfer_id = 0)")
                messagebox.showerror("خطأ", "فشل في إجراء التحويل. يرجى المحاولة مرة أخرى.", parent=self)

        except (ValueError, InvalidOperation) as e:
            print(f"❌ [DEBUG] خطأ في تحويل البيانات: {e}")
            messagebox.showerror("خطأ", "يرجى إدخال مبالغ صحيحة.", parent=self)
        except Exception as e:
            print(f"❌ [DEBUG] خطأ غير متوقع: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {str(e)}", parent=self)
