#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة إضافة الحسابات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import Account

def test_account_creation_fix():
    """اختبار إصلاح إنشاء الحسابات"""
    print("🔧 اختبار إصلاح إنشاء الحسابات...")
    print("=" * 50)
    
    try:
        # التأكد من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # التحقق من جدول account_types
        account_types = db.execute_query("SELECT * FROM account_types")
        print(f"📊 عدد أنواع الحسابات المتاحة: {len(account_types) if account_types else 0}")
        
        if account_types:
            print("🏦 أنواع الحسابات:")
            for at in account_types:
                print(f"   {at['id']}: {at['name']}")
        
        # التحقق من العملات
        currencies = db.execute_query("SELECT * FROM currencies")
        print(f"💱 عدد العملات المتاحة: {len(currencies) if currencies else 0}")
        
        if currencies:
            print("💰 العملات:")
            for curr in currencies:
                print(f"   {curr['id']}: {curr['name']} ({curr['symbol']})")
        
        # اختبار إنشاء حساب جديد
        print("\n🧪 اختبار إنشاء حساب جديد...")
        
        test_data = {
            'user_id': 1,  # افتراض وجود مستخدم بمعرف 1
            'name': 'حساب اختبار الإصلاح',
            'account_type_id': 1,  # صندوق نقدي
            'currency_id': 1,      # ريال سعودي
            'initial_balance': 500.0,
            'description': 'حساب تجريبي لاختبار الإصلاح'
        }
        
        # محاولة إنشاء الحساب
        result = Account.create(
            user_id=test_data['user_id'],
            name=test_data['name'],
            account_type_id=test_data['account_type_id'],
            currency_id=test_data['currency_id'],
            initial_balance=test_data['initial_balance'],
            description=test_data['description']
        )
        
        if result > 0:
            print(f"✅ تم إنشاء الحساب بنجاح! معرف الحساب: {result}")
            
            # استرجاع الحساب للتأكد
            created_account = Account.get_by_id(result)
            if created_account:
                print("📋 تفاصيل الحساب المُنشأ:")
                print(f"   الاسم: {created_account['name']}")
                print(f"   النوع: {created_account['account_type_name']}")
                print(f"   العملة: {created_account['currency_name']}")
                print(f"   الرصيد: {created_account['initial_balance']}")
                
                # حذف الحساب التجريبي
                delete_result = db.execute_update("DELETE FROM accounts WHERE id = %s", (result,))
                if delete_result > 0:
                    print("🗑️ تم حذف الحساب التجريبي")
                
                return True
            else:
                print("❌ فشل في استرجاع الحساب")
                return False
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_or_create_account_type():
    """اختبار دالة get_or_create_account_type"""
    print("\n🔧 اختبار دالة get_or_create_account_type...")
    
    try:
        # محاكاة الدالة الجديدة
        def get_or_create_account_type(type_name):
            try:
                # البحث عن نوع الحساب الموجود
                query = "SELECT id FROM account_types WHERE name = %s"
                result = db.execute_query(query, (type_name,))
                
                if result:
                    return result[0]['id']
                
                # إنشاء نوع حساب جديد
                insert_query = """
                    INSERT INTO account_types (name, description, icon)
                    VALUES (%s, %s, %s)
                """
                new_id = db.execute_insert(insert_query, (
                    type_name,
                    f"نوع حساب: {type_name}",
                    "💼"
                ))
                
                return new_id if new_id > 0 else None
                
            except Exception as e:
                print(f"خطأ في معالجة نوع الحساب: {e}")
                return None
        
        # اختبار مع نوع موجود
        existing_type_id = get_or_create_account_type("صندوق نقدي")
        print(f"✅ نوع موجود - معرف 'صندوق نقدي': {existing_type_id}")
        
        # اختبار مع نوع جديد
        new_type_name = "حساب اختبار جديد"
        new_type_id = get_or_create_account_type(new_type_name)
        print(f"✅ نوع جديد - معرف '{new_type_name}': {new_type_id}")
        
        # حذف النوع الجديد
        if new_type_id:
            db.execute_update("DELETE FROM account_types WHERE id = %s", (new_type_id,))
            print("🗑️ تم حذف النوع التجريبي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار get_or_create_account_type: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح إضافة الحسابات...")
    
    success1 = test_account_creation_fix()
    success2 = test_get_or_create_account_type()
    
    if success1 and success2:
        print("\n🎉 نجحت جميع الاختبارات! الإصلاح يعمل بشكل صحيح")
        print("\n✅ المشاكل التي تم إصلاحها:")
        print("   1. استخدام account_type_id بدلاً من account_type_name")
        print("   2. استخدام نموذج Account.create() الصحيح")
        print("   3. إضافة دالة get_or_create_account_type()")
        print("   4. معالجة أفضل للأخطاء")
    else:
        print("\n💥 فشل في بعض الاختبارات")
    
    print("\n" + "="*50)
