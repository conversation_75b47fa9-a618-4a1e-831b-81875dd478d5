#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لإضافة الحسابات
"""

import sys
import os
import mysql.connector

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_direct_account_creation():
    """اختبار إنشاء حساب مباشرة"""
    print("🧪 اختبار إنشاء حساب مباشرة...")
    
    try:
        # الاتصال بقاعدة البيانات مباشرة
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        cursor = connection.cursor(dictionary=True)
        
        # إنشاء حساب تجريبي
        account_name = "حساب تجريبي مباشر"
        user_id = 1  # افتراض أن المستخدم admin له ID = 1
        
        print(f"🔍 إنشاء حساب: {account_name}")
        
        # إدراج الحساب
        insert_query = """
            INSERT INTO accounts (user_id, name, account_type_id, description, created_at, updated_at)
            VALUES (%s, %s, %s, %s, NOW(), NOW())
        """
        cursor.execute(insert_query, (user_id, account_name, 1, "حساب تجريبي"))
        account_id = cursor.lastrowid
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب بنجاح (ID: {account_id})")
            
            # إضافة رصيد بالريال السعودي
            balance_query = """
                INSERT INTO account_balances (account_id, currency_id, balance, created_at, updated_at)
                VALUES (%s, %s, %s, NOW(), NOW())
                ON DUPLICATE KEY UPDATE balance = VALUES(balance), updated_at = NOW()
            """
            cursor.execute(balance_query, (account_id, 1, 1000.0))  # SAR
            
            print("✅ تم إضافة رصيد بالريال السعودي: 1000.0")
            
            # فحص الرصيد
            cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account_id, 1))
            balance_result = cursor.fetchone()
            if balance_result:
                print(f"💰 الرصيد المحفوظ: {balance_result['balance']:,.2f} ر.س")
            
            connection.commit()
            
            # حذف الحساب التجريبي
            cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
            connection.commit()
            
            print("🗑️ تم حذف الحساب التجريبي")
            
            return True
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False
    
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

def test_account_model():
    """اختبار نموذج Account"""
    print("\n🧪 اختبار نموذج Account...")
    
    try:
        from database.models import Account
        from utils.auth import auth_manager
        
        # تسجيل دخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        user_id = auth_manager.current_user['id']
        account_name = "حساب تجريبي من النموذج"
        
        print(f"🔍 إنشاء حساب: {account_name}")
        
        # إنشاء الحساب
        account_id = Account.create(
            user_id=user_id,
            name=account_name,
            description="حساب تجريبي"
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب بنجاح (ID: {account_id})")
            
            # إضافة رصيد
            balance_result = Account.add_currency_balance(account_id, 1, 1500.0)
            if balance_result:
                print("✅ تم إضافة رصيد بالريال السعودي: 1500.0")
                
                # فحص الرصيد
                balance = Account.get_currency_balance(account_id, 1)
                print(f"💰 الرصيد المحفوظ: {balance:,.2f} ر.س")
            else:
                print("❌ فشل في إضافة الرصيد")
            
            # حذف الحساب التجريبي
            connection = mysql.connector.connect(
                host='localhost',
                port=3306,
                user='root',
                password='mohdam',
                database='money_manager'
            )
            cursor = connection.cursor()
            cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (account_id,))
            cursor.execute("DELETE FROM accounts WHERE id = %s", (account_id,))
            connection.commit()
            cursor.close()
            connection.close()
            
            print("🗑️ تم حذف الحساب التجريبي")
            
            return True
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_currencies():
    """فحص العملات المتاحة"""
    print("\n🔍 فحص العملات المتاحة...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("SELECT id, code, name, symbol FROM currencies WHERE is_active = 1 ORDER BY name")
        currencies = cursor.fetchall()
        
        print(f"📊 العملات المتاحة ({len(currencies)}):")
        for currency in currencies:
            print(f"   {currency['id']}: {currency['code']} - {currency['name']} ({currency['symbol']})")
        
        cursor.close()
        connection.close()
        
        return len(currencies) >= 4
        
    except Exception as e:
        print(f"❌ خطأ في فحص العملات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("="*50)
    print("🧪 اختبار مبسط لإضافة الحسابات")
    print("="*50)
    
    tests = [
        ("فحص العملات المتاحة", check_currencies),
        ("اختبار إنشاء حساب مباشرة", test_direct_account_creation),
        ("اختبار نموذج Account", test_account_model)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*50)
    print("📊 ملخص النتائج:")
    print("="*50)
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ وظيفة إضافة الحسابات تعمل بشكل صحيح")
        
        print("\n🚀 يمكنك الآن:")
        print("   1. تشغيل التطبيق: python main.py")
        print("   2. تسجيل الدخول: admin / 123456")
        print("   3. إضافة حسابات جديدة بثقة")
        
    else:
        print(f"\n⚠️ {total_tests - passed_tests} اختبارات فشلت")
        print("💡 تأكد من تشغيل XAMPP أو MySQL Server")

if __name__ == "__main__":
    main()
