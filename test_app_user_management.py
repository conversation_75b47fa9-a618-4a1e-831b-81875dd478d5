#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لميزة إدارة المستخدمين في التطبيق
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_app_functionality():
    """اختبار وظائف التطبيق"""
    print("🧪 اختبار مباشر لميزة إدارة المستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. فحص المستخدمين الموجودين
        print("\n2. فحص المستخدمين الموجودين...")
        users = db.execute_query("SELECT username, full_name, role, is_active FROM users")
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        admin_found = False
        for user in users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
            if user['role'] == 'admin' and user['is_active']:
                admin_found = True
        
        if not admin_found:
            print("❌ لا يوجد مدير نشط في النظام")
            return False
        
        # 3. اختبار تسجيل الدخول
        print("\n3. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        # جرب المستخدمين المختلفين
        login_success = False
        admin_username = None
        
        for user in users:
            if user['role'] == 'admin' and user['is_active']:
                username = user['username']
                success, message = auth_manager.login(username, "123456")
                if success:
                    print(f"✅ تسجيل الدخول بـ {username} ناجح")
                    admin_username = username
                    login_success = True
                    break
                else:
                    print(f"⚠️ فشل تسجيل الدخول بـ {username}: {message}")
        
        if not login_success:
            print("❌ فشل تسجيل الدخول بجميع المديرين")
            return False
        
        # 4. اختبار الصلاحيات
        print("\n4. اختبار الصلاحيات...")
        if auth_manager.can_manage_users():
            print("✅ صلاحيات إدارة المستخدمين متاحة")
        else:
            print("❌ صلاحيات إدارة المستخدمين غير متاحة")
            return False
        
        # 5. اختبار User.get_all()
        print("\n5. اختبار User.get_all()...")
        from database.models import User
        model_users = User.get_all()
        
        if model_users and len(model_users) > 0:
            print(f"✅ User.get_all() تعمل بشكل صحيح - {len(model_users)} مستخدم")
            for user in model_users:
                print(f"   - {user['username']} ({user['full_name']})")
        else:
            print("❌ User.get_all() لا تعمل بشكل صحيح")
            return False
        
        # 6. اختبار مكونات واجهة المستخدم
        print("\n6. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
            print("✅ جميع مكونات واجهة المستخدم متاحة")
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        # 7. محاكاة دالة load_users_list
        print("\n7. محاكاة دالة load_users_list...")
        
        # محاكاة الخطوات التي تحدث في load_users_list
        print("   🔄 بدء تحميل قائمة المستخدمين...")
        print("   📊 جلب المستخدمين من قاعدة البيانات...")
        
        users = User.get_all()
        print(f"   📋 تم جلب {len(users)} مستخدم")
        
        if not users:
            print("   ⚠️ لا توجد مستخدمين في النظام")
            return False
        
        print("   🎨 إنشاء بطاقات المستخدمين...")
        for i, user in enumerate(users):
            username = user.get('username', 'غير محدد')
            fullname = user.get('full_name', 'غير محدد')
            role = user.get('role', 'user')
            is_active = user.get('is_active', True)
            
            print(f"      بطاقة {i+1}: {username}")
            print(f"         - الاسم الكامل: {fullname}")
            print(f"         - الدور: {'مدير' if role == 'admin' else 'مستخدم'}")
            print(f"         - الحالة: {'نشط' if is_active else 'معطل'}")
        
        print("   ✅ تم تحميل قائمة المستخدمين بنجاح")
        
        # 8. اختبار إنشاء نافذة إدارة المستخدمين (بدون عرض)
        print("\n8. اختبار إنشاء نافذة إدارة المستخدمين...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة مؤقتة للاختبار
            test_window = ctk.CTk()
            test_window.withdraw()  # إخفاء النافذة
            
            # محاكاة إنشاء إطار المحتوى
            content_frame = ctk.CTkFrame(test_window)
            
            # محاكاة إنشاء إطار قائمة المستخدمين
            users_list_frame = ctk.CTkScrollableFrame(
                content_frame,
                fg_color=COLORS['bg_light'],
                corner_radius=10,
                height=400
            )
            
            print("✅ تم إنشاء مكونات نافذة إدارة المستخدمين بنجاح")
            
            # إغلاق النافذة التجريبية
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة إدارة المستخدمين: {e}")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print(f"\n📋 ملخص النتائج:")
        print(f"   - عدد المستخدمين: {len(users)}")
        print(f"   - المدير المسجل: {admin_username}")
        print(f"   - صلاحيات الإدارة: متاحة")
        print(f"   - مكونات واجهة المستخدم: تعمل")
        print(f"   - دالة User.get_all(): تعمل")
        
        print(f"\n🚀 للاستخدام:")
        print(f"1. شغل التطبيق: python main.py")
        print(f"2. سجل الدخول باستخدام: {admin_username} / 123456")
        print(f"3. انقر على '👥 إدارة المستخدمين'")
        print(f"4. يجب أن تظهر {len(users)} مستخدم في بطاقات منظمة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_admin2_if_needed():
    """إنشاء المستخدم admin2 إذا لم يكن موجوداً"""
    print("\n🔧 التحقق من وجود المستخدم admin2...")
    
    try:
        from database.connection import db
        
        # البحث عن admin2
        admin2_check = db.execute_query("SELECT * FROM users WHERE username = 'admin2'")
        
        if admin2_check:
            print("✅ المستخدم admin2 موجود مسبقاً")
            return True
        
        print("⚠️ المستخدم admin2 غير موجود - سيتم إنشاؤه...")
        
        import bcrypt
        from datetime import datetime
        
        # تشفير كلمة المرور
        password_hash = bcrypt.hashpw("123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # إنشاء المستخدم
        insert_query = """
            INSERT INTO users (username, password_hash, full_name, role, is_active, created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        result = db.execute_insert(insert_query, (
            'admin2', password_hash, 'المدير الجديد', 'admin', True, datetime.now()
        ))
        
        if result > 0:
            print("✅ تم إنشاء المستخدم admin2 بنجاح")
            return True
        else:
            print("❌ فشل في إنشاء المستخدم admin2")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء admin2: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    # إنشاء admin2 إذا لم يكن موجوداً
    create_admin2_if_needed()
    
    # اختبار وظائف التطبيق
    if test_app_functionality():
        print("\n🎊 تم إصلاح ميزة إدارة المستخدمين بنجاح!")
        print("يمكنك الآن استخدام التطبيق بشكل طبيعي.")
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح.")

if __name__ == "__main__":
    main()
