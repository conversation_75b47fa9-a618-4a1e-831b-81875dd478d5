#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح العملات المتعددة في لوحة التحكم
"""

import sys
import os
import mysql.connector
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return None, None

def test_fixed_dashboard_queries():
    """اختبار الاستعلامات المصححة"""
    print("🧪 اختبار الاستعلامات المصححة...")
    
    connection, cursor = connect_to_database()
    if not connection:
        return False
    
    try:
        user_id = 1
        current_month = datetime.now().strftime('%Y-%m')
        
        # 1. اختبار الاستعلام المصحح للأرصدة
        print("\n1️⃣ اختبار استعلام الأرصدة المصحح:")
        
        fixed_balance_query = """
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(a.current_balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN accounts a ON c.id = a.currency_id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            ORDER BY total_balance DESC
        """
        
        cursor.execute(fixed_balance_query, (user_id,))
        fixed_results = cursor.fetchall()
        
        print("   النتائج المصححة:")
        for result in fixed_results:
            if result['total_balance'] > 0:
                print(f"   ✅ {result['name']} ({result['code']}): {result['total_balance']:,.2f} {result['symbol']}")
        
        # 2. اختبار المعاملات الشهرية (لم تتغير)
        print("\n2️⃣ اختبار استعلام المعاملات الشهرية:")
        
        transactions_query = """
            SELECT
                c.code,
                c.symbol,
                t.type,
                SUM(t.amount) as total_amount,
                COUNT(*) as transaction_count
            FROM transactions t
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s
            AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            AND c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol, t.type
            ORDER BY c.code, t.type
        """
        
        cursor.execute(transactions_query, (user_id, current_month))
        transactions_results = cursor.fetchall()
        
        print("   النتائج:")
        if transactions_results:
            for trans in transactions_results:
                type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
                print(f"   ✅ {trans['code']} {type_text}: {trans['transaction_count']} معاملة بقيمة {trans['total_amount']:,.2f} {trans['symbol']}")
        else:
            print("   ⚠️ لا توجد معاملات شهرية")
        
        # 3. محاكاة get_dashboard_stats المصححة
        print("\n3️⃣ محاكاة get_dashboard_stats المصححة:")
        
        dashboard_stats = {
            'currency_balances': fixed_results or [],
            'currency_transactions': transactions_results or [],
            'accounts_count': 10  # سيتم حسابه لاحقاً
        }
        
        print(f"   📊 عدد العملات مع أرصدة: {len([b for b in dashboard_stats['currency_balances'] if b['total_balance'] > 0])}")
        print(f"   📈 عدد أنواع المعاملات الشهرية: {len(dashboard_stats['currency_transactions'])}")
        
        # 4. عرض البطاقات كما ستظهر في لوحة التحكم
        print("\n4️⃣ محاكاة عرض البطاقات في لوحة التحكم:")
        
        print("   💰 بطاقات الأرصدة:")
        currencies_with_balance = [b for b in dashboard_stats['currency_balances'] if b['total_balance'] > 0]
        if currencies_with_balance:
            for balance in currencies_with_balance:
                print(f"      - رصيد {balance['code']}: {balance['total_balance']:,.0f} {balance['symbol']}")
        else:
            print("      ⚠️ لا توجد أرصدة")
        
        print("   📊 بطاقة المعاملات الشهرية:")
        if dashboard_stats['currency_transactions']:
            # تجميع المعاملات حسب العملة
            currency_groups = {}
            for trans in dashboard_stats['currency_transactions']:
                currency_key = trans['code']
                if currency_key not in currency_groups:
                    currency_groups[currency_key] = {'symbol': trans['symbol'], 'income': 0, 'expense': 0}
                
                if trans['type'] == 'income':
                    currency_groups[currency_key]['income'] = trans['total_amount']
                elif trans['type'] == 'expense':
                    currency_groups[currency_key]['expense'] = trans['total_amount']
            
            for currency_code, data in currency_groups.items():
                if data['income'] > 0 or data['expense'] > 0:
                    print(f"      💱 {currency_code}:")
                    if data['income'] > 0:
                        print(f"         📈 الواردات: +{data['income']:,.0f} {data['symbol']}")
                    if data['expense'] > 0:
                        print(f"         📉 المصروفات: -{data['expense']:,.0f} {data['symbol']}")
        else:
            print("      ⚠️ لا توجد معاملات شهرية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cursor.close()
        connection.close()

def test_aed_specific_scenario():
    """اختبار سيناريو الدرهم الإماراتي المحدد"""
    print("\n🧪 اختبار سيناريو الدرهم الإماراتي...")
    
    connection, cursor = connect_to_database()
    if not connection:
        return False
    
    try:
        user_id = 1
        
        # 1. فحص الحسابات بالدرهم الإماراتي
        cursor.execute("""
            SELECT id, name, current_balance
            FROM accounts 
            WHERE currency_id = 2 AND user_id = %s AND is_active = TRUE
        """, (user_id,))
        
        aed_accounts = cursor.fetchall()
        print(f"   📊 حسابات الدرهم الإماراتي: {len(aed_accounts)}")
        
        total_aed = 0
        for account in aed_accounts:
            balance = float(account['current_balance'] or 0)
            total_aed += balance
            print(f"      {account['name']}: {balance:,.2f} د.إ")
        
        print(f"   💰 إجمالي AED: {total_aed:,.2f} د.إ")
        
        # 2. اختبار الاستعلام المصحح للدرهم الإماراتي فقط
        cursor.execute("""
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(a.current_balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN accounts a ON c.id = a.currency_id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE AND c.code = 'AED'
            GROUP BY c.id, c.code, c.name, c.symbol
        """, (user_id,))
        
        aed_query_result = cursor.fetchone()
        
        if aed_query_result:
            query_balance = float(aed_query_result['total_balance'])
            print(f"   🔍 نتيجة الاستعلام المصحح: {query_balance:,.2f} د.إ")
            
            if abs(total_aed - query_balance) < 0.01:
                print(f"   ✅ الاستعلام المصحح يعطي النتيجة الصحيحة")
                return True
            else:
                print(f"   ❌ عدم تطابق: يدوي={total_aed:,.2f} ≠ استعلام={query_balance:,.2f}")
                return False
        else:
            print(f"   ❌ لم يتم العثور على نتائج للدرهم الإماراتي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار AED: {e}")
        return False
    
    finally:
        cursor.close()
        connection.close()

def test_gui_integration():
    """اختبار التكامل مع الواجهة"""
    print("\n🧪 اختبار التكامل مع الواجهة...")
    
    try:
        # استيراد المكونات المطلوبة
        from utils.auth import auth_manager
        from gui.main_window import MainWindow
        import customtkinter as ctk
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # إنشاء نافذة التطبيق (مخفية)
        root = ctk.CTk()
        root.withdraw()
        
        # إنشاء نافذة إدارة الأموال
        app = MainWindow(root)
        
        # اختبار دالة get_dashboard_stats المصححة
        print("   🔍 اختبار get_dashboard_stats المصححة...")
        stats = app.get_dashboard_stats()
        
        print(f"   📊 النتائج:")
        print(f"      - عدد العملات مع أرصدة: {len([b for b in stats['currency_balances'] if b['total_balance'] > 0])}")
        print(f"      - عدد المعاملات الشهرية: {len(stats['currency_transactions'])}")
        print(f"      - عدد الحسابات: {stats['accounts_count']}")
        
        if stats['currency_balances']:
            print(f"   💰 الأرصدة:")
            for balance in stats['currency_balances']:
                if balance['total_balance'] > 0:
                    print(f"      - {balance['code']}: {balance['total_balance']:,.2f} {balance['symbol']}")
        
        # تنظيف
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار إصلاح العملات المتعددة في لوحة التحكم")
    print("="*60)
    
    tests = [
        ("استعلامات قاعدة البيانات المصححة", test_fixed_dashboard_queries),
        ("سيناريو الدرهم الإماراتي", test_aed_specific_scenario),
        ("التكامل مع الواجهة", test_gui_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                print(f"✅ {test_name}: نجح")
                passed_tests += 1
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    print(f"✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ تم إصلاح مشكلة العملات المتعددة بنجاح")
        print("\n💡 الآن لوحة التحكم ستعرض:")
        print("   ✅ جميع العملات الأربع بأرصدتها الصحيحة")
        print("   ✅ المعاملات الشهرية بجميع العملات")
        print("   ✅ تحديث فوري عند إضافة معاملات جديدة")
        print("\n🚀 قم بتشغيل التطبيق واختبر إضافة معاملات بعملات مختلفة")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} اختبارات فشلت")
        print("💡 راجع الأخطاء أعلاه")

if __name__ == "__main__":
    main()
