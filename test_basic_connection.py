#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أساسي للاتصال
"""

import mysql.connector

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor(dictionary=True)
        
        # اختبار بسيط
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        
        if result and result['test'] == 1:
            print("✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح")
            
            # فحص الجداول
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📊 عدد الجداول: {len(tables)}")
            
            # فحص جدول accounts
            cursor.execute("SHOW TABLES LIKE 'accounts'")
            accounts_table = cursor.fetchone()
            if accounts_table:
                print("✅ جدول accounts موجود")
                
                # فحص عدد الحسابات
                cursor.execute("SELECT COUNT(*) as count FROM accounts")
                count_result = cursor.fetchone()
                print(f"📈 عدد الحسابات الحالية: {count_result['count']}")
            else:
                print("❌ جدول accounts مفقود")
            
            # فحص جدول currencies
            cursor.execute("SHOW TABLES LIKE 'currencies'")
            currencies_table = cursor.fetchone()
            if currencies_table:
                print("✅ جدول currencies موجود")
                
                # فحص العملات
                cursor.execute("SELECT id, code, name FROM currencies WHERE is_active = 1")
                currencies = cursor.fetchall()
                print(f"💰 العملات المتاحة: {len(currencies)}")
                for currency in currencies:
                    print(f"   - {currency['code']}: {currency['name']}")
            else:
                print("❌ جدول currencies مفقود")
            
            return True
        else:
            print("❌ فشل في اختبار الاتصال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False
    
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("="*50)
    print("🧪 اختبار أساسي للاتصال")
    print("="*50)
    
    if test_connection():
        print("\n🎉 الاتصال يعمل بشكل صحيح!")
        print("💡 يمكنك الآن تشغيل التطبيق")
    else:
        print("\n❌ هناك مشكلة في الاتصال")
        print("💡 تأكد من تشغيل XAMPP أو MySQL Server")
