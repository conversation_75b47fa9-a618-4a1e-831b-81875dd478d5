#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعامل مع النصوص العربية في Excel
"""

import pandas as pd
import os

def test_arabic_excel():
    """اختبار كتابة وقراءة النصوص العربية في Excel"""
    
    print("🔍 اختبار التعامل مع النصوص العربية في Excel...")
    print("=" * 60)
    
    # 1. إنشاء بيانات تجريبية بالعربية
    test_data = [
        {
            'اسم الحساب': 'الحساب الجاري',
            'نوع الحساب': 'حساب بنكي',
            'العملة': 'ريال سعودي',
            'الرصيد الابتدائي': 10000.50,
            'الوصف': 'الحساب الجاري في البنك الأهلي'
        },
        {
            'اسم الحساب': 'الصندوق النقدي',
            'نوع الحساب': 'صندوق نقدي',
            'العملة': 'ريال سعودي',
            'الرصيد الابتدائي': 2500.00,
            'الوصف': 'النقد المتوفر في المنزل'
        },
        {
            'اسم الحساب': 'محفظة PayPal',
            'نوع الحساب': 'محفظة إلكترونية',
            'العملة': 'دولار أمريكي',
            'الرصيد الابتدائي': 500.75,
            'الوصف': 'محفظة للمعاملات الدولية'
        }
    ]
    
    # 2. إنشاء مجلد للاختبار
    test_dir = 'test_arabic_excel'
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 3. اختبار كتابة Excel بطرق مختلفة
    test_files = []
    
    # الطريقة الأساسية
    try:
        df = pd.DataFrame(test_data)
        file1 = os.path.join(test_dir, 'اختبار_أساسي.xlsx')
        df.to_excel(file1, index=False, engine='openpyxl')
        test_files.append(('اختبار أساسي', file1))
        print("✅ تم إنشاء الملف الأساسي")
    except Exception as e:
        print(f"❌ فشل في إنشاء الملف الأساسي: {e}")
    
    # مع تحديد الترميز
    try:
        file2 = os.path.join(test_dir, 'اختبار_ترميز.xlsx')
        with pd.ExcelWriter(file2, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='البيانات')
        test_files.append(('اختبار الترميز', file2))
        print("✅ تم إنشاء الملف مع تحديد الترميز")
    except Exception as e:
        print(f"❌ فشل في إنشاء الملف مع الترميز: {e}")
    
    # مع خيارات إضافية
    try:
        file3 = os.path.join(test_dir, 'اختبار_متقدم.xlsx')
        with pd.ExcelWriter(file3, engine='openpyxl', options={'strings_to_urls': False}) as writer:
            df.to_excel(writer, index=False, sheet_name='الحسابات')
        test_files.append(('اختبار متقدم', file3))
        print("✅ تم إنشاء الملف المتقدم")
    except Exception as e:
        print(f"❌ فشل في إنشاء الملف المتقدم: {e}")
    
    # 4. اختبار قراءة الملفات
    print(f"\n📖 اختبار قراءة الملفات:")
    for test_name, file_path in test_files:
        print(f"\n🔍 {test_name}: {os.path.basename(file_path)}")
        
        if not os.path.exists(file_path):
            print("❌ الملف غير موجود")
            continue
        
        try:
            # قراءة أساسية
            df_read = pd.read_excel(file_path)
            print(f"✅ تم قراءة الملف - {len(df_read)} صف، {len(df_read.columns)} عمود")
            
            # فحص الأعمدة
            print(f"📋 الأعمدة: {list(df_read.columns)}")
            
            # فحص البيانات
            for idx, row in df_read.head(2).iterrows():
                account_name = row.get('اسم الحساب', 'غير محدد')
                account_type = row.get('نوع الحساب', 'غير محدد')
                print(f"   الصف {idx + 1}: {account_name} - {account_type}")
            
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
    
    # 5. اختبار مع أحرف خاصة
    print(f"\n🔤 اختبار الأحرف الخاصة:")
    special_data = [
        {
            'الاسم': 'حساب "خاص"',
            'النوع': 'نوع، مع فاصلة',
            'الوصف': 'وصف يحتوي على: نقطتين؛ فاصلة منقوطة'
        },
        {
            'الاسم': 'حساب بأرقام ١٢٣',
            'النوع': 'نوع بـ % رموز',
            'الوصف': 'وصف مع رموز @#$'
        }
    ]
    
    try:
        df_special = pd.DataFrame(special_data)
        special_file = os.path.join(test_dir, 'اختبار_أحرف_خاصة.xlsx')
        df_special.to_excel(special_file, index=False, engine='openpyxl')
        
        # قراءة الملف
        df_read_special = pd.read_excel(special_file)
        print("✅ تم إنشاء وقراءة ملف الأحرف الخاصة بنجاح")
        
        for idx, row in df_read_special.iterrows():
            name = row.get('الاسم', '')
            print(f"   ✅ {name}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأحرف الخاصة: {e}")
    
    # 6. نصائح للمستخدم
    print(f"\n💡 نصائح لتجنب مشاكل Excel مع العربية:")
    print("1. استخدم ملفات .xlsx (وليس .xls)")
    print("2. تأكد من حفظ الملف بترميز UTF-8")
    print("3. تجنب الأحرف الخاصة في أسماء الأعمدة")
    print("4. استخدم برنامج Excel أو LibreOffice لفتح الملفات")
    print("5. تأكد من أن نظام التشغيل يدعم العربية")
    
    print(f"\n📁 ملفات الاختبار في المجلد: {test_dir}")
    print("✅ انتهى اختبار النصوص العربية في Excel")

if __name__ == "__main__":
    test_arabic_excel()
