#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة تسجيل الدخول
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login_window():
    """اختبار نافذة تسجيل الدخول"""
    print("🔄 اختبار نافذة تسجيل الدخول...")
    
    try:
        # تشغيل MySQL أولاً
        import subprocess
        print("🔄 تشغيل MySQL...")
        subprocess.Popen([
            "C:\\xampp\\mysql\\bin\\mysqld.exe",
            "--defaults-file=C:\\xampp\\mysql\\bin\\my.ini",
            "--standalone"
        ])
        
        # انتظار قليل
        import time
        time.sleep(3)
        
        # تشغيل نافذة تسجيل الدخول
        from gui.login_window import LoginWindow
        
        print("✅ تم تحميل نافذة تسجيل الدخول")
        print("\n📋 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123456")
        print("   كلمة مرور MySQL: اتركها فارغة")
        print("\n🚀 تشغيل النافذة...")
        
        app = LoginWindow()
        app.run()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار نافذة تسجيل الدخول")
    print("=" * 60)
    
    if test_login_window():
        print("\n🎉 الاختبار نجح!")
    else:
        print("\n❌ الاختبار فشل!")
    
    input("\n🔄 اضغط Enter للخروج...")
