#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استعلامات لوحة التحكم المصححة
"""

import sys
import os
import mysql.connector
from mysql.connector import Error
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def test_fixed_dashboard_queries(cursor):
    """اختبار الاستعلامات المصححة للوحة التحكم"""
    print("🧪 اختبار الاستعلامات المصححة للوحة التحكم...")
    
    try:
        user_id = 1
        current_month = datetime.now().strftime('%Y-%m')
        
        # 1. اختبار استعلام الأرصدة المصحح
        print("1️⃣ اختبار استعلام الأرصدة المصحح:")
        balance_query = """
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            HAVING total_balance > 0
            ORDER BY total_balance DESC
        """
        cursor.execute(balance_query, (user_id,))
        balance_results = cursor.fetchall()
        
        print("   النتائج:")
        if balance_results:
            for balance in balance_results:
                print(f"   ✅ {balance['name']} ({balance['code']}): {balance['total_balance']:,.2f} {balance['symbol']}")
        else:
            print("   ❌ لا توجد أرصدة!")
        
        # 2. اختبار استعلام المعاملات الشهرية المصحح
        print("\n2️⃣ اختبار استعلام المعاملات الشهرية المصحح:")
        transactions_query = """
            SELECT
                c.code,
                c.symbol,
                t.type,
                SUM(t.amount) as total_amount,
                COUNT(*) as transaction_count
            FROM transactions t
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s
            AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            AND c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol, t.type
            ORDER BY c.code, t.type
        """
        cursor.execute(transactions_query, (user_id, current_month))
        transactions_results = cursor.fetchall()
        
        print("   النتائج:")
        if transactions_results:
            for trans in transactions_results:
                type_text = "واردات" if trans['type'] == 'income' else "مصروفات"
                print(f"   ✅ {trans['code']} {type_text}: {trans['transaction_count']} معاملة بقيمة {trans['total_amount']:,.2f} {trans['symbol']}")
        else:
            print("   ❌ لا توجد معاملات شهرية!")
        
        # 3. محاكاة دالة get_dashboard_stats المصححة
        print("\n3️⃣ محاكاة دالة get_dashboard_stats المصححة:")
        
        # عدد الحسابات
        cursor.execute("SELECT COUNT(*) as accounts_count FROM accounts WHERE user_id = %s AND is_active = TRUE", (user_id,))
        accounts_count = cursor.fetchone()['accounts_count']
        
        dashboard_stats = {
            'currency_balances': balance_results or [],
            'currency_transactions': transactions_results or [],
            'accounts_count': accounts_count
        }
        
        print(f"   📊 عدد الحسابات: {dashboard_stats['accounts_count']}")
        print(f"   💰 عدد العملات مع أرصدة: {len(dashboard_stats['currency_balances'])}")
        print(f"   📈 عدد أنواع المعاملات الشهرية: {len(dashboard_stats['currency_transactions'])}")
        
        # 4. محاكاة عرض البطاقات
        print("\n4️⃣ محاكاة عرض البطاقات في لوحة التحكم:")
        
        # بطاقة عدد الحسابات
        print(f"   🏦 بطاقة الحسابات: {dashboard_stats['accounts_count']} حساب")
        
        # بطاقات الأرصدة
        print("   💰 بطاقات الأرصدة:")
        for balance in dashboard_stats['currency_balances']:
            print(f"      - {balance['name']} ({balance['code']}): {balance['total_balance']:,.0f} {balance['symbol']}")
        
        # بطاقة المعاملات الشهرية
        print("   📊 بطاقة المعاملات الشهرية:")
        if dashboard_stats['currency_transactions']:
            # تجميع المعاملات حسب العملة
            currency_groups = {}
            for trans in dashboard_stats['currency_transactions']:
                currency_key = trans['code']
                if currency_key not in currency_groups:
                    currency_groups[currency_key] = {'symbol': trans['symbol'], 'income': 0, 'expense': 0}
                
                if trans['type'] == 'income':
                    currency_groups[currency_key]['income'] = trans['total_amount']
                elif trans['type'] == 'expense':
                    currency_groups[currency_key]['expense'] = trans['total_amount']
            
            for currency_code, data in currency_groups.items():
                if data['income'] > 0 or data['expense'] > 0:
                    print(f"      💱 {currency_code}:")
                    if data['income'] > 0:
                        print(f"         📈 الواردات: +{data['income']:,.0f} {data['symbol']}")
                    if data['expense'] > 0:
                        print(f"         📉 المصروفات: -{data['expense']:,.0f} {data['symbol']}")
        else:
            print("      ⚠️ لا توجد معاملات شهرية")
        
        return dashboard_stats
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستعلامات: {e}")
        import traceback
        traceback.print_exc()
        return None

def add_test_transaction_and_verify(cursor, connection):
    """إضافة معاملة تجريبية والتحقق من ظهورها"""
    print("\n🧪 اختبار إضافة معاملة والتحقق من ظهورها...")
    
    try:
        user_id = 1
        today = datetime.now().strftime('%Y-%m-%d')
        
        # البحث عن حساب بالدولار الأمريكي
        cursor.execute("""
            SELECT id, name FROM accounts 
            WHERE currency_id = 4 AND user_id = %s AND is_active = TRUE 
            LIMIT 1
        """, (user_id,))
        
        usd_account = cursor.fetchone()
        
        if not usd_account:
            print("   ⚠️ لا يوجد حساب بالدولار الأمريكي")
            return False
        
        # الحصول على الإحصائيات قبل الإضافة
        print("   📊 الإحصائيات قبل إضافة المعاملة:")
        stats_before = test_fixed_dashboard_queries(cursor)
        
        # إضافة معاملة تجريبية بالدولار
        test_amount = 500.00
        cursor.execute("""
            INSERT INTO transactions (user_id, account_id, currency_id, type, amount, description, transaction_date, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (user_id, usd_account['id'], 4, 'income', test_amount, 
              f'وارد تجريبي USD - {datetime.now().strftime("%H:%M:%S")}', today))
        
        transaction_id = cursor.lastrowid
        connection.commit()
        
        print(f"   ✅ تم إضافة معاملة تجريبية (ID: {transaction_id}) بمبلغ {test_amount:,.2f} $")
        
        # الحصول على الإحصائيات بعد الإضافة
        print("\n   📊 الإحصائيات بعد إضافة المعاملة:")
        stats_after = test_fixed_dashboard_queries(cursor)
        
        # مقارنة النتائج
        if stats_before and stats_after:
            print("\n   🔍 مقارنة النتائج:")
            
            # مقارنة عدد المعاملات USD
            usd_before = len([t for t in stats_before['currency_transactions'] if t['code'] == 'USD' and t['type'] == 'income'])
            usd_after = len([t for t in stats_after['currency_transactions'] if t['code'] == 'USD' and t['type'] == 'income'])
            
            print(f"      - معاملات USD income قبل: {usd_before}")
            print(f"      - معاملات USD income بعد: {usd_after}")
            
            if usd_after > usd_before:
                print("      ✅ المعاملة ظهرت في الإحصائيات!")
            else:
                print("      ❌ المعاملة لم تظهر في الإحصائيات!")
        
        # حذف المعاملة التجريبية
        cursor.execute("DELETE FROM transactions WHERE id = %s", (transaction_id,))
        connection.commit()
        print(f"   🗑️ تم حذف المعاملة التجريبية (ID: {transaction_id})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إضافة المعاملة: {e}")
        connection.rollback()
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار استعلامات لوحة التحكم المصححة")
    print("="*60)
    
    # الاتصال بقاعدة البيانات
    connection, cursor = connect_to_database()
    
    if not connection:
        print("❌ لا يمكن المتابعة بدون اتصال بقاعدة البيانات")
        return
    
    try:
        # اختبار الاستعلامات المصححة
        stats = test_fixed_dashboard_queries(cursor)
        
        if stats:
            # اختبار إضافة معاملة والتحقق من ظهورها
            test_success = add_test_transaction_and_verify(cursor, connection)
            
            print("\n" + "="*60)
            print("📋 ملخص نتائج الاختبار:")
            print(f"✅ استعلامات لوحة التحكم: {'نجحت' if stats else 'فشلت'}")
            print(f"✅ اختبار إضافة معاملة: {'نجح' if test_success else 'فشل'}")
            
            if stats and test_success:
                print("\n🎉 جميع الاختبارات نجحت!")
                print("💡 الآن لوحة التحكم ستعرض:")
                print("   - جميع العملات الأربع في بطاقات الأرصدة")
                print("   - المعاملات الشهرية بجميع العملات")
                print("   - تحديث فوري عند إضافة معاملات جديدة")
                print("\n🚀 قم بتشغيل التطبيق لرؤية التحسينات!")
            else:
                print("\n⚠️ توجد مشاكل تحتاج إلى مراجعة")
        
        print("="*60)
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    main()
