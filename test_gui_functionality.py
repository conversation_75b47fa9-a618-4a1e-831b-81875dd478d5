#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف الواجهة بعد الإصلاحات
"""

import sys
import os
import mysql.connector
from mysql.connector import Error

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def test_dashboard_queries(cursor):
    """اختبار استعلامات لوحة التحكم"""
    print("🏠 اختبار استعلامات لوحة التحكم...")
    
    user_id = 1
    current_month = "2025-01"
    
    try:
        # 1. إجمالي الرصيد
        balance_query = """
            SELECT COALESCE(SUM(ab.balance), 0) as total_balance
            FROM account_balances ab
            JOIN accounts a ON ab.account_id = a.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            AND ab.currency_id = 1
        """
        cursor.execute(balance_query, (user_id,))
        balance_result = cursor.fetchone()
        print(f"   ✅ إجمالي الرصيد: {balance_result['total_balance']:,.2f} ر.س")
        
        # 2. الواردات الشهرية
        income_query = """
            SELECT COALESCE(SUM(amount), 0) as monthly_income
            FROM transactions
            WHERE user_id = %s AND type = 'income'
            AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
        """
        cursor.execute(income_query, (user_id, current_month))
        income_result = cursor.fetchone()
        print(f"   ✅ الواردات الشهرية: {income_result['monthly_income']:,.2f} ر.س")
        
        # 3. المصروفات الشهرية
        expense_query = """
            SELECT COALESCE(SUM(amount), 0) as monthly_expense
            FROM transactions
            WHERE user_id = %s AND type = 'expense'
            AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
        """
        cursor.execute(expense_query, (user_id, current_month))
        expense_result = cursor.fetchone()
        print(f"   ✅ المصروفات الشهرية: {expense_result['monthly_expense']:,.2f} ر.س")
        
        # 4. عدد الحسابات
        accounts_query = """
            SELECT COUNT(*) as accounts_count
            FROM accounts
            WHERE user_id = %s AND is_active = TRUE
        """
        cursor.execute(accounts_query, (user_id,))
        accounts_result = cursor.fetchone()
        print(f"   ✅ عدد الحسابات: {accounts_result['accounts_count']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استعلامات لوحة التحكم: {e}")
        return False

def test_income_queries(cursor):
    """اختبار استعلامات الواردات"""
    print("\n📈 اختبار استعلامات الواردات...")
    
    user_id = 1
    
    try:
        # استعلام الواردات
        query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.type = 'income'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 10
        """
        cursor.execute(query, (user_id,))
        incomes = cursor.fetchall()
        
        print(f"   ✅ عدد الواردات: {len(incomes)}")
        
        if incomes:
            total_income = sum(income['amount'] for income in incomes)
            print(f"   ✅ إجمالي أول 10 واردات: {total_income:,.2f}")
            
            print("   📝 عينة من الواردات:")
            for i, income in enumerate(incomes[:3], 1):
                print(f"      {i}. {income['description']}: {income['amount']:,.2f} {income['currency_symbol']} ({income['account_name']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استعلامات الواردات: {e}")
        return False

def test_expense_queries(cursor):
    """اختبار استعلامات المصروفات"""
    print("\n📉 اختبار استعلامات المصروفات...")
    
    user_id = 1
    
    try:
        # استعلام المصروفات
        query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.type = 'expense'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 10
        """
        cursor.execute(query, (user_id,))
        expenses = cursor.fetchall()
        
        print(f"   ✅ عدد المصروفات: {len(expenses)}")
        
        if expenses:
            total_expense = sum(expense['amount'] for expense in expenses)
            print(f"   ✅ إجمالي أول 10 مصروفات: {total_expense:,.2f}")
            
            print("   📝 عينة من المصروفات:")
            for i, expense in enumerate(expenses[:3], 1):
                print(f"      {i}. {expense['description']}: {expense['amount']:,.2f} {expense['currency_symbol']} ({expense['account_name']})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استعلامات المصروفات: {e}")
        return False

def test_reports_queries(cursor):
    """اختبار استعلامات التقارير"""
    print("\n📊 اختبار استعلامات التقارير...")
    
    user_id = 1
    current_month = "2025-01"
    
    try:
        # 1. تقرير الملخص المالي
        summary_query = """
            SELECT 
                COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
                COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expense
            FROM transactions
            WHERE user_id = %s
            AND DATE_FORMAT(transaction_date, '%Y-%m') = %s
        """
        cursor.execute(summary_query, (user_id, current_month))
        summary = cursor.fetchone()
        
        net_income = summary['total_income'] - summary['total_expense']
        print(f"   ✅ إجمالي الواردات: {summary['total_income']:,.2f} ر.س")
        print(f"   ✅ إجمالي المصروفات: {summary['total_expense']:,.2f} ر.س")
        print(f"   ✅ صافي الدخل: {net_income:,.2f} ر.س")
        
        # 2. تقرير الحسابات
        accounts_query = """
            SELECT a.name, a.current_balance, c.symbol
            FROM accounts a
            JOIN currencies c ON a.currency_id = c.id
            WHERE a.user_id = %s AND a.is_active = TRUE
            ORDER BY a.current_balance DESC
        """
        cursor.execute(accounts_query, (user_id,))
        accounts = cursor.fetchall()
        
        print(f"\n   📋 تقرير الحسابات ({len(accounts)} حساب):")
        for account in accounts[:5]:  # أول 5 حسابات
            print(f"      - {account['name']}: {account['current_balance']:,.2f} {account['symbol']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في استعلامات التقارير: {e}")
        return False

def test_authentication():
    """اختبار المصادقة"""
    print("\n🔐 اختبار المصادقة...")
    
    try:
        from utils.auth import auth_manager
        
        # اختبار تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        
        if success:
            print("   ✅ تسجيل الدخول نجح")
            print(f"   ✅ المستخدم الحالي: {auth_manager.current_user['username']}")
            print(f"   ✅ الدور: {auth_manager.current_user['role']}")
            return True
        else:
            print(f"   ❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار المصادقة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار وظائف الواجهة بعد الإصلاحات")
    print("="*60)

    # الاتصال بقاعدة البيانات أولاً
    connection, cursor = connect_to_database()

    if not connection:
        print("❌ لا يمكن المتابعة بدون اتصال بقاعدة البيانات")
        return

    # اختبار المصادقة
    auth_success = test_authentication()

    if not auth_success:
        print("\n⚠️ فشل في اختبار المصادقة - سنتابع اختبار قاعدة البيانات")
    
    try:
        # اختبار جميع الوظائف
        tests = [
            ("لوحة التحكم", test_dashboard_queries),
            ("الواردات", test_income_queries),
            ("المصروفات", test_expense_queries),
            ("التقارير", test_reports_queries),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            if test_function(cursor):
                passed_tests += 1
        
        # النتائج النهائية
        print("\n" + "="*60)
        print("📋 نتائج الاختبارات")
        print("="*60)
        print(f"✅ اختبارات نجحت: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            print("🎉 جميع الاختبارات نجحت! الواجهة جاهزة للاستخدام")
            print("\n💡 يمكنك الآن:")
            print("   1. تشغيل التطبيق: python main.py")
            print("   2. تسجيل الدخول: admin / 123456")
            print("   3. استخدام جميع الوظائف المالية")
        else:
            print("⚠️ بعض الاختبارات فشلت - قد تحتاج إلى إصلاحات إضافية")
        
        print("="*60)
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    main()
