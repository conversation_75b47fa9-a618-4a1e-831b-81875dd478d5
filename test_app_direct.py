#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للتطبيق مع كلمة المرور الصحيحة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل التطبيق مع التسجيل المفصل"""
    print("="*60)
    print("🚀 تشغيل التطبيق مع التسجيل المفصل")
    print("="*60)
    
    print("💡 تعليمات الاختبار:")
    print("1. سيتم تشغيل التطبيق الآن")
    print("2. سجل الدخول باستخدام:")
    print("   - المستخدم: admin")
    print("   - كلمة المرور: 123456")
    print("3. اذهب إلى لوحة التحكم")
    print("4. راقب رسائل التسجيل في هذه النافذة")
    print("5. جرب إضافة معاملة جديدة بعملة غير الريال السعودي")
    print("6. تحقق من تحديث لوحة التحكم")
    print()
    print("🔍 رسائل التسجيل ستظهر هنا:")
    print("-" * 40)
    
    try:
        # تشغيل التطبيق مباشرة
        os.system("python main.py")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
