#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة KeyError في نافذة تسجيل الدخول
"""

import sys
import os

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_colors_import():
    """اختبار استيراد الألوان"""
    print("🔍 اختبار استيراد الألوان...")
    
    try:
        from config.colors import COLORS, BUTTON_STYLES
        
        # فحص الألوان المطلوبة
        required_colors = ['success', 'error', 'warning', 'text_secondary']
        missing_colors = []
        
        for color in required_colors:
            if color in COLORS:
                print(f"✅ COLORS['{color}'] = {COLORS[color]}")
            else:
                missing_colors.append(color)
                print(f"❌ COLORS['{color}'] مفقود")
        
        # فحص أنماط الأزرار
        required_button_styles = ['danger']
        missing_button_styles = []
        
        for style in required_button_styles:
            if style in BUTTON_STYLES:
                print(f"✅ BUTTON_STYLES['{style}'] موجود")
            else:
                missing_button_styles.append(style)
                print(f"❌ BUTTON_STYLES['{style}'] مفقود")
        
        # فحص عدم وجود 'danger' في COLORS
        if 'danger' not in COLORS:
            print("✅ COLORS['danger'] غير موجود (هذا صحيح)")
        else:
            print("⚠️ COLORS['danger'] موجود (قد يسبب تعارض)")
        
        return len(missing_colors) == 0 and len(missing_button_styles) == 0
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الألوان: {e}")
        return False

def test_login_window_update_status():
    """اختبار دالة update_status في نافذة تسجيل الدخول"""
    print("\n🔍 اختبار دالة update_status...")
    
    try:
        from config.colors import COLORS
        
        # محاكاة دالة update_status
        def mock_update_status(message, status_type="info"):
            colors = {
                "success": COLORS['success'],
                "error": COLORS['error'],  # استخدام 'error' بدلاً من 'danger'
                "warning": COLORS['warning'],
                "info": COLORS['text_secondary']
            }
            
            color = colors.get(status_type, COLORS['text_secondary'])
            return color
        
        # اختبار جميع أنواع الحالات
        test_cases = [
            ("رسالة نجاح", "success"),
            ("رسالة خطأ", "error"),
            ("رسالة تحذير", "warning"),
            ("رسالة معلومات", "info"),
            ("رسالة غير معروفة", "unknown")
        ]
        
        all_passed = True
        
        for message, status_type in test_cases:
            try:
                color = mock_update_status(message, status_type)
                print(f"✅ {status_type}: {message} -> {color}")
            except Exception as e:
                print(f"❌ {status_type}: خطأ -> {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ خطأ في اختبار update_status: {e}")
        return False

def test_database_config_error_handling():
    """اختبار معالجة أخطاء إعدادات قاعدة البيانات"""
    print("\n🔍 اختبار معالجة أخطاء إعدادات قاعدة البيانات...")
    
    try:
        from config.database_config import db_config
        
        # اختبار إعدادات غير صحيحة
        invalid_configs = [
            {},  # إعدادات فارغة
            {"host": ""},  # خادم فارغ
            {"host": "localhost", "port": "invalid"},  # منفذ غير صحيح
            {"host": "localhost", "port": 3306, "database": ""},  # قاعدة بيانات فارغة
        ]
        
        for i, config in enumerate(invalid_configs):
            try:
                success, message = db_config.validate_config(config)
                if not success:
                    print(f"✅ اختبار {i+1}: تم رفض الإعدادات غير الصحيحة - {message}")
                else:
                    print(f"⚠️ اختبار {i+1}: تم قبول إعدادات غير صحيحة")
            except Exception as e:
                print(f"❌ اختبار {i+1}: خطأ في التحقق - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الأخطاء: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إصلاح مشكلة KeyError في نافذة تسجيل الدخول")
    print("=" * 60)
    
    tests = [
        ("استيراد الألوان", test_colors_import),
        ("دالة update_status", test_login_window_update_status),
        ("معالجة أخطاء قاعدة البيانات", test_database_config_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! تم إصلاح مشكلة KeyError.")
    else:
        print("⚠️ بعض الاختبارات فشلت. قد تحتاج إلى مراجعة الإصلاحات.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
