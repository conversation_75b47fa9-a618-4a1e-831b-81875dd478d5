#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات RTL في نوافذ المحتوى
يختبر عرض النصوص العربية في نوافذ لوحة التحكم والواردات والمصروفات
"""

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button
from config.colors import COLORS, ARABIC_TEXT_STYLES, BUTTON_STYLES

class RTLContentTestWindow:
    """نافذة اختبار إصلاحات RTL في نوافذ المحتوى"""
    
    def __init__(self):
        self.window = None
        self.setup_window()
        self.create_test_interface()
    
    def setup_window(self):
        """إعداد النافذة"""
        ctk.set_appearance_mode("light")
        
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاحات RTL - نوافذ المحتوى")
        self.window.geometry("800x600")
        self.window.configure(fg_color=COLORS['bg_light'])
        
        # توسيط النافذة
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = 800
        height = 600
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        # العنوان الرئيسي
        main_title = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاحات RTL في نوافذ المحتوى",
            font_size='title',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        main_title.pack(pady=(20, 30))
        
        # إطار التبويبات
        tabs_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        tabs_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # إنشاء التبويبات
        self.create_dashboard_test(tabs_frame)
        self.create_income_test(tabs_frame)
        self.create_expense_test(tabs_frame)
    
    def create_dashboard_test(self, parent):
        """اختبار لوحة التحكم"""
        dashboard_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        dashboard_frame.pack(fill="x", pady=10)
        
        # عنوان القسم
        title = create_rtl_label(
            dashboard_frame,
            text="🏠 لوحة التحكم",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(15, 10))
        
        # بطاقات الإحصائيات
        stats_frame = ctk.CTkFrame(dashboard_frame, fg_color="transparent")
        stats_frame.pack(fill="x", padx=20, pady=10)
        
        stats_data = [
            ("إجمالي الرصيد", "15,250.00 ر.س", COLORS['success'], "💰"),
            ("الواردات الشهرية", "8,500.00 ر.س", COLORS['info'], "📈"),
            ("المصروفات الشهرية", "3,200.00 ر.س", COLORS['error'], "📉"),
            ("عدد الحسابات", "5", COLORS['primary'], "🏦"),
        ]
        
        for i, (title_text, value, color, icon) in enumerate(stats_data):
            card = ctk.CTkFrame(stats_frame, fg_color=COLORS['bg_light'])
            card.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
            stats_frame.grid_columnconfigure(i, weight=1)
            
            # الأيقونة
            icon_label = create_rtl_label(
                card,
                text=icon,
                font_size='header',
                **ARABIC_TEXT_STYLES['title']
            )
            icon_label.pack(pady=(10, 5))
            
            # القيمة
            value_label = create_rtl_label(
                card,
                text=value,
                font_size='header',
                text_color=color,
                **ARABIC_TEXT_STYLES['title']
            )
            value_label.pack()
            
            # العنوان
            title_label = create_rtl_label(
                card,
                text=title_text,
                font_size='small',
                text_color=COLORS['text_secondary'],
                **ARABIC_TEXT_STYLES['title']
            )
            title_label.pack(pady=(0, 10))
        
        # المعاملات الأخيرة
        transactions_title = create_rtl_label(
            dashboard_frame,
            text="المعاملات الأخيرة",
            font_size='header',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        transactions_title.pack(pady=(15, 5))
        
        # عنصر معاملة تجريبي
        transaction_frame = ctk.CTkFrame(dashboard_frame, fg_color=COLORS['bg_light'])
        transaction_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        desc_label = create_rtl_label(
            transaction_frame,
            text="راتب شهر ديسمبر",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="e", padx=15, pady=(10, 5))
        
        amount_label = create_rtl_label(
            transaction_frame,
            text="+5,000.00 ر.س",
            font_size='header',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['title']
        )
        amount_label.pack(pady=(0, 10))
    
    def create_income_test(self, parent):
        """اختبار الواردات"""
        income_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        income_frame.pack(fill="x", pady=10)
        
        # عنوان القسم
        title = create_rtl_label(
            income_frame,
            text="💰 إدارة الواردات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(15, 10))
        
        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(income_frame, fg_color="transparent")
        buttons_frame.pack(pady=10)
        
        add_button = create_rtl_button(
            buttons_frame,
            text="+ إضافة وارد جديد",
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))
        
        import_button = create_rtl_button(
            buttons_frame,
            text="📂 استيراد من Excel",
            **BUTTON_STYLES['secondary']
        )
        import_button.pack(side="left")
        
        # عنصر وارد تجريبي
        income_item = ctk.CTkFrame(income_frame, fg_color=COLORS['bg_light'])
        income_item.pack(fill="x", padx=20, pady=(10, 15))
        
        desc_label = create_rtl_label(
            income_item,
            text="مكافأة نهاية العام",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="e", padx=15, pady=(10, 5))
        
        amount_label = create_rtl_label(
            income_item,
            text="+2,500.00 ر.س",
            font_size='header',
            text_color=COLORS['success'],
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(anchor="e", padx=15)
        
        account_label = create_rtl_label(
            income_item,
            text="الحساب: البنك الأهلي",
            font_size='small',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="e", padx=15, pady=(0, 10))
    
    def create_expense_test(self, parent):
        """اختبار المصروفات"""
        expense_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'])
        expense_frame.pack(fill="x", pady=10)
        
        # عنوان القسم
        title = create_rtl_label(
            expense_frame,
            text="💸 إدارة المصروفات",
            font_size='subtitle',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title.pack(pady=(15, 10))
        
        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(expense_frame, fg_color="transparent")
        buttons_frame.pack(pady=10)
        
        add_button = create_rtl_button(
            buttons_frame,
            text="+ إضافة مصروف جديد",
            **BUTTON_STYLES['primary']
        )
        add_button.pack(side="left", padx=(0, 10))
        
        import_button = create_rtl_button(
            buttons_frame,
            text="📂 استيراد من Excel",
            **BUTTON_STYLES['secondary']
        )
        import_button.pack(side="left")
        
        # عنصر مصروف تجريبي
        expense_item = ctk.CTkFrame(expense_frame, fg_color=COLORS['bg_light'])
        expense_item.pack(fill="x", padx=20, pady=(10, 15))
        
        desc_label = create_rtl_label(
            expense_item,
            text="فاتورة الكهرباء",
            font_size='body',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['label']
        )
        desc_label.pack(anchor="e", padx=15, pady=(10, 5))
        
        amount_label = create_rtl_label(
            expense_item,
            text="-450.00 ر.س",
            font_size='header',
            text_color=COLORS['error'],
            **ARABIC_TEXT_STYLES['label']
        )
        amount_label.pack(anchor="e", padx=15)
        
        account_label = create_rtl_label(
            expense_item,
            text="الحساب: محفظة نقدية",
            font_size='small',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        account_label.pack(anchor="e", padx=15, pady=(0, 10))
    
    def run(self):
        """تشغيل النافذة"""
        self.window.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار إصلاحات RTL في نوافذ المحتوى...")
    print("=" * 50)
    print("📋 ما يتم اختباره:")
    print("   ✅ عرض النصوص العربية بـ RTL في لوحة التحكم")
    print("   ✅ عرض النصوص العربية بـ RTL في نافذة الواردات")
    print("   ✅ عرض النصوص العربية بـ RTL في نافذة المصروفات")
    print("   ✅ محاذاة الأزرار والتسميات بشكل صحيح")
    print("=" * 50)
    
    try:
        app = RTLContentTestWindow()
        app.run()
        print("✅ تم إنهاء الاختبار بنجاح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
