#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة اتصال قاعدة البيانات في نافذة تسجيل الدخول
"""

import sys
import os

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_config():
    """اختبار إعدادات قاعدة البيانات"""
    print("🔍 اختبار إعدادات قاعدة البيانات...")
    
    try:
        from config.database_config import db_config
        
        # عرض الإعدادات الحالية
        current_config = db_config.get_config()
        print(f"📋 الإعدادات الحالية:")
        print(f"   • الخادم: {current_config['host']}:{current_config['port']}")
        print(f"   • قاعدة البيانات: {current_config['database']}")
        print(f"   • المستخدم: {current_config['user']}")
        print(f"   • كلمة المرور: {'***' if current_config['password'] else '(فارغة)'}")
        
        # اختبار الاتصال
        print("\n🔄 اختبار الاتصال...")
        success, message = db_config.test_connection()
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات قاعدة البيانات: {e}")
        return False

def test_database_connection():
    """اختبار اتصال قاعدة البيانات"""
    print("\n🔍 اختبار اتصال قاعدة البيانات...")
    
    try:
        from database.connection import db
        
        # اختبار الاتصال
        print("🔄 محاولة الاتصال...")
        success = db.connect()
        
        if success:
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            
            # اختبار استعلام بسيط
            try:
                result = db.execute_query("SELECT 1 as test")
                if result and len(result) > 0:
                    print("✅ تم تنفيذ استعلام اختبار بنجاح")
                else:
                    print("⚠️ فشل في تنفيذ استعلام الاختبار")
            except Exception as e:
                print(f"⚠️ خطأ في استعلام الاختبار: {e}")
                
            # إغلاق الاتصال
            db.close()
            print("✅ تم إغلاق الاتصال")
            
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            
        return success
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اتصال قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إصلاح مشكلة اتصال قاعدة البيانات")
    print("=" * 60)
    
    tests = [
        ("إعدادات قاعدة البيانات", test_database_config),
        ("اتصال قاعدة البيانات", test_database_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! تم إصلاح مشكلة اتصال قاعدة البيانات.")
    else:
        print("⚠️ بعض الاختبارات فشلت. قد تحتاج إلى مراجعة الإعدادات.")

if __name__ == "__main__":
    main()
