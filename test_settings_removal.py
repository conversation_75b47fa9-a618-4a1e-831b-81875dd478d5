#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة زر الإعدادات وجميع المكونات المرتبطة به
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_settings_removal():
    """اختبار إزالة زر الإعدادات"""
    print("🧪 اختبار إزالة زر الإعدادات وجميع المكونات المرتبطة به")
    print("=" * 60)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول
        print("\n2. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # 3. اختبار استيراد الواجهة الرئيسية
        print("\n3. اختبار استيراد الواجهة الرئيسية...")
        from gui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        
        # 4. فحص قائمة الأزرار في الشريط الجانبي
        print("\n4. فحص قائمة الأزرار في الشريط الجانبي...")
        
        # قراءة ملف main_window.py للتحقق من إزالة زر الإعدادات
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من عدم وجود زر الإعدادات في menu_items
        if '"settings"' in content and 'الإعدادات' in content and 'show_settings' in content:
            print("❌ زر الإعدادات لا يزال موجوداً في الكود")
            return False
        else:
            print("✅ تم إزالة زر الإعدادات من الشريط الجانبي")
        
        # 5. التحقق من إزالة دالة show_settings
        print("\n5. التحقق من إزالة دالة show_settings...")
        if 'def show_settings(' in content:
            print("❌ دالة show_settings لا تزال موجودة")
            return False
        else:
            print("✅ تم إزالة دالة show_settings")
        
        # 6. التحقق من إزالة دوال الإعدادات الأخرى
        print("\n6. التحقق من إزالة دوال الإعدادات الأخرى...")
        
        removed_functions = [
            'def load_settings(',
            'def create_user_info_section(',
            'def create_data_management_section(',
            'def create_system_settings_section(',
            'def create_backup(',
            'def restore_backup(',
            'def export_to_excel('
        ]
        
        all_removed = True
        for func in removed_functions:
            if func in content:
                print(f"❌ الدالة {func} لا تزال موجودة")
                all_removed = False
            else:
                print(f"✅ تم إزالة الدالة {func}")
        
        if not all_removed:
            return False
        
        # 7. اختبار إنشاء نافذة رئيسية
        print("\n7. اختبار إنشاء نافذة رئيسية...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة تجريبية
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # محاولة إنشاء MainWindow
            main_window = MainWindow(test_window)
            print("✅ تم إنشاء MainWindow بنجاح")
            
            # التحقق من عدم وجود زر الإعدادات في menu_buttons
            if hasattr(main_window, 'menu_buttons'):
                if 'settings' in main_window.menu_buttons:
                    print("❌ زر الإعدادات لا يزال موجوداً في menu_buttons")
                    test_window.destroy()
                    return False
                else:
                    print("✅ زر الإعدادات غير موجود في menu_buttons")
            
            # التحقق من الأزرار الموجودة
            if hasattr(main_window, 'menu_buttons'):
                available_buttons = list(main_window.menu_buttons.keys())
                print(f"✅ الأزرار المتاحة: {available_buttons}")
                
                expected_buttons = ['dashboard', 'income', 'expense', 'accounts', 'transfers', 'search', 'reports', 'users']
                missing_buttons = [btn for btn in expected_buttons if btn not in available_buttons]
                extra_buttons = [btn for btn in available_buttons if btn not in expected_buttons]
                
                if missing_buttons:
                    print(f"⚠️ أزرار مفقودة: {missing_buttons}")
                
                if extra_buttons:
                    print(f"⚠️ أزرار إضافية: {extra_buttons}")
                
                if not missing_buttons and not extra_buttons:
                    print("✅ جميع الأزرار المطلوبة موجودة وزر الإعدادات غير موجود")
            
            # إغلاق النافذة التجريبية
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")
            return False
        
        # 8. اختبار الوظائف الأساسية
        print("\n8. اختبار الوظائف الأساسية...")
        
        # اختبار وجود الدوال الأساسية
        essential_functions = [
            'show_dashboard',
            'show_income',
            'show_expense',
            'show_accounts',
            'show_transfers',
            'show_search',
            'show_reports',
            'show_users'
        ]
        
        all_functions_exist = True
        for func in essential_functions:
            if f'def {func}(' in content:
                print(f"✅ الدالة {func} موجودة")
            else:
                print(f"❌ الدالة {func} مفقودة")
                all_functions_exist = False
        
        if not all_functions_exist:
            return False
        
        # 9. اختبار مكونات واجهة المستخدم
        print("\n9. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            print("✅ مكونات واجهة المستخدم متاحة")
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("\n🚀 اختبار بدء تشغيل التطبيق")
    print("=" * 40)
    
    try:
        # اختبار استيراد الملفات الأساسية
        print("1. اختبار استيراد الملفات الأساسية...")
        
        from gui.login_window import LoginWindow
        print("✅ تم استيراد LoginWindow")
        
        from gui.main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        from database.connection import db
        print("✅ تم استيراد database connection")
        
        from utils.auth import auth_manager
        print("✅ تم استيراد auth_manager")
        
        # اختبار إنشاء نافذة تسجيل الدخول
        print("\n2. اختبار إنشاء نافذة تسجيل الدخول...")
        import customtkinter as ctk
        
        test_window = ctk.CTk()
        test_window.withdraw()
        
        login_window = LoginWindow()
        print("✅ تم إنشاء نافذة تسجيل الدخول بنجاح")
        
        # إغلاق النافذة
        if hasattr(login_window, 'window'):
            login_window.window.destroy()
        
        test_window.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بدء التشغيل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار شامل لإزالة زر الإعدادات")
    print("=" * 70)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار إزالة زر الإعدادات
    if test_settings_removal():
        tests_passed += 1
        print("\n✅ اختبار إزالة زر الإعدادات نجح")
    else:
        print("\n❌ اختبار إزالة زر الإعدادات فشل")
    
    # اختبار بدء تشغيل التطبيق
    if test_application_startup():
        tests_passed += 1
        print("\n✅ اختبار بدء تشغيل التطبيق نجح")
    else:
        print("\n❌ اختبار بدء تشغيل التطبيق فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 تم إزالة زر الإعدادات بنجاح!")
        
        print("\n✨ ما تم إزالته:")
        print("   🗑️ زر الإعدادات من الشريط الجانبي")
        print("   🗑️ دالة show_settings")
        print("   🗑️ دالة load_settings")
        print("   🗑️ دالة create_user_info_section")
        print("   🗑️ دالة create_data_management_section")
        print("   🗑️ دالة create_system_settings_section")
        print("   🗑️ دالة create_backup")
        print("   🗑️ دالة restore_backup")
        print("   🗑️ دالة export_to_excel")
        
        print("\n✅ ما تم الحفاظ عليه:")
        print("   🏠 لوحة التحكم")
        print("   💰 الواردات")
        print("   💸 المصروفات")
        print("   🏦 الحسابات")
        print("   🔄 التحويلات")
        print("   🔍 البحث")
        print("   📊 التقارير")
        print("   👥 إدارة المستخدمين")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin / 123456")
        print("3. لن تجد زر الإعدادات في الشريط الجانبي")
        print("4. جميع الوظائف الأخرى تعمل بشكل طبيعي")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
