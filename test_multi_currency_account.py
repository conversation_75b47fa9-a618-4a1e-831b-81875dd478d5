#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة حساب متعدد العملات
Test adding multi-currency account
"""

from database.connection import db
from database.models import Account

def test_multi_currency_account():
    """اختبار إنشاء حساب متعدد العملات"""
    try:
        print("🧪 اختبار إنشاء حساب متعدد العملات...")
        
        # إنشاء حساب جديد
        user_id = 1  # افتراض وجود مستخدم بـ ID = 1
        account_name = "حساب اختبار متعدد العملات"
        description = "حساب للاختبار يدعم عدة عملات"
        
        account_id = Account.create(
            user_id=user_id,
            name=account_name,
            description=description
        )
        
        if account_id > 0:
            print(f"✅ تم إنشاء الحساب بنجاح - ID: {account_id}")
            
            # إضافة أرصدة بعملات مختلفة
            currencies_balances = [
                (1, 1000.0),  # SAR - ريال سعودي
                (2, 500.0),   # YER - ريال يمني  
                (3, 200.0),   # AED - درهم إماراتي
                (4, 100.0)    # USD - دولار أمريكي
            ]
            
            print("💰 إضافة أرصدة العملات...")
            for currency_id, balance in currencies_balances:
                result = Account.add_currency_balance(account_id, currency_id, balance)
                if result > 0:
                    print(f"   ✅ تم إضافة رصيد العملة {currency_id}: {balance}")
                else:
                    print(f"   ❌ فشل في إضافة رصيد العملة {currency_id}")
            
            # عرض أرصدة الحساب
            print("\n📊 أرصدة الحساب:")
            balances = Account.get_all_balances(account_id)
            total_currencies = 0
            
            for balance in balances:
                print(f"   - {balance['name']} ({balance['code']}): {balance['balance']} {balance['symbol']}")
                total_currencies += 1
            
            print(f"\n🎉 تم إنشاء حساب متعدد العملات بنجاح!")
            print(f"   - اسم الحساب: {account_name}")
            print(f"   - عدد العملات: {total_currencies}")
            print(f"   - معرف الحساب: {account_id}")
            
            return True
            
        else:
            print("❌ فشل في إنشاء الحساب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_account_retrieval():
    """اختبار استرجاع الحسابات متعددة العملات"""
    try:
        print("\n🔍 اختبار استرجاع الحسابات...")
        
        user_id = 1
        accounts = Account.get_by_user(user_id)
        
        print(f"📋 عدد الحسابات للمستخدم {user_id}: {len(accounts)}")
        
        for account in accounts:
            print(f"\n🏦 حساب: {account['name']}")
            print(f"   - الحالة: {'نشط' if account['is_active'] else 'غير نشط'}")
            print(f"   - الوصف: {account.get('description', 'لا يوجد')}")
            
            if account.get('balances'):
                print("   - الأرصدة:")
                for balance in account['balances']:
                    print(f"     • {balance['name']}: {balance['balance']} {balance['symbol']}")
            else:
                print("   - لا توجد أرصدة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استرجاع الحسابات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام الحسابات متعددة العملات")
    print("=" * 60)
    
    # اختبار إنشاء حساب
    success1 = test_multi_currency_account()
    
    # اختبار استرجاع الحسابات
    success2 = test_account_retrieval()
    
    if success1 and success2:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام الحسابات متعددة العملات يعمل بشكل صحيح")
    else:
        print("\n❌ بعض الاختبارات فشلت")

if __name__ == "__main__":
    main()
