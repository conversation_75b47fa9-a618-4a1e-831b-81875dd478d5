#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار XAMPP وإصلاح مشكلة حفظ التعديلات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_xampp_connection():
    """اختبار الاتصال بـ XAMPP"""
    print("🔌 اختبار الاتصال بـ XAMPP...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # محاولة الاتصال
        print("📡 محاولة الاتصال بقاعدة البيانات...")
        if db.connect():
            print("✅ تم الاتصال بـ XAMPP بنجاح!")
            
            # اختبار استعلام بسيط
            try:
                result = db.execute_query("SELECT VERSION() as version")
                if result:
                    print(f"   إصدار MySQL: {result[0]['version']}")
                
                # اختبار قاعدة البيانات
                result = db.execute_query("SELECT DATABASE() as db_name")
                if result:
                    print(f"   قاعدة البيانات الحالية: {result[0]['db_name']}")
                
                return True
                
            except Exception as e:
                print(f"⚠️ خطأ في تنفيذ الاستعلام: {e}")
                return False
        else:
            print("❌ فشل في الاتصال بـ XAMPP!")
            print("\nتأكد من:")
            print("1. تشغيل XAMPP Control Panel")
            print("2. تشغيل خدمة Apache")
            print("3. تشغيل خدمة MySQL")
            print("4. أن المنفذ 3306 غير محجوب")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def check_database_schema():
    """فحص مخطط قاعدة البيانات"""
    print("\n📋 فحص مخطط قاعدة البيانات...")
    print("=" * 50)
    
    try:
        from database.connection import db
        
        # فحص جدول transactions
        print("🔍 فحص جدول transactions...")
        columns = db.execute_query("DESCRIBE transactions")
        
        if not columns:
            print("❌ جدول transactions غير موجود!")
            return False
        
        column_names = [col['Field'] for col in columns]
        required_columns = ['id', 'amount', 'account_id', 'currency_id', 'category_name', 'description', 'transaction_date', 'transaction_type']
        
        print("   الأعمدة الموجودة:")
        for col in columns:
            status = "✅" if col['Field'] in required_columns else "📋"
            print(f"   {status} {col['Field']}: {col['Type']}")
        
        # التحقق من الأعمدة المطلوبة
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"\n⚠️ أعمدة مفقودة: {', '.join(missing_columns)}")
            
            # إضافة العمود المفقود إذا كان category_name
            if 'category_name' in missing_columns:
                print("🔧 إضافة عمود category_name...")
                try:
                    db.execute_update("""
                        ALTER TABLE transactions 
                        ADD COLUMN category_name VARCHAR(100) NULL 
                        COMMENT 'اسم التصنيف المدخل يدوياً'
                    """)
                    print("✅ تم إضافة عمود category_name")
                except Exception as e:
                    print(f"❌ فشل في إضافة العمود: {e}")
                    return False
        else:
            print("\n✅ جميع الأعمدة المطلوبة موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المخطط: {e}")
        return False

def test_transaction_update():
    """اختبار دالة تحديث المعاملة"""
    print("\n🧪 اختبار دالة تحديث المعاملة...")
    print("=" * 50)
    
    try:
        from database.models import Transaction
        from database.connection import db
        
        # التحقق من وجود دالة update
        if not hasattr(Transaction, 'update'):
            print("❌ دالة Transaction.update غير موجودة!")
            return False
        
        print("✅ دالة Transaction.update موجودة")
        
        # البحث عن معاملة للاختبار
        test_transactions = db.execute_query("""
            SELECT id, amount, account_id, currency_id, description, transaction_date 
            FROM transactions 
            LIMIT 1
        """)
        
        if not test_transactions:
            print("⚠️ لا توجد معاملات للاختبار")
            return True
        
        test_transaction = test_transactions[0]
        print(f"📝 اختبار تحديث المعاملة {test_transaction['id']}")
        
        # محاولة تحديث بسيط (تغيير الوصف فقط)
        original_description = test_transaction['description']
        test_description = f"اختبار تحديث - {original_description}"
        
        try:
            success = Transaction.update(
                transaction_id=test_transaction['id'],
                description=test_description
            )
            
            if success:
                print("✅ تم تحديث المعاملة بنجاح")
                
                # إعادة الوصف الأصلي
                Transaction.update(
                    transaction_id=test_transaction['id'],
                    description=original_description
                )
                print("✅ تم إعادة الوصف الأصلي")
                
                return True
            else:
                print("❌ فشل في تحديث المعاملة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحديث المعاملة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        return False

def fix_edit_dialog_issues():
    """إصلاح مشاكل نافذة التعديل"""
    print("\n🔧 إصلاح مشاكل نافذة التعديل...")
    print("=" * 50)
    
    # 1. التحقق من XAMPP
    if not test_xampp_connection():
        print("❌ يجب إصلاح مشكلة الاتصال بـ XAMPP أولاً")
        return False
    
    # 2. فحص وإصلاح مخطط قاعدة البيانات
    if not check_database_schema():
        print("❌ يجب إصلاح مخطط قاعدة البيانات أولاً")
        return False
    
    # 3. اختبار دالة التحديث
    if not test_transaction_update():
        print("❌ يجب إصلاح دالة التحديث أولاً")
        return False
    
    print("\n🎉 تم إصلاح جميع المشاكل!")
    return True

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار XAMPP وإصلاح مشكلة حفظ التعديلات")
    print("=" * 60)
    
    if fix_edit_dialog_issues():
        print("\n✅ جميع الاختبارات نجحت!")
        print("\nيمكنك الآن:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. الذهاب لقسم الواردات أو المصروفات")
        print("3. اضغط على زر 'تعديل' بجانب أي معاملة")
        print("4. عدّل أي حقل واضغط 'حفظ التغييرات'")
        print("5. يجب أن يتم حفظ التعديلات بنجاح")
        
        print("\n💡 ملاحظات:")
        print("- تأكد من أن XAMPP يعمل قبل تشغيل التطبيق")
        print("- ستظهر رسائل تطوير في وحدة التحكم لتتبع عملية الحفظ")
        print("- إذا ظهرت أخطاء، راجع وحدة التحكم للتفاصيل")
        
    else:
        print("\n❌ فشل في إصلاح بعض المشاكل!")
        print("\nيرجى:")
        print("1. التأكد من تشغيل XAMPP")
        print("2. التأكد من تشغيل خدمة MySQL")
        print("3. التأكد من وجود قاعدة البيانات money_manager")
        print("4. إعادة تشغيل هذا الملف")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
