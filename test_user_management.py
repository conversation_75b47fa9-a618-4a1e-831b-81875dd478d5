#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from database.models import User
from utils.auth import auth_manager
import bcrypt

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    try:
        if db.is_connected():
            print("✅ الاتصال بقاعدة البيانات ناجح")
            return True
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_admin_login():
    """اختبار تسجيل دخول المدير الجديد"""
    print("\n🔐 اختبار تسجيل دخول المدير الجديد...")
    try:
        # محاولة تسجيل الدخول بالمستخدم الجديد
        success, message = auth_manager.login("admin2", "123456")
        
        if success:
            print("✅ تم تسجيل دخول admin2 بنجاح")
            print(f"   المستخدم الحالي: {auth_manager.current_user['username']}")
            print(f"   الدور: {auth_manager.current_user['role']}")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return False

def test_user_crud_operations():
    """اختبار عمليات CRUD للمستخدمين"""
    print("\n📝 اختبار عمليات إدارة المستخدمين...")
    
    try:
        # 1. اختبار إنشاء مستخدم جديد
        print("   1. اختبار إنشاء مستخدم جديد...")
        success, result = auth_manager.register_user(
            username="test_user",
            password="test123",
            full_name="مستخدم تجريبي",
            role="user"
        )
        
        if success:
            print("   ✅ تم إنشاء المستخدم التجريبي بنجاح")
        else:
            print(f"   ❌ فشل إنشاء المستخدم: {result}")
            return False
        
        # 2. اختبار جلب جميع المستخدمين
        print("   2. اختبار جلب جميع المستخدمين...")
        users = User.get_all()
        if users and len(users) >= 2:
            print(f"   ✅ تم جلب {len(users)} مستخدم")
            for user in users:
                print(f"      - {user['username']} ({user['role']}) - {'نشط' if user['is_active'] else 'معطل'}")
        else:
            print("   ❌ فشل في جلب المستخدمين")
            return False
        
        # 3. اختبار تحديث بيانات المستخدم
        print("   3. اختبار تحديث بيانات المستخدم...")
        test_user = User.get_by_username("test_user")
        if test_user:
            success, message = User.update_user(
                test_user['id'],
                full_name="مستخدم تجريبي محدث"
            )
            if success:
                print("   ✅ تم تحديث بيانات المستخدم بنجاح")
            else:
                print(f"   ❌ فشل تحديث المستخدم: {message}")
                return False
        
        # 4. اختبار تعطيل المستخدم
        print("   4. اختبار تعطيل المستخدم...")
        success, message = User.delete_user(test_user['id'])
        if success:
            print("   ✅ تم تعطيل المستخدم بنجاح")
        else:
            print(f"   ❌ فشل تعطيل المستخدم: {message}")
            return False
        
        # 5. اختبار إحصائيات المستخدمين
        print("   5. اختبار إحصائيات المستخدمين...")
        stats = User.get_user_statistics()
        if stats:
            print("   ✅ تم جلب الإحصائيات بنجاح:")
            print(f"      - إجمالي المستخدمين: {stats['total_users']}")
            print(f"      - المستخدمين النشطين: {stats['active_users']}")
            print(f"      - المديرين: {stats['admin_users']}")
            print(f"      - المستخدمين العاديين: {stats['regular_users']}")
        else:
            print("   ❌ فشل في جلب الإحصائيات")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار عمليات CRUD: {e}")
        return False

def test_authentication_features():
    """اختبار ميزات المصادقة"""
    print("\n🔒 اختبار ميزات المصادقة...")
    
    try:
        # 1. اختبار التحقق من الصلاحيات
        print("   1. اختبار التحقق من الصلاحيات...")
        if auth_manager.can_manage_users():
            print("   ✅ المستخدم الحالي يمكنه إدارة المستخدمين")
        else:
            print("   ❌ المستخدم الحالي لا يمكنه إدارة المستخدمين")
            return False
        
        # 2. اختبار تشفير كلمة المرور
        print("   2. اختبار تشفير كلمة المرور...")
        password = "test_password"
        hashed = auth_manager.hash_password(password)
        if hashed and auth_manager.verify_password(password, hashed):
            print("   ✅ تشفير والتحقق من كلمة المرور يعمل بشكل صحيح")
        else:
            print("   ❌ فشل في تشفير أو التحقق من كلمة المرور")
            return False
        
        # 3. اختبار إعادة تعيين كلمة المرور
        print("   3. اختبار إعادة تعيين كلمة المرور...")
        test_user = User.get_by_username("test_user")
        if test_user:
            success, message = auth_manager.reset_user_password(test_user['id'], "new_password123")
            if success:
                print("   ✅ تم إعادة تعيين كلمة المرور بنجاح")
            else:
                print(f"   ❌ فشل إعادة تعيين كلمة المرور: {message}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار ميزات المصادقة: {e}")
        return False

def test_security_measures():
    """اختبار الإجراءات الأمنية"""
    print("\n🛡️ اختبار الإجراءات الأمنية...")
    
    try:
        # 1. اختبار منع إنشاء مستخدم بنفس الاسم
        print("   1. اختبار منع إنشاء مستخدم بنفس الاسم...")
        success, message = auth_manager.register_user(
            username="admin2",  # نفس اسم المدير الموجود
            password="test123",
            full_name="مستخدم مكرر",
            role="user"
        )
        
        if not success and "موجود مسبقاً" in message:
            print("   ✅ تم منع إنشاء مستخدم بنفس الاسم")
        else:
            print("   ❌ لم يتم منع إنشاء مستخدم بنفس الاسم")
            return False
        
        # 2. اختبار التحقق من قوة كلمة المرور
        print("   2. اختبار التحقق من قوة كلمة المرور...")
        success, message = auth_manager.register_user(
            username="weak_pass_user",
            password="123",  # كلمة مرور ضعيفة
            full_name="مستخدم كلمة مرور ضعيفة",
            role="user"
        )
        
        if not success and "6 أحرف على الأقل" in message:
            print("   ✅ تم رفض كلمة المرور الضعيفة")
        else:
            print("   ❌ لم يتم رفض كلمة المرور الضعيفة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الإجراءات الأمنية: {e}")
        return False

def cleanup_test_data():
    """تنظيف البيانات التجريبية"""
    print("\n🧹 تنظيف البيانات التجريبية...")
    try:
        # حذف المستخدم التجريبي إذا كان موجوداً
        test_user = User.get_by_username("test_user")
        if test_user:
            # حذف فعلي من قاعدة البيانات للتنظيف
            query = "DELETE FROM users WHERE username = 'test_user'"
            db.execute_update(query, ())
            print("   ✅ تم حذف المستخدم التجريبي")
        
        print("   ✅ تم تنظيف البيانات التجريبية")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في تنظيف البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة إدارة المستخدمين")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # اختبار الاتصال بقاعدة البيانات
    if test_database_connection():
        tests_passed += 1
    
    # اختبار تسجيل دخول المدير
    if test_admin_login():
        tests_passed += 1
    
    # اختبار عمليات CRUD
    if test_user_crud_operations():
        tests_passed += 1
    
    # اختبار ميزات المصادقة
    if test_authentication_features():
        tests_passed += 1
    
    # اختبار الإجراءات الأمنية
    if test_security_measures():
        tests_passed += 1
    
    # تنظيف البيانات التجريبية
    cleanup_test_data()
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! ميزة إدارة المستخدمين تعمل بشكل صحيح.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
