#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from config.settings import DATABASE_CONFIG

try:
    print("🔍 اختبار الاتصال بـ MySQL...")
    print(f"Host: {DATABASE_CONFIG['host']}")
    print(f"Database: {DATABASE_CONFIG['database']}")
    print(f"User: {DATABASE_CONFIG['user']}")
    
    connection = mysql.connector.connect(**DATABASE_CONFIG)
    cursor = connection.cursor()
    
    print("✅ تم الاتصال بنجاح!")
    
    # فحص الجداول الموجودة
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    
    print(f"\n📊 الجداول الموجودة ({len(tables)}):")
    for table in tables:
        print(f"   - {table[0]}")
    
    # فحص جدول currencies
    if ('currencies',) in tables:
        cursor.execute("DESCRIBE currencies")
        columns = cursor.fetchall()
        print(f"\n💰 أعمدة جدول currencies:")
        for col in columns:
            print(f"   - {col[0]}: {col[1]}")
    
    # فحص جدول accounts
    if ('accounts',) in tables:
        cursor.execute("DESCRIBE accounts")
        columns = cursor.fetchall()
        print(f"\n🏦 أعمدة جدول accounts:")
        for col in columns:
            print(f"   - {col[0]}: {col[1]}")
    
    cursor.close()
    connection.close()
    
except Exception as e:
    print(f"❌ خطأ في الاتصال: {e}")
