#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للعملات المتعددة
"""

import mysql.connector
from datetime import datetime

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4'
        )
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return None, None

def test_dashboard_query():
    """اختبار استعلام لوحة التحكم"""
    print("🔍 اختبار استعلام لوحة التحكم...")
    
    connection, cursor = connect_to_database()
    if not connection:
        return
    
    try:
        user_id = 1
        
        # الاستعلام المحسن للأرصدة
        balance_query = """
            SELECT
                c.code,
                c.name,
                c.symbol,
                COALESCE(SUM(a.current_balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN accounts a ON c.id = a.currency_id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.name, c.symbol
            ORDER BY total_balance DESC
        """
        
        cursor.execute(balance_query, (user_id,))
        results = cursor.fetchall()
        
        print("📊 نتائج الأرصدة:")
        for result in results:
            if result['total_balance'] > 0:
                print(f"   ✅ {result['code']}: {result['total_balance']:,.2f} {result['symbol']}")
        
        # فحص العملات الأربع
        expected = ['SAR', 'AED', 'USD', 'YER']
        found = [r['code'] for r in results if r['total_balance'] > 0]
        
        print(f"\n🔍 التحقق من العملات:")
        for currency in expected:
            if currency in found:
                print(f"   ✅ {currency}: موجود")
            else:
                print(f"   ❌ {currency}: مفقود")
        
        print(f"\n📈 إجمالي العملات مع أرصدة: {len(found)}/4")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    finally:
        cursor.close()
        connection.close()

def test_aed_accounts():
    """اختبار حسابات الدرهم الإماراتي"""
    print("\n🔍 اختبار حسابات الدرهم الإماراتي...")
    
    connection, cursor = connect_to_database()
    if not connection:
        return
    
    try:
        user_id = 1
        
        # فحص الحسابات بالدرهم الإماراتي
        cursor.execute("""
            SELECT id, name, current_balance
            FROM accounts 
            WHERE currency_id = 3 AND user_id = %s AND is_active = TRUE
        """, (user_id,))
        
        accounts = cursor.fetchall()
        print(f"📊 حسابات AED: {len(accounts)}")
        
        total = 0
        for account in accounts:
            balance = float(account['current_balance'] or 0)
            total += balance
            print(f"   - {account['name']}: {balance:,.2f} د.إ")
        
        print(f"💰 إجمالي AED: {total:,.2f} د.إ")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    finally:
        cursor.close()
        connection.close()

def main():
    """الدالة الرئيسية"""
    print("="*50)
    print("🧪 اختبار مبسط للعملات المتعددة")
    print("="*50)
    
    test_dashboard_query()
    test_aed_accounts()
    
    print("\n" + "="*50)
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
