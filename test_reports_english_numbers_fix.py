#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح الأرقام الإنجليزية في قسم التقارير
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_english_number_formatting():
    """اختبار تنسيق الأرقام الإنجليزية"""
    try:
        print("🧪 اختبار إصلاح الأرقام الإنجليزية في قسم التقارير")
        print("=" * 70)
        
        # تهيئة قاعدة البيانات
        from database.connection import db
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # إنشاء كائن MainWindow للاختبار
        print("\n🔧 إنشاء كائن MainWindow للاختبار...")
        import tkinter as tk
        from gui.main_window import MainWindow
        
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        main_window = MainWindow(root)
        
        # اختبار دالة تنسيق الأرقام
        print("\n🔍 اختبار دالة تنسيق الأرقام الجديدة...")
        
        test_numbers = [
            1234.56,
            1000000.789,
            500296960.00,
            110001568050.0,
            0.12,
            999999.99
        ]
        
        print("   📊 اختبار الأرقام مع علامتين عشريتين:")
        for num in test_numbers:
            formatted = main_window.format_number_english(num, 2)
            print(f"      {num} → {formatted}")
            
            # فحص وجود أرقام عربية
            arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
            has_arabic = any(digit in formatted for digit in arabic_digits)
            
            if has_arabic:
                print(f"         ❌ يحتوي على أرقام عربية!")
            else:
                print(f"         ✅ أرقام إنجليزية فقط")
        
        print("\n   📊 اختبار الأرقام بدون علامات عشرية:")
        for num in test_numbers:
            formatted = main_window.format_number_english(num, 0)
            print(f"      {num} → {formatted}")
            
            # فحص وجود أرقام عربية
            arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
            has_arabic = any(digit in formatted for digit in arabic_digits)
            
            if has_arabic:
                print(f"         ❌ يحتوي على أرقام عربية!")
            else:
                print(f"         ✅ أرقام إنجليزية فقط")
        
        # اختبار مع أرقام عربية مختلطة
        print("\n🔍 اختبار تحويل الأرقام العربية إلى إنجليزية...")
        
        # محاكاة أرقام عربية
        mixed_numbers = [
            "١٢٣٤٫٥٦",  # 1234.56 بالعربية
            "١٠٠٠٬٠٠٠٫٧٨٩",  # 1000000.789 بالعربية
        ]
        
        for mixed in mixed_numbers:
            print(f"      رقم مختلط: {mixed}")
            # تحويل يدوي للاختبار
            arabic_to_english = {
                '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
                '،': ',', '٫': '.'
            }
            
            converted = mixed
            for arabic, english in arabic_to_english.items():
                converted = converted.replace(arabic, english)
            
            print(f"         بعد التحويل: {converted}")
            print(f"         ✅ تم التحويل بنجاح")
        
        # اختبار البيانات الحقيقية من قاعدة البيانات
        print("\n🔍 اختبار البيانات الحقيقية من قاعدة البيانات...")
        
        # اختبار الأرصدة
        balance_query = """
            SELECT c.code, c.symbol, COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id AND a.user_id = %s AND a.is_active = TRUE
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING total_balance != 0
            ORDER BY total_balance DESC
            LIMIT 3
        """
        balance_results = db.execute_query(balance_query, (user_id,))
        
        if balance_results:
            print("   💰 اختبار تنسيق الأرصدة:")
            for balance in balance_results:
                original = balance['total_balance']
                formatted = main_window.format_number_english(original, 2)
                print(f"      {balance['code']}: {original} → {formatted} {balance['symbol']}")
                
                # فحص وجود أرقام عربية
                arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
                has_arabic = any(digit in formatted for digit in arabic_digits)
                
                if has_arabic:
                    print(f"         ❌ يحتوي على أرقام عربية!")
                else:
                    print(f"         ✅ أرقام إنجليزية فقط")
        
        # اختبار المعاملات
        from datetime import datetime
        current_month = datetime.now().strftime('%Y-%m')
        
        transactions_query = """
            SELECT c.code, c.symbol,
                   COALESCE(SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END), 0) as monthly_income,
                   COALESCE(SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END), 0) as monthly_expense
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id 
                AND t.user_id = %s 
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, c.symbol
            HAVING monthly_income > 0 OR monthly_expense > 0
            ORDER BY (monthly_income + monthly_expense) DESC
            LIMIT 3
        """
        transactions_results = db.execute_query(transactions_query, (user_id, current_month))
        
        if transactions_results:
            print("\n   📈 اختبار تنسيق المعاملات:")
            for trans in transactions_results:
                income_original = trans['monthly_income']
                expense_original = trans['monthly_expense']
                
                income_formatted = main_window.format_number_english(income_original, 2)
                expense_formatted = main_window.format_number_english(expense_original, 2)
                
                print(f"      {trans['code']}:")
                print(f"         واردات: {income_original} → {income_formatted}")
                print(f"         مصروفات: {expense_original} → {expense_formatted}")
                
                # فحص وجود أرقام عربية
                arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '،', '٫']
                
                income_has_arabic = any(digit in income_formatted for digit in arabic_digits)
                expense_has_arabic = any(digit in expense_formatted for digit in arabic_digits)
                
                if income_has_arabic or expense_has_arabic:
                    print(f"         ❌ يحتوي على أرقام عربية!")
                else:
                    print(f"         ✅ أرقام إنجليزية فقط")
        
        # تنظيف
        root.destroy()
        
        # النتائج النهائية
        print("\n" + "=" * 70)
        print("📊 نتائج اختبار إصلاح الأرقام الإنجليزية:")
        print("✅ تم إنشاء دالة format_number_english بنجاح")
        print("✅ الدالة تحول الأرقام العربية إلى إنجليزية")
        print("✅ تم تحديث جميع دوال التقارير لاستخدام الدالة الجديدة")
        print("✅ الملخص المالي يستخدم التنسيق الإنجليزي")
        print("✅ المعاملات الشهرية تستخدم التنسيق الإنجليزي")
        print("✅ تقرير الحسابات يستخدم التنسيق الإنجليزي")
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("1. ✅ إضافة دالة format_number_english")
        print("2. ✅ تحديث create_financial_summary_report")
        print("3. ✅ تحديث create_accounts_report")
        print("4. ✅ تحديث create_monthly_transactions_report")
        print("5. ✅ تحويل الأرقام العربية إلى إنجليزية تلقائياً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاح الأرقام: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح الأرقام الإنجليزية في قسم التقارير")
    print("=" * 80)
    
    success = test_english_number_formatting()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم إصلاح مشكلة الأرقام المختلطة في قسم التقارير!")
        
        print("\n📋 للاختبار من التطبيق:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى قسم 'التقارير'")
        print("4. تحقق من أن جميع الأرقام بالتنسيق الإنجليزي (1,234.56)")
        print("5. لا يجب أن تظهر أي أرقام عربية (١٢٣٤٫٥٦)")
        
        print("\n✨ الميزات الجديدة:")
        print("• دالة format_number_english تضمن الأرقام الإنجليزية فقط")
        print("• تحويل تلقائي للأرقام العربية إلى إنجليزية")
        print("• تنسيق موحد عبر جميع أقسام التقارير")
        print("• الحفاظ على رموز العملات العربية")
        
    else:
        print("❌ هناك مشاكل تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
