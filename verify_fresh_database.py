#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من قاعدة البيانات الجديدة
تطبيق إدارة الأموال الشخصية
"""

import sys
import os
import mysql.connector
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class DatabaseVerifier:
    """فئة التحقق من قاعدة البيانات"""
    
    def __init__(self):
        self.config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'mohdam',
            'database': 'money_manager',
            'charset': 'utf8mb4'
        }
        
        self.verification_results = {
            'connection': False,
            'tables': {},
            'data_counts': {},
            'sample_data': {},
            'errors': []
        }

    def run_verification(self):
        """تشغيل التحقق الشامل"""
        print("="*60)
        print("🔍 التحقق من قاعدة البيانات الجديدة")
        print("="*60)
        print(f"⏰ وقت التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # اختبار الاتصال
        if not self.test_connection():
            return False
        
        # فحص الجداول
        if not self.verify_tables():
            return False
        
        # فحص البيانات
        if not self.verify_data():
            return False
        
        # اختبار الوظائف
        if not self.test_functionality():
            return False
        
        # عرض التقرير النهائي
        self.show_final_report()
        
        return True

    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        print("🔌 اختبار الاتصال...")
        
        try:
            connection = mysql.connector.connect(**self.config)
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                
                print(f"   ✅ الاتصال ناجح")
                print(f"   📊 إصدار MySQL: {version}")
                
                cursor.close()
                connection.close()
                
                self.verification_results['connection'] = True
                return True
            else:
                print("   ❌ فشل في الاتصال")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال: {e}")
            self.verification_results['errors'].append(f"خطأ الاتصال: {e}")
            return False

    def verify_tables(self):
        """فحص الجداول المطلوبة"""
        print("\n🏗️ فحص الجداول...")
        
        required_tables = [
            'users', 'currencies', 'account_types', 'accounts', 
            'account_balances', 'transactions', 'transfers',
            'income_categories', 'expense_categories'
        ]
        
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            # الحصول على قائمة الجداول الموجودة
            cursor.execute("SHOW TABLES")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            print(f"   📊 الجداول الموجودة: {len(existing_tables)}")
            
            # فحص كل جدول مطلوب
            all_tables_exist = True
            for table in required_tables:
                if table in existing_tables:
                    # فحص عدد الأعمدة
                    cursor.execute(f"DESCRIBE {table}")
                    columns = cursor.fetchall()
                    
                    print(f"      ✅ {table}: موجود ({len(columns)} عمود)")
                    self.verification_results['tables'][table] = {
                        'exists': True,
                        'columns': len(columns)
                    }
                else:
                    print(f"      ❌ {table}: مفقود")
                    self.verification_results['tables'][table] = {'exists': False}
                    all_tables_exist = False
            
            cursor.close()
            connection.close()
            
            return all_tables_exist
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص الجداول: {e}")
            self.verification_results['errors'].append(f"خطأ فحص الجداول: {e}")
            return False

    def verify_data(self):
        """فحص البيانات التجريبية"""
        print("\n📊 فحص البيانات التجريبية...")
        
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor(dictionary=True)
            
            # فحص المستخدمين
            cursor.execute("SELECT COUNT(*) as count FROM users")
            users_count = cursor.fetchone()['count']
            print(f"   👤 المستخدمين: {users_count}")
            self.verification_results['data_counts']['users'] = users_count
            
            # فحص المستخدم admin
            cursor.execute("SELECT username FROM users WHERE username = 'admin'")
            admin_exists = cursor.fetchone() is not None
            print(f"   👤 المستخدم admin: {'✅ موجود' if admin_exists else '❌ مفقود'}")
            
            # فحص العملات
            cursor.execute("SELECT COUNT(*) as count FROM currencies WHERE is_active = 1")
            currencies_count = cursor.fetchone()['count']
            print(f"   💰 العملات النشطة: {currencies_count}")
            self.verification_results['data_counts']['currencies'] = currencies_count
            
            # فحص العملات المطلوبة
            cursor.execute("SELECT code FROM currencies WHERE is_active = 1")
            currency_codes = [row['code'] for row in cursor.fetchall()]
            required_currencies = ['SAR', 'AED', 'YER', 'USD']
            missing_currencies = [curr for curr in required_currencies if curr not in currency_codes]
            
            if not missing_currencies:
                print(f"   💰 العملات المطلوبة: ✅ جميعها موجودة ({', '.join(currency_codes)})")
            else:
                print(f"   💰 العملات المفقودة: ❌ {', '.join(missing_currencies)}")
            
            # فحص الحسابات
            cursor.execute("SELECT COUNT(*) as count FROM accounts WHERE is_active = 1")
            accounts_count = cursor.fetchone()['count']
            print(f"   🏦 الحسابات النشطة: {accounts_count}")
            self.verification_results['data_counts']['accounts'] = accounts_count
            
            # فحص أرصدة الحسابات
            cursor.execute("SELECT COUNT(*) as count FROM account_balances")
            balances_count = cursor.fetchone()['count']
            print(f"   💰 أرصدة الحسابات: {balances_count}")
            self.verification_results['data_counts']['balances'] = balances_count
            
            # فحص المعاملات
            cursor.execute("SELECT COUNT(*) as count FROM transactions")
            transactions_count = cursor.fetchone()['count']
            print(f"   💳 المعاملات: {transactions_count}")
            self.verification_results['data_counts']['transactions'] = transactions_count
            
            # فحص التحويلات
            cursor.execute("SELECT COUNT(*) as count FROM transfers")
            transfers_count = cursor.fetchone()['count']
            print(f"   🔄 التحويلات: {transfers_count}")
            self.verification_results['data_counts']['transfers'] = transfers_count
            
            # فحص الأرصدة الإجمالية
            cursor.execute("""
                SELECT c.code, c.symbol, SUM(ab.balance) as total_balance
                FROM account_balances ab
                JOIN currencies c ON ab.currency_id = c.id
                GROUP BY c.id, c.code, c.symbol
                ORDER BY total_balance DESC
            """)
            
            balances = cursor.fetchall()
            print(f"\n   💰 الأرصدة الإجمالية:")
            for balance in balances:
                print(f"      - {balance['code']}: {balance['total_balance']:,.2f} {balance['symbol']}")
            
            self.verification_results['sample_data']['balances'] = balances
            
            cursor.close()
            connection.close()
            
            # التحقق من الحد الأدنى للبيانات
            min_requirements = {
                'users': 1,
                'currencies': 4,
                'accounts': 5,
                'transactions': 15,
                'transfers': 3
            }
            
            all_requirements_met = True
            for data_type, min_count in min_requirements.items():
                actual_count = self.verification_results['data_counts'].get(data_type, 0)
                if actual_count < min_count:
                    print(f"   ⚠️ {data_type}: {actual_count} < {min_count} (أقل من المطلوب)")
                    all_requirements_met = False
            
            return all_requirements_met and admin_exists and not missing_currencies
            
        except Exception as e:
            print(f"   ❌ خطأ في فحص البيانات: {e}")
            self.verification_results['errors'].append(f"خطأ فحص البيانات: {e}")
            return False

    def test_functionality(self):
        """اختبار الوظائف الأساسية"""
        print("\n🧪 اختبار الوظائف الأساسية...")
        
        try:
            connection = mysql.connector.connect(**self.config)
            cursor = connection.cursor()
            
            # اختبار إضافة حساب جديد
            print("   🏦 اختبار إضافة حساب جديد...")
            cursor.execute("""
                INSERT INTO accounts (user_id, name, account_type_id, description, is_active, created_at, updated_at)
                VALUES (1, 'حساب اختبار التحقق', 1, 'حساب تجريبي للتحقق', 1, NOW(), NOW())
            """)
            test_account_id = cursor.lastrowid
            print(f"      ✅ تم إنشاء حساب اختبار (ID: {test_account_id})")
            
            # اختبار إضافة رصيد
            print("   💰 اختبار إضافة رصيد...")
            cursor.execute("""
                INSERT INTO account_balances (account_id, currency_id, balance, created_at, updated_at)
                VALUES (%s, 1, 1000.0, NOW(), NOW())
            """, (test_account_id,))
            print("      ✅ تم إضافة رصيد تجريبي")
            
            # اختبار إضافة معاملة
            print("   💳 اختبار إضافة معاملة...")
            cursor.execute("""
                INSERT INTO transactions (user_id, account_id, currency_id, type, amount, description, transaction_date, created_at, updated_at)
                VALUES (1, %s, 1, 'income', 500.0, 'معاملة اختبار التحقق', NOW(), NOW(), NOW())
            """, (test_account_id,))
            test_transaction_id = cursor.lastrowid
            print(f"      ✅ تم إنشاء معاملة اختبار (ID: {test_transaction_id})")
            
            # تنظيف البيانات التجريبية
            print("   🧹 تنظيف بيانات الاختبار...")
            cursor.execute("DELETE FROM transactions WHERE id = %s", (test_transaction_id,))
            cursor.execute("DELETE FROM account_balances WHERE account_id = %s", (test_account_id,))
            cursor.execute("DELETE FROM accounts WHERE id = %s", (test_account_id,))
            print("      ✅ تم تنظيف بيانات الاختبار")
            
            connection.commit()
            cursor.close()
            connection.close()
            
            print("   ✅ جميع الوظائف تعمل بشكل صحيح")
            return True
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار الوظائف: {e}")
            self.verification_results['errors'].append(f"خطأ اختبار الوظائف: {e}")
            return False

    def show_final_report(self):
        """عرض التقرير النهائي"""
        print("\n" + "="*60)
        print("📋 تقرير التحقق النهائي")
        print("="*60)
        
        # حالة الاتصال
        connection_status = "✅ متصل" if self.verification_results['connection'] else "❌ غير متصل"
        print(f"🔌 حالة الاتصال: {connection_status}")
        
        # حالة الجداول
        tables_count = len([t for t in self.verification_results['tables'].values() if t.get('exists', False)])
        total_tables = len(self.verification_results['tables'])
        print(f"🏗️ الجداول: {tables_count}/{total_tables} موجود")
        
        # إحصائيات البيانات
        print(f"\n📊 إحصائيات البيانات:")
        for data_type, count in self.verification_results['data_counts'].items():
            print(f"   - {data_type}: {count}")
        
        # الأرصدة
        if 'balances' in self.verification_results['sample_data']:
            print(f"\n💰 ملخص الأرصدة:")
            for balance in self.verification_results['sample_data']['balances']:
                print(f"   - {balance['code']}: {balance['total_balance']:,.2f} {balance['symbol']}")
        
        # الأخطاء
        if self.verification_results['errors']:
            print(f"\n❌ الأخطاء ({len(self.verification_results['errors'])}):")
            for error in self.verification_results['errors']:
                print(f"   - {error}")
        else:
            print(f"\n✅ لا توجد أخطاء")
        
        # التوصيات
        print(f"\n💡 التوصيات:")
        if self.verification_results['connection'] and tables_count == total_tables:
            print("   ✅ قاعدة البيانات جاهزة للاستخدام")
            print("   🚀 يمكنك تشغيل التطبيق: python main.py")
            print("   🔐 سجل الدخول: admin / 123456")
        else:
            print("   ⚠️ هناك مشاكل تحتاج إلى إصلاح")
            print("   🔧 أعد تشغيل سكريبت إنشاء قاعدة البيانات")

def main():
    """الدالة الرئيسية"""
    verifier = DatabaseVerifier()
    success = verifier.run_verification()
    
    print(f"\n🏁 انتهى التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("🎉 التحقق مكتمل بنجاح!")
    else:
        print("⚠️ هناك مشاكل تحتاج إلى مراجعة")

if __name__ == "__main__":
    main()
