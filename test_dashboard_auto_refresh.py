#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحديث التلقائي للوحة التحكم
"""

import sys
import os
import mysql.connector
from mysql.connector import Error
from datetime import datetime

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def connect_to_database():
    """الاتصال بقاعدة البيانات"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'mohdam',
        'database': 'money_manager',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor(dictionary=True)
        return connection, cursor
    except Error as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return None, None

def get_current_dashboard_stats(cursor):
    """الحصول على إحصائيات لوحة التحكم الحالية"""
    try:
        user_id = 1
        current_month = datetime.now().strftime('%Y-%m')
        
        # الأرصدة بجميع العملات
        balance_query = """
            SELECT 
                c.code,
                c.symbol,
                COALESCE(SUM(ab.balance), 0) as total_balance
            FROM currencies c
            LEFT JOIN account_balances ab ON c.id = ab.currency_id
            LEFT JOIN accounts a ON ab.account_id = a.id
            WHERE c.is_active = TRUE
            AND (a.user_id = %s AND a.is_active = TRUE OR a.id IS NULL)
            GROUP BY c.id, c.code, c.symbol
            HAVING total_balance > 0
            ORDER BY total_balance DESC
        """
        cursor.execute(balance_query, (user_id,))
        balance_results = cursor.fetchall()
        
        # المعاملات الشهرية
        transactions_query = """
            SELECT 
                c.code,
                t.type,
                COALESCE(SUM(t.amount), 0) as total_amount
            FROM currencies c
            LEFT JOIN transactions t ON c.id = t.currency_id 
                AND t.user_id = %s 
                AND DATE_FORMAT(t.transaction_date, '%Y-%m') = %s
            WHERE c.is_active = TRUE
            GROUP BY c.id, c.code, t.type
            HAVING total_amount > 0
            ORDER BY c.code, t.type
        """
        cursor.execute(transactions_query, (user_id, current_month))
        transactions_results = cursor.fetchall()
        
        # عدد الحسابات
        cursor.execute("SELECT COUNT(*) as count FROM accounts WHERE user_id = %s AND is_active = TRUE", (user_id,))
        accounts_count = cursor.fetchone()['count']
        
        return {
            'balances': balance_results,
            'transactions': transactions_results,
            'accounts_count': accounts_count
        }
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على الإحصائيات: {e}")
        return None

def add_test_transaction_and_check(cursor, connection):
    """إضافة معاملة تجريبية وفحص التحديث"""
    print("🧪 اختبار إضافة معاملة وتحديث الإحصائيات...")
    
    try:
        # الحصول على الإحصائيات قبل الإضافة
        print("📊 الإحصائيات قبل الإضافة:")
        stats_before = get_current_dashboard_stats(cursor)
        if stats_before:
            print(f"   - عدد الحسابات: {stats_before['accounts_count']}")
            for balance in stats_before['balances']:
                print(f"   - {balance['code']}: {balance['total_balance']:,.2f} {balance['symbol']}")
        
        # إضافة معاملة تجريبية
        user_id = 1
        today = datetime.now().strftime('%Y-%m-%d')
        
        # البحث عن حساب متاح
        cursor.execute("SELECT id, name FROM accounts WHERE user_id = %s AND is_active = TRUE LIMIT 1", (user_id,))
        account = cursor.fetchone()
        
        if not account:
            print("❌ لا توجد حسابات متاحة")
            return False
        
        # إضافة معاملة تجريبية
        test_amount = 2500.00
        insert_sql = """
        INSERT INTO transactions (user_id, account_id, currency_id, type, amount, description, transaction_date, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """
        
        cursor.execute(insert_sql, (
            user_id, account['id'], 1, 'income', test_amount, 
            f'معاملة اختبار تحديث لوحة التحكم - {datetime.now().strftime("%H:%M:%S")}', today
        ))
        
        transaction_id = cursor.lastrowid
        print(f"✅ تم إضافة معاملة تجريبية (ID: {transaction_id}) بمبلغ {test_amount:,.2f} ر.س")
        
        # تحديث رصيد الحساب
        cursor.execute("""
            UPDATE accounts 
            SET current_balance = current_balance + %s 
            WHERE id = %s
        """, (test_amount, account['id']))
        
        # تحديث view account_balances
        cursor.execute("DROP VIEW IF EXISTS account_balances")
        create_view_sql = """
        CREATE VIEW account_balances AS
        SELECT 
            a.id as account_id,
            a.currency_id,
            COALESCE(a.initial_balance, 0) + 
            COALESCE(
                (SELECT SUM(CASE 
                    WHEN t.type = 'income' THEN t.amount 
                    WHEN t.type = 'expense' THEN -t.amount 
                    ELSE 0 
                END)
                FROM transactions t 
                WHERE t.account_id = a.id), 0
            ) + 
            COALESCE(
                (SELECT SUM(tr.to_amount)
                FROM transfers tr 
                WHERE tr.to_account_id = a.id), 0
            ) - 
            COALESCE(
                (SELECT SUM(tr.from_amount)
                FROM transfers tr 
                WHERE tr.from_account_id = a.id), 0
            ) as balance
        FROM accounts a
        WHERE a.is_active = TRUE
        """
        cursor.execute(create_view_sql)
        
        connection.commit()
        
        # الحصول على الإحصائيات بعد الإضافة
        print("\n📊 الإحصائيات بعد الإضافة:")
        stats_after = get_current_dashboard_stats(cursor)
        if stats_after:
            print(f"   - عدد الحسابات: {stats_after['accounts_count']}")
            for balance in stats_after['balances']:
                print(f"   - {balance['code']}: {balance['total_balance']:,.2f} {balance['symbol']}")
        
        # مقارنة النتائج
        if stats_before and stats_after:
            print("\n🔍 مقارنة النتائج:")
            
            # مقارنة الأرصدة
            sar_before = next((b['total_balance'] for b in stats_before['balances'] if b['code'] == 'SAR'), 0)
            sar_after = next((b['total_balance'] for b in stats_after['balances'] if b['code'] == 'SAR'), 0)
            
            difference = sar_after - sar_before
            print(f"   - الفرق في رصيد SAR: {difference:,.2f} ر.س")
            
            if abs(difference - test_amount) < 0.01:  # تسامح صغير للأرقام العشرية
                print("   ✅ التحديث تم بشكل صحيح!")
            else:
                print(f"   ❌ خطأ في التحديث! متوقع: {test_amount:,.2f}, الفعلي: {difference:,.2f}")
        
        # حذف المعاملة التجريبية
        cursor.execute("DELETE FROM transactions WHERE id = %s", (transaction_id,))
        cursor.execute("""
            UPDATE accounts 
            SET current_balance = current_balance - %s 
            WHERE id = %s
        """, (test_amount, account['id']))
        
        print(f"🗑️ تم حذف المعاملة التجريبية (ID: {transaction_id})")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحديث: {e}")
        connection.rollback()
        return False

def test_gui_integration():
    """اختبار التكامل مع الواجهة"""
    print("\n🖥️ اختبار التكامل مع الواجهة...")
    
    try:
        # محاولة استيراد الواجهة
        from gui.main_window import MoneyManagerGUI
        print("✅ تم استيراد الواجهة بنجاح")
        
        # فحص وجود الدوال المطلوبة
        gui_methods = [
            'show_dashboard',
            'get_dashboard_stats',
            'refresh_dashboard_if_active',
            'save_new_transaction',
            'save_transaction_changes',
            'delete_transaction'
        ]
        
        for method in gui_methods:
            if hasattr(MoneyManagerGUI, method):
                print(f"   ✅ دالة {method} موجودة")
            else:
                print(f"   ❌ دالة {method} مفقودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار التحديث التلقائي للوحة التحكم")
    print("="*60)
    
    # اختبار التكامل مع الواجهة
    gui_ok = test_gui_integration()
    
    # الاتصال بقاعدة البيانات
    connection, cursor = connect_to_database()
    
    if not connection:
        print("❌ لا يمكن المتابعة بدون اتصال بقاعدة البيانات")
        return
    
    try:
        # اختبار إضافة معاملة وتحديث الإحصائيات
        db_ok = add_test_transaction_and_check(cursor, connection)
        
        print("\n" + "="*60)
        print("📋 ملخص نتائج الاختبار:")
        print(f"✅ التكامل مع الواجهة: {'نجح' if gui_ok else 'فشل'}")
        print(f"✅ تحديث قاعدة البيانات: {'نجح' if db_ok else 'فشل'}")
        
        if gui_ok and db_ok:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("💡 الآن عند إضافة/تعديل/حذف معاملات في التطبيق:")
            print("   1. ستتحدث الإحصائيات فوراً في قاعدة البيانات")
            print("   2. ستتحدث لوحة التحكم تلقائياً إذا كانت مفتوحة")
            print("   3. ستظهر جميع العملات بشكل صحيح")
            print("\n🚀 قم بتشغيل التطبيق واختبر إضافة معاملة جديدة!")
        else:
            print("\n⚠️ توجد مشاكل تحتاج إلى إصلاح")
        
        print("="*60)
        
    finally:
        # إغلاق الاتصال
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    main()
