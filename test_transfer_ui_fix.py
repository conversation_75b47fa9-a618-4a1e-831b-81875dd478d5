#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح واجهة التحويل
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from database.connection import db
from utils.auth import auth_manager

def test_transfer_ui_fix():
    """اختبار إصلاح واجهة التحويل"""
    try:
        print("🧪 اختبار إصلاح واجهة التحويل")
        print("=" * 50)
        
        # تهيئة قاعدة البيانات
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار دالة Transfer.get_by_user
        print("\n🔍 اختبار دالة Transfer.get_by_user...")
        
        from database.models import Transfer
        user_id = auth_manager.current_user['id']
        
        transfers = Transfer.get_by_user(user_id)
        print(f"📊 عدد التحويلات الموجودة: {len(transfers) if transfers else 0}")
        
        if transfers:
            print("📋 أول 3 تحويلات:")
            for i, transfer in enumerate(transfers[:3]):
                print(f"   {i+1}. ID: {transfer['id']} - {transfer['amount']:,.2f} {transfer.get('currency_symbol', 'ر.س')}")
                print(f"      من: {transfer['from_account_name']} إلى: {transfer['to_account_name']}")
                print(f"      التاريخ: {transfer['transfer_date']}")
        
        # إنشاء واجهة اختبار
        print("\n🖥️ إنشاء واجهة اختبار...")
        
        root = ctk.CTk()
        root.title("اختبار إصلاح واجهة التحويل")
        root.geometry("800x600")
        
        # عنوان
        title_label = ctk.CTkLabel(
            root,
            text="اختبار إصلاح واجهة التحويل",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # معلومات الإصلاحات
        info_text = """
تم إصلاح المشاكل التالية:

✅ تحديث النافذة الرئيسية لاستخدام TransferWindow المحسن
✅ إضافة callback لتحديث قائمة التحويلات بعد إضافة تحويل جديد
✅ إصلاح دالة Transfer.get_by_user لتتوافق مع هيكل الجدول الجديد
✅ إصلاح دالة Transfer.get_by_id لتتوافق مع الهيكل الجديد
✅ إضافة تشخيص مفصل لتتبع العمليات

النتيجة المتوقعة:
• عند إضافة تحويل جديد، ستظهر رسالة نجاح
• ستتم إعادة تحميل قائمة التحويلات تلقائياً
• سيظهر التحويل الجديد في أعلى القائمة فوراً
        """
        
        info_label = ctk.CTkLabel(
            root,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="right"
        )
        info_label.pack(pady=20, padx=20)
        
        # زر اختبار
        def test_transfer_window():
            """اختبار فتح نافذة التحويل"""
            try:
                print("🔄 فتح نافذة التحويل للاختبار...")
                from gui.transfer_window import TransferWindow
                
                def on_close():
                    print("✅ تم إغلاق نافذة التحويل - سيتم تحديث القائمة")
                
                transfer_window = TransferWindow(root, on_close)
                print("✅ تم فتح نافذة التحويل بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في فتح نافذة التحويل: {e}")
                import traceback
                traceback.print_exc()
        
        test_button = ctk.CTkButton(
            root,
            text="🧪 اختبار نافذة التحويل المحسنة",
            command=test_transfer_window,
            font=ctk.CTkFont(size=14),
            width=300,
            height=40
        )
        test_button.pack(pady=20)
        
        # زر اختبار النافذة الرئيسية
        def test_main_window():
            """اختبار النافذة الرئيسية"""
            try:
                print("🔄 فتح النافذة الرئيسية للاختبار...")
                from gui.main_window import MainWindow
                
                main_window = MainWindow()
                print("✅ تم فتح النافذة الرئيسية بنجاح")
                
            except Exception as e:
                print(f"❌ خطأ في فتح النافذة الرئيسية: {e}")
                import traceback
                traceback.print_exc()
        
        main_button = ctk.CTkButton(
            root,
            text="🖥️ اختبار النافذة الرئيسية",
            command=test_main_window,
            font=ctk.CTkFont(size=14),
            width=300,
            height=40
        )
        main_button.pack(pady=10)
        
        # معلومات إضافية
        steps_text = """
خطوات الاختبار:
1. اضغط على "اختبار النافذة الرئيسية"
2. انتقل إلى قسم "التحويلات"
3. اضغط على "إضافة تحويل جديد"
4. أكمل بيانات التحويل واضغط "حفظ"
5. تحقق من ظهور التحويل الجديد في القائمة فوراً
        """
        
        steps_label = ctk.CTkLabel(
            root,
            text=steps_text,
            font=ctk.CTkFont(size=11),
            justify="right",
            text_color="gray"
        )
        steps_label.pack(pady=20, padx=20)
        
        print("✅ تم إنشاء واجهة الاختبار")
        print("\n🚀 تشغيل واجهة الاختبار...")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة التحويل: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح واجهة التحويل")
    print("=" * 60)
    
    test_transfer_ui_fix()
    
    print("\n" + "=" * 60)
    print("📋 ملخص الإصلاحات:")
    print("✅ إصلاح استدعاء TransferWindow في النافذة الرئيسية")
    print("✅ إضافة callback لتحديث قائمة التحويلات")
    print("✅ إصلاح استعلامات قاعدة البيانات في Transfer model")
    print("✅ إضافة تشخيص مفصل للعمليات")
    print("\n🎯 النتيجة المتوقعة:")
    print("   التحويلات الجديدة ستظهر فوراً في القائمة بدون إعادة فتح النافذة")

if __name__ == "__main__":
    main()
