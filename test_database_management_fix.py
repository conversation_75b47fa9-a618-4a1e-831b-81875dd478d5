#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة ميزة إدارة قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_management_fix():
    """اختبار إصلاح مشكلة إدارة قاعدة البيانات"""
    print("🧪 اختبار إصلاح مشكلة ميزة إدارة قاعدة البيانات")
    print("=" * 55)
    
    try:
        # 1. اختبار الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال بقاعدة البيانات ناجح")
        
        # 2. اختبار تسجيل الدخول كمدير
        print("\n2. اختبار تسجيل الدخول كمدير...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        print("✅ تسجيل الدخول ناجح")
        
        # التحقق من صلاحيات المدير
        if not auth_manager.is_admin():
            print("❌ المستخدم ليس مديراً")
            return False
        print("✅ المستخدم مدير - يمكنه الوصول لإدارة قاعدة البيانات")
        
        # 3. اختبار استيراد الأنماط المحدثة
        print("\n3. اختبار استيراد الأنماط المحدثة...")
        try:
            from config.colors import BUTTON_STYLES
            
            # التحقق من وجود الأنماط الجديدة
            required_styles = ['primary', 'secondary', 'success', 'danger', 'warning', 'info']
            missing_styles = []
            
            for style in required_styles:
                if style in BUTTON_STYLES:
                    print(f"✅ نمط {style} موجود")
                    # التحقق من وجود معامل height
                    if 'height' in BUTTON_STYLES[style]:
                        print(f"   - يحتوي على height: {BUTTON_STYLES[style]['height']}")
                    else:
                        print(f"   ⚠️ لا يحتوي على معامل height")
                else:
                    missing_styles.append(style)
                    print(f"❌ نمط {style} مفقود")
            
            if missing_styles:
                print(f"❌ أنماط مفقودة: {missing_styles}")
                return False
            else:
                print("✅ جميع الأنماط المطلوبة موجودة")
                
        except Exception as e:
            print(f"❌ خطأ في استيراد الأنماط: {e}")
            return False
        
        # 4. اختبار استيراد MainWindow
        print("\n4. اختبار استيراد MainWindow...")
        try:
            from gui.main_window import MainWindow
            print("✅ تم استيراد MainWindow بنجاح")
        except Exception as e:
            print(f"❌ فشل استيراد MainWindow: {e}")
            return False
        
        # 5. فحص الدوال الجديدة في الكود
        print("\n5. فحص الدوال الجديدة في الكود...")
        
        # قراءة ملف main_window.py للتحقق من إصلاح تضارب المعاملات
        with open('gui/main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن تضارب المعاملات
        problematic_patterns = [
            'height=40,\n            **BUTTON_STYLES',
            'height=35,\n            **BUTTON_STYLES',
            'height=45,\n            **BUTTON_STYLES',
            'height=50,\n            **BUTTON_STYLES'
        ]
        
        conflicts_found = []
        for pattern in problematic_patterns:
            if pattern in content:
                conflicts_found.append(pattern)
        
        if conflicts_found:
            print("❌ تم العثور على تضارب في المعاملات:")
            for conflict in conflicts_found:
                print(f"   - {conflict}")
            return False
        else:
            print("✅ لا توجد تضاربات في معاملات الأزرار")
        
        # 6. اختبار إنشاء واجهة إدارة قاعدة البيانات
        print("\n6. اختبار إنشاء واجهة إدارة قاعدة البيانات...")
        try:
            import customtkinter as ctk
            
            # إنشاء نافذة تجريبية
            test_window = ctk.CTk()
            test_window.withdraw()
            
            # محاولة إنشاء MainWindow
            main_window = MainWindow(test_window)
            print("✅ تم إنشاء MainWindow بنجاح")
            
            # محاولة استدعاء دالة show_database_management
            try:
                main_window.show_database_management()
                print("✅ تم استدعاء show_database_management بدون أخطاء")
            except Exception as e:
                print(f"❌ خطأ في استدعاء show_database_management: {e}")
                test_window.destroy()
                return False
            
            # إغلاق النافذة التجريبية
            test_window.destroy()
            print("✅ تم إغلاق النافذة التجريبية بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الواجهة: {e}")
            return False
        
        # 7. اختبار وحدة النسخ الاحتياطي
        print("\n7. اختبار وحدة النسخ الاحتياطي...")
        try:
            from utils.backup import backup_manager
            
            # اختبار تحميل الإعدادات
            backup_manager.load_backup_settings()
            print("✅ تم تحميل إعدادات النسخ الاحتياطي")
            
            # اختبار الحصول على قائمة النسخ
            backup_files = backup_manager.get_backup_files()
            print(f"✅ تم العثور على {len(backup_files)} نسخة احتياطية")
            
            # اختبار اتصال قاعدة البيانات
            success, message = backup_manager.test_database_connection()
            if success:
                print("✅ اختبار اتصال قاعدة البيانات نجح")
            else:
                print(f"⚠️ اختبار اتصال قاعدة البيانات فشل: {message}")
                
        except Exception as e:
            print(f"❌ خطأ في وحدة النسخ الاحتياطي: {e}")
            return False
        
        # 8. اختبار مكونات واجهة المستخدم
        print("\n8. اختبار مكونات واجهة المستخدم...")
        try:
            from config.fonts import create_rtl_button, create_rtl_label
            
            # اختبار إنشاء زر بدون تضارب
            test_window = ctk.CTk()
            test_window.withdraw()
            
            test_frame = ctk.CTkFrame(test_window)
            
            # اختبار إنشاء زر مع نمط primary
            test_button = create_rtl_button(
                test_frame,
                text="اختبار",
                **BUTTON_STYLES['primary']
            )
            print("✅ تم إنشاء زر بنمط primary بدون تضارب")
            
            # اختبار إنشاء زر مع نمط warning
            test_button2 = create_rtl_button(
                test_frame,
                text="اختبار تحذير",
                **BUTTON_STYLES['warning']
            )
            print("✅ تم إنشاء زر بنمط warning بدون تضارب")
            
            # اختبار إنشاء زر مع نمط info
            test_button3 = create_rtl_button(
                test_frame,
                text="اختبار معلومات",
                **BUTTON_STYLES['info']
            )
            print("✅ تم إنشاء زر بنمط info بدون تضارب")
            
            test_window.destroy()
            
        except Exception as e:
            print(f"❌ خطأ في اختبار مكونات واجهة المستخدم: {e}")
            return False
        
        auth_manager.logout()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار إصلاح مشكلة ميزة إدارة قاعدة البيانات")
    print("=" * 65)
    
    if test_database_management_fix():
        print("\n🎉 تم إصلاح مشكلة ميزة إدارة قاعدة البيانات بنجاح!")
        
        print("\n✨ الإصلاحات المطبقة:")
        print("   🔧 إزالة تضارب معاملات height في create_rtl_button")
        print("   🎨 إضافة أنماط أزرار جديدة (warning, info)")
        print("   🛠️ إصلاح جميع الأزرار في قسمي إدارة قاعدة البيانات")
        print("   🧹 تنظيف الكود وإزالة المتغيرات غير المستخدمة")
        
        print("\n🚀 للاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول كمدير: admin / 123456")
        print("3. انقر على زر '🗄️ إدارة قاعدة البيانات' في الشريط الجانبي")
        print("4. يجب أن تظهر الصفحة بالمحتوى كاملاً بدون أخطاء")
        
        print("\n📋 الأقسام المتاحة:")
        print("   📥 إدارة النسخ الاحتياطية:")
        print("      - إنشاء نسخة احتياطية فورية")
        print("      - استعادة نسخة احتياطية")
        print("      - إعدادات النسخ التلقائي")
        print("   🔗 إعدادات الاتصال بقاعدة البيانات:")
        print("      - عرض الإعدادات الحالية")
        print("      - تعديل إعدادات الاتصال")
        print("      - اختبار الاتصال")
        
        return True
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح. راجع الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
