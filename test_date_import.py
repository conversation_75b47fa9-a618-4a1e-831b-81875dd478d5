#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد التواريخ من ملفات Excel المحسنة
"""

import pandas as pd
import os
from datetime import datetime, date

def test_enhanced_date_parsing():
    """اختبار دالة تحليل التواريخ المحسنة"""
    
    print("🧪 اختبار دالة تحليل التواريخ المحسنة...")
    print("="*60)
    
    # نسخة من الدالة المحسنة للاختبار
    def parse_date_enhanced(date_value):
        """نسخة اختبار من دالة تحليل التواريخ المحسنة"""
        try:
            if pd.isna(date_value):
                return None

            # إذا كانت القيمة من نوع التاريخ والوقت، قم بتحويلها مباشرة
            if isinstance(date_value, datetime):
                return date_value.date()
            
            # إذا كانت القيمة من نوع pandas Timestamp
            if hasattr(date_value, 'date') and callable(getattr(date_value, 'date')):
                return date_value.date()
            
            # إذا كانت القيمة من نوع pandas datetime64
            if hasattr(date_value, 'to_pydatetime'):
                return date_value.to_pydatetime().date()
            
            # إذا كانت القيمة رقم (Excel serial date)
            if isinstance(date_value, (int, float)):
                try:
                    # تحويل Excel serial date إلى تاريخ
                    from datetime import timedelta
                    excel_epoch = datetime(1899, 12, 30)  # Excel epoch مع تعديل الخطأ
                    converted_date = excel_epoch + timedelta(days=date_value)
                    return converted_date.date()
                except:
                    pass

            # تحويل إلى نص ومحاولة التحليل
            date_str = str(date_value).strip()
            
            # إزالة الوقت إذا كان موجوداً
            if ' ' in date_str and ':' in date_str:
                date_str = date_str.split(' ')[0]

            # محاولة تحويل التاريخ بصيغ مختلفة
            date_formats = [
                '%Y-%m-%d %H:%M:%S',  # تنسيق التاريخ والوقت الكامل
                '%Y-%m-%d %H:%M:%S.%f',  # مع microseconds
                '%Y-%m-%d',           # ISO format
                '%d/%m/%Y',           # DD/MM/YYYY
                '%m/%d/%Y',           # MM/DD/YYYY
                '%d-%m-%Y',           # DD-MM-YYYY
                '%Y/%m/%d',           # YYYY/MM/DD
                '%d.%m.%Y',           # DD.MM.YYYY
                '%Y.%m.%d',           # YYYY.MM.DD
                '%d %m %Y',           # DD MM YYYY
                '%Y %m %d'            # YYYY MM DD
            ]

            for date_format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, date_format)
                    return parsed_date.date()
                except:
                    continue

            # محاولة أخيرة مع pandas to_datetime
            try:
                parsed_date = pd.to_datetime(date_value, errors='coerce')
                if not pd.isna(parsed_date):
                    return parsed_date.date()
            except:
                pass

            return None
        except Exception as e:
            print(f"خطأ في تحليل التاريخ {date_value}: {e}")
            return None
    
    # اختبار قيم تاريخ مختلفة
    test_dates = [
        # pandas Timestamp
        pd.Timestamp('2024-01-15'),
        
        # datetime object
        datetime(2024, 1, 16, 14, 30),
        
        # date object
        date(2024, 1, 17),
        
        # نصوص بتنسيقات مختلفة
        '2024-01-18',
        '19/01/2024',
        '01/20/2024',
        '21-01-2024',
        '2024/01/22',
        '23.01.2024',
        '24 01 2024',
        
        # Excel serial date (تقريبي)
        44927.0,  # يجب أن يكون حوالي 2024-01-01
        
        # قيم خاطئة
        'invalid_date',
        None,
        pd.NaT
    ]
    
    print("📅 اختبار قيم التاريخ المختلفة:")
    for i, test_date in enumerate(test_dates, 1):
        result = parse_date_enhanced(test_date)
        status = "✅" if result else "❌"
        print(f"   {i:2d}. {str(test_date):25} ({type(test_date).__name__:15}) → {status} {result}")

def test_excel_files():
    """اختبار قراءة ملفات Excel المحسنة"""
    
    print("\n📊 اختبار قراءة ملفات Excel المحسنة...")
    print("="*60)
    
    test_files = [
        ('sample_income_formatted.xlsx', 'ملف الواردات المنسق'),
        ('sample_expenses_formatted.xlsx', 'ملف المصروفات المنسق'),
        ('sample_mixed_dates.xlsx', 'ملف التنسيقات المختلطة'),
        ('sample_income.xlsx', 'ملف الواردات الأصلي'),
        ('sample_expenses.xlsx', 'ملف المصروفات الأصلي')
    ]
    
    for filename, description in test_files:
        print(f"\n🔍 اختبار: {description}")
        print(f"   📁 الملف: {filename}")
        
        if not os.path.exists(filename):
            print("   ❌ الملف غير موجود")
            continue
        
        try:
            # قراءة الملف
            df = pd.read_excel(filename, engine='openpyxl')
            print(f"   ✅ تم قراءة الملف - {len(df)} صف، {len(df.columns)} عمود")
            
            # فحص عمود التاريخ
            if 'التاريخ' in df.columns:
                print("   📅 تحليل عمود التاريخ:")
                
                for idx, date_val in enumerate(df['التاريخ'].head(3)):
                    date_type = type(date_val).__name__
                    print(f"      الصف {idx+1}: {str(date_val):25} ({date_type})")
                    
                    # اختبار التحليل
                    try:
                        if isinstance(date_val, datetime):
                            parsed = date_val.date()
                            print(f"         ✅ datetime → {parsed}")
                        elif hasattr(date_val, 'date'):
                            parsed = date_val.date()
                            print(f"         ✅ timestamp → {parsed}")
                        elif pd.isna(date_val):
                            print(f"         ⚠️  قيمة فارغة")
                        else:
                            # محاولة تحليل كنص
                            date_str = str(date_val)
                            try:
                                parsed = pd.to_datetime(date_str).date()
                                print(f"         ✅ نص → {parsed}")
                            except:
                                print(f"         ❌ فشل في التحليل")
                    except Exception as e:
                        print(f"         ❌ خطأ: {e}")
            else:
                print("   ❌ عمود التاريخ غير موجود")
                print(f"   📋 الأعمدة الموجودة: {list(df.columns)}")
                
        except Exception as e:
            print(f"   ❌ خطأ في قراءة الملف: {e}")

def test_import_compatibility():
    """اختبار التوافق مع وظيفة الاستيراد"""
    
    print("\n🔧 اختبار التوافق مع وظيفة الاستيراد...")
    print("="*60)
    
    # محاكاة عملية الاستيراد
    test_file = 'sample_income_formatted.xlsx'
    
    if not os.path.exists(test_file):
        print(f"❌ ملف الاختبار {test_file} غير موجود")
        return
    
    try:
        # قراءة الملف كما يفعل التطبيق
        df = pd.read_excel(test_file, engine='openpyxl')
        print(f"✅ تم قراءة الملف - {len(df)} صف")
        
        # التحقق من الأعمدة المطلوبة
        required_columns = ['التاريخ', 'الوصف', 'المبلغ', 'العملة', 'الحساب']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ أعمدة مفقودة: {missing_columns}")
        else:
            print("✅ جميع الأعمدة المطلوبة موجودة")
        
        # اختبار معالجة كل صف
        print("\n📋 اختبار معالجة الصفوف:")
        success_count = 0
        
        for idx, row in df.iterrows():
            try:
                # اختبار التاريخ
                date_val = row['التاريخ']
                if isinstance(date_val, datetime):
                    parsed_date = date_val.date()
                elif hasattr(date_val, 'date'):
                    parsed_date = date_val.date()
                else:
                    parsed_date = pd.to_datetime(date_val).date()
                
                # اختبار المبلغ
                amount = float(row['المبلغ'])
                
                # اختبار العملة
                currency = str(row['العملة']).strip()
                
                # اختبار الحساب
                account = str(row['الحساب']).strip()
                
                # اختبار الوصف
                description = str(row['الوصف']).strip()
                
                print(f"   ✅ الصف {idx+1}: {parsed_date} | {amount:,.2f} {currency} | {account}")
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ الصف {idx+1}: خطأ - {e}")
        
        print(f"\n📊 النتيجة: {success_count}/{len(df)} صف تم معالجته بنجاح")
        
        if success_count == len(df):
            print("🎉 جميع الصفوف جاهزة للاستيراد!")
        else:
            print("⚠️  بعض الصفوف تحتاج إلى مراجعة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التوافق: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار شامل لاستيراد التواريخ من Excel")
    print("="*60)
    
    # تشغيل الاختبارات
    test_enhanced_date_parsing()
    test_excel_files()
    test_import_compatibility()
    
    print("\n" + "="*60)
    print("✅ انتهى الاختبار الشامل")
    print("="*60)
    print("📝 ملاحظات:")
    print("   • الملفات المنسقة تستخدم pandas Timestamp")
    print("   • الدالة المحسنة تدعم جميع التنسيقات")
    print("   • التطبيق جاهز لاستيراد الملفات الجديدة")

if __name__ == "__main__":
    main()
