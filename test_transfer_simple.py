import sys
import os
from decimal import Decimal
from datetime import datetime

# إضافة مسار المشروع للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_transfer_simple():
    """اختبار بسيط للتحويلات"""
    
    print("🔍 اختبار بسيط للتحويلات...")
    
    try:
        from database.connection import db
        from database.models import Transfer
        from utils.auth import auth_manager
        
        # الاتصال بقاعدة البيانات
        print("📡 الاتصال بقاعدة البيانات...")
        db.connect()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # محاولة تسجيل دخول
        print("🔐 تسجيل دخول المستخدم...")
        success, message = auth_manager.login("admin", "admin123")
        
        if success:
            print("✅ تم تسجيل الدخول بنجاح")
            user_id = auth_manager.current_user['id']
            print(f"👤 معرف المستخدم: {user_id}")
            
            # جلب التحويلات
            print("🔍 جلب التحويلات...")
            transfers = Transfer.get_by_user(user_id)
            print(f"📊 عدد التحويلات: {len(transfers) if transfers else 0}")
            
            if transfers:
                print("📋 التحويلات الموجودة:")
                for transfer in transfers[:3]:  # أول 3 تحويلات
                    print(f"  - ID: {transfer['id']}")
                    print(f"    من: {transfer.get('from_account_name', 'غير معروف')}")
                    print(f"    إلى: {transfer.get('to_account_name', 'غير معروف')}")
                    print(f"    المبلغ: {transfer.get('from_amount', 0)}")
                    print(f"    التاريخ: {transfer.get('transfer_date', 'غير معروف')}")
                    print()
            else:
                print("📝 لا توجد تحويلات")
                
        else:
            print(f"❌ فشل في تسجيل الدخول: {message}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            db.close()
            print("🔌 تم إغلاق الاتصال")
        except:
            pass

if __name__ == "__main__":
    test_transfer_simple()
