#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لميزة عرض المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_display():
    """اختبار عرض المستخدمين"""
    print("🧪 اختبار مباشر لميزة عرض المستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار الاتصال
        print("1. اختبار الاتصال...")
        from database.connection import db
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ الاتصال ناجح")
        
        # 2. اختبار جلب المستخدمين مباشرة
        print("\n2. اختبار جلب المستخدمين مباشرة...")
        users = db.execute_query("SELECT * FROM users ORDER BY created_at DESC")
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        for user in users:
            status = "نشط" if user['is_active'] else "معطل"
            print(f"   - {user['username']} ({user['full_name']}) - {user['role']} - {status}")
        
        # 3. اختبار User.get_all()
        print("\n3. اختبار User.get_all()...")
        from database.models import User
        model_users = User.get_all()
        
        if model_users:
            print(f"✅ User.get_all() أرجع {len(model_users)} مستخدم")
            for user in model_users:
                print(f"   - {user['username']} ({user['full_name']})")
        else:
            print("❌ User.get_all() أرجع قائمة فارغة أو None")
            return False
        
        # 4. اختبار تسجيل الدخول
        print("\n4. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        # جرب admin أولاً
        success, message = auth_manager.login("admin", "123456")
        if success:
            print("✅ تسجيل الدخول بـ admin ناجح")
            username = "admin"
        else:
            # جرب admin2
            success, message = auth_manager.login("admin2", "123456")
            if success:
                print("✅ تسجيل الدخول بـ admin2 ناجح")
                username = "admin2"
            else:
                print(f"❌ فشل تسجيل الدخول: {message}")
                return False
        
        # 5. اختبار الصلاحيات
        print("\n5. اختبار الصلاحيات...")
        if auth_manager.can_manage_users():
            print("✅ صلاحيات إدارة المستخدمين متاحة")
        else:
            print("❌ صلاحيات إدارة المستخدمين غير متاحة")
            return False
        
        # 6. محاكاة دالة load_users_list
        print("\n6. محاكاة دالة load_users_list...")
        
        # جلب المستخدمين
        users = User.get_all()
        
        if not users:
            print("❌ لا توجد مستخدمين للعرض")
            return False
        
        print(f"✅ سيتم عرض {len(users)} مستخدم:")
        
        # محاكاة إنشاء بطاقات المستخدمين
        for user in users:
            print(f"\n   📋 بطاقة المستخدم:")
            print(f"      - اسم المستخدم: {user['username']}")
            print(f"      - الاسم الكامل: {user['full_name']}")
            print(f"      - الدور: {'مدير' if user['role'] == 'admin' else 'مستخدم'}")
            print(f"      - الحالة: {'نشط' if user['is_active'] else 'معطل'}")
            print(f"      - تاريخ الإنشاء: {user.get('created_at', 'غير محدد')}")
            print(f"      - منشئ بواسطة: {user.get('created_by_username', 'غير محدد')}")
        
        # 7. اختبار مكونات واجهة المستخدم
        print("\n7. اختبار مكونات واجهة المستخدم...")
        try:
            from config.colors import COLORS, BUTTON_STYLES
            from config.fonts import create_rtl_label, create_rtl_button
            print("✅ مكونات الألوان والخطوط متاحة")
            
            from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
            print("✅ نوافذ إدارة المستخدمين متاحة")
            
        except Exception as e:
            print(f"❌ خطأ في مكونات واجهة المستخدم: {e}")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        print(f"\n📋 ملخص النتائج:")
        print(f"   - عدد المستخدمين: {len(users)}")
        print(f"   - المستخدم المسجل: {username}")
        print(f"   - صلاحيات الإدارة: متاحة")
        print(f"   - مكونات واجهة المستخدم: تعمل")
        
        print(f"\n🚀 للاستخدام:")
        print(f"1. شغل التطبيق: python main.py")
        print(f"2. سجل الدخول باستخدام: {username} / 123456")
        print(f"3. انقر على '👥 إدارة المستخدمين'")
        print(f"4. يجب أن تظهر {len(users)} مستخدم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_user_display()
