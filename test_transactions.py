#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة المعاملات متعددة العملات
Test adding multi-currency transactions
"""

from database.models import Account, Transaction, Currency
from database.connection import db
from datetime import date

def test_transaction_creation():
    """اختبار إنشاء المعاملات"""
    try:
        print("🧪 اختبار إنشاء المعاملات...")
        
        # 1. إنشاء حساب اختبار
        print("\n1️⃣ إنشاء حساب اختبار...")
        account_id = Account.create(
            user_id=1,
            name="حساب اختبار المعاملات",
            description="حساب لاختبار المعاملات متعددة العملات",
            account_type_id=1
        )
        
        if account_id <= 0:
            print("❌ فشل في إنشاء الحساب")
            return False
        
        print(f"✅ تم إنشاء الحساب - ID: {account_id}")
        
        # 2. إضافة رصيد ابتدائي بالريال السعودي
        print("\n2️⃣ إضافة رصيد ابتدائي...")
        Account.add_currency_balance(account_id, 1, 1000.0)  # SAR
        print("✅ تم إضافة رصيد ابتدائي: 1000 ريال سعودي")
        
        # 3. اختبار إضافة وارد
        print("\n3️⃣ اختبار إضافة وارد...")
        income_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=1,  # SAR
            transaction_type='income',
            amount=500.0,
            description="راتب شهري",
            transaction_date=date.today()
        )
        
        if income_id > 0:
            print(f"✅ تم إضافة الوارد - ID: {income_id}")
            
            # فحص الرصيد بعد الوارد
            balance = Account.get_currency_balance(account_id, 1)
            print(f"💰 الرصيد بعد الوارد: {balance} ريال سعودي")
        else:
            print("❌ فشل في إضافة الوارد")
            return False
        
        # 4. اختبار إضافة مصروف
        print("\n4️⃣ اختبار إضافة مصروف...")
        expense_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=1,  # SAR
            transaction_type='expense',
            amount=200.0,
            description="مصروفات متنوعة",
            transaction_date=date.today()
        )
        
        if expense_id > 0:
            print(f"✅ تم إضافة المصروف - ID: {expense_id}")
            
            # فحص الرصيد بعد المصروف
            balance = Account.get_currency_balance(account_id, 1)
            print(f"💰 الرصيد بعد المصروف: {balance} ريال سعودي")
        else:
            print("❌ فشل في إضافة المصروف")
            return False
        
        # 5. اختبار إضافة عملة جديدة والتعامل معها
        print("\n5️⃣ اختبار إضافة عملة جديدة...")
        Account.add_currency_balance(account_id, 4, 100.0)  # USD
        print("✅ تم إضافة رصيد بالدولار: 100 دولار")
        
        # إضافة وارد بالدولار
        usd_income_id = Transaction.create(
            user_id=1,
            account_id=account_id,
            currency_id=4,  # USD
            transaction_type='income',
            amount=50.0,
            description="مكافأة بالدولار",
            transaction_date=date.today()
        )
        
        if usd_income_id > 0:
            print(f"✅ تم إضافة وارد بالدولار - ID: {usd_income_id}")
            
            # فحص الأرصدة
            sar_balance = Account.get_currency_balance(account_id, 1)
            usd_balance = Account.get_currency_balance(account_id, 4)
            print(f"💰 الرصيد SAR: {sar_balance} ريال سعودي")
            print(f"💰 الرصيد USD: {usd_balance} دولار أمريكي")
        else:
            print("❌ فشل في إضافة الوارد بالدولار")
            return False
        
        # 6. عرض جميع أرصدة الحساب
        print("\n6️⃣ عرض جميع أرصدة الحساب...")
        balances = Account.get_all_balances(account_id)
        print("📊 جميع الأرصدة:")
        for balance in balances:
            print(f"   - {balance['name']}: {balance['balance']} {balance['symbol']}")
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_currency_validation():
    """اختبار التحقق من صحة العملات"""
    try:
        print("\n🔍 اختبار التحقق من صحة العملات...")
        
        # فحص العملات المتاحة
        currencies = Currency.get_all()
        print(f"📋 العملات المتاحة ({len(currencies)}):")
        for currency in currencies:
            print(f"   - {currency['code']}: {currency['name']} ({currency['symbol']})")
        
        # التحقق من وجود العملات المطلوبة
        required_currencies = ['SAR', 'YER', 'AED', 'USD']
        available_codes = [c['code'] for c in currencies]
        
        missing_currencies = [code for code in required_currencies if code not in available_codes]
        
        if missing_currencies:
            print(f"⚠️ عملات مفقودة: {missing_currencies}")
            return False
        else:
            print("✅ جميع العملات المطلوبة متوفرة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص العملات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام المعاملات متعددة العملات")
    print("=" * 60)
    
    # اختبار العملات
    success1 = test_currency_validation()
    
    # اختبار المعاملات
    success2 = test_transaction_creation()
    
    if success1 and success2:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام المعاملات متعددة العملات يعمل بشكل صحيح")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("💡 تحقق من:")
        print("   - وجود العملات المطلوبة في قاعدة البيانات")
        print("   - صحة الاتصال بقاعدة البيانات")
        print("   - صحة نماذج البيانات")

if __name__ == "__main__":
    main()
