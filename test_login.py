#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔄 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        if db.connect():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            
            # اختبار استعلام بسيط
            result = db.execute_query("SELECT 1 as test")
            if result:
                print("✅ قاعدة البيانات تعمل بشكل صحيح")
                return True
            else:
                print("❌ مشكلة في تنفيذ الاستعلامات")
                return False
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_user_exists():
    """اختبار وجود المستخدمين"""
    print("\n🔄 اختبار وجود المستخدمين...")
    
    try:
        result = db.execute_query("SELECT COUNT(*) as count FROM users")
        if result:
            count = result[0]['count']
            print(f"📊 عدد المستخدمين في قاعدة البيانات: {count}")
            
            if count > 0:
                # عرض المستخدمين
                users = db.execute_query("SELECT id, username, full_name, role FROM users")
                print("👥 المستخدمون الموجودون:")
                for user in users:
                    print(f"   • ID: {user['id']}, اسم المستخدم: {user['username']}, الاسم: {user['full_name']}, الدور: {user['role']}")
                return True
            else:
                print("❌ لا يوجد مستخدمون في قاعدة البيانات")
                return False
        else:
            print("❌ فشل في الحصول على عدد المستخدمين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المستخدمين: {e}")
        return False

def test_login():
    """اختبار تسجيل الدخول"""
    print("\n🔄 اختبار تسجيل الدخول...")
    
    username = "admin"
    password = "123456"
    
    print(f"📝 محاولة تسجيل الدخول باستخدام:")
    print(f"   اسم المستخدم: {username}")
    print(f"   كلمة المرور: {password}")
    
    try:
        success, message = auth_manager.login(username, password)
        
        if success:
            print("✅ تم تسجيل الدخول بنجاح!")
            print(f"📋 بيانات المستخدم الحالي:")
            if auth_manager.current_user:
                for key, value in auth_manager.current_user.items():
                    print(f"   {key}: {value}")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return False

def test_password_verification():
    """اختبار التحقق من كلمة المرور"""
    print("\n🔄 اختبار التحقق من كلمة المرور...")
    
    try:
        # الحصول على كلمة المرور المشفرة من قاعدة البيانات
        result = db.execute_query("SELECT password_hash FROM users WHERE username = %s", ("admin",))
        
        if result:
            stored_hash = result[0]['password_hash']
            print(f"🔐 كلمة المرور المشفرة المحفوظة: {stored_hash[:50]}...")
            
            # اختبار التحقق
            test_password = "123456"
            is_valid = auth_manager.verify_password(test_password, stored_hash)
            
            if is_valid:
                print("✅ التحقق من كلمة المرور نجح")
                return True
            else:
                print("❌ التحقق من كلمة المرور فشل")
                return False
        else:
            print("❌ لم يتم العثور على المستخدم admin")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار كلمة المرور: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام تسجيل الدخول")
    print("=" * 60)
    
    # اختبار الاتصال بقاعدة البيانات
    if not test_database_connection():
        print("\n❌ فشل اختبار قاعدة البيانات")
        return False
    
    # اختبار وجود المستخدمين
    if not test_user_exists():
        print("\n❌ فشل اختبار المستخدمين")
        return False
    
    # اختبار التحقق من كلمة المرور
    if not test_password_verification():
        print("\n❌ فشل اختبار كلمة المرور")
        return False
    
    # اختبار تسجيل الدخول
    if not test_login():
        print("\n❌ فشل اختبار تسجيل الدخول")
        return False
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("💡 يجب أن يعمل تسجيل الدخول في البرنامج الآن")
    
    # إغلاق الاتصال
    db.close()
    return True

if __name__ == "__main__":
    main()
    input("\n🔄 اضغط Enter للخروج...")
