#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة إدارة المستخدمين
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_management_functionality():
    """اختبار وظائف إدارة المستخدمين"""
    print("🚀 اختبار وظائف إدارة المستخدمين")
    print("=" * 50)
    
    try:
        # 1. اختبار تسجيل الدخول
        print("1. اختبار تسجيل الدخول...")
        from utils.auth import auth_manager
        
        success, message = auth_manager.login("admin2", "123456")
        if success:
            print("✅ تسجيل الدخول ناجح")
            print(f"   المستخدم: {auth_manager.current_user['username']}")
            print(f"   الدور: {auth_manager.current_user['role']}")
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        # 2. اختبار صلاحيات إدارة المستخدمين
        print("\n2. اختبار صلاحيات إدارة المستخدمين...")
        if auth_manager.can_manage_users():
            print("✅ المستخدم يمكنه إدارة المستخدمين")
        else:
            print("❌ المستخدم لا يمكنه إدارة المستخدمين")
            return False
        
        # 3. اختبار جلب قائمة المستخدمين
        print("\n3. اختبار جلب قائمة المستخدمين...")
        from database.models import User
        
        users = User.get_all()
        if users:
            print(f"✅ تم جلب {len(users)} مستخدم")
            for user in users:
                print(f"   - {user['username']} ({user['full_name']}) - {user['role']}")
        else:
            print("❌ لم يتم جلب أي مستخدمين")
            return False
        
        # 4. اختبار إنشاء مستخدم جديد
        print("\n4. اختبار إنشاء مستخدم جديد...")
        success, result = auth_manager.register_user(
            username="test_gui_user",
            password="test123456",
            full_name="مستخدم اختبار الواجهة",
            role="user"
        )
        
        if success:
            print("✅ تم إنشاء المستخدم الجديد بنجاح")
        else:
            print(f"❌ فشل إنشاء المستخدم: {result}")
            return False
        
        # 5. اختبار تحديث بيانات المستخدم
        print("\n5. اختبار تحديث بيانات المستخدم...")
        test_user = User.get_by_username("test_gui_user")
        if test_user:
            success, message = auth_manager.update_user_data(
                test_user['id'],
                full_name="مستخدم اختبار الواجهة المحدث"
            )
            
            if success:
                print("✅ تم تحديث بيانات المستخدم بنجاح")
            else:
                print(f"❌ فشل تحديث المستخدم: {message}")
                return False
        
        # 6. اختبار إعادة تعيين كلمة المرور
        print("\n6. اختبار إعادة تعيين كلمة المرور...")
        success, message = auth_manager.reset_user_password(
            test_user['id'],
            "new_password123"
        )
        
        if success:
            print("✅ تم إعادة تعيين كلمة المرور بنجاح")
        else:
            print(f"❌ فشل إعادة تعيين كلمة المرور: {message}")
            return False
        
        # 7. اختبار تعطيل المستخدم
        print("\n7. اختبار تعطيل المستخدم...")
        success, message = auth_manager.delete_user_account(test_user['id'])
        
        if success:
            print("✅ تم تعطيل المستخدم بنجاح")
        else:
            print(f"❌ فشل تعطيل المستخدم: {message}")
            return False
        
        # 8. اختبار إحصائيات المستخدمين
        print("\n8. اختبار إحصائيات المستخدمين...")
        stats = User.get_user_statistics()
        if stats:
            print("✅ تم جلب الإحصائيات بنجاح:")
            print(f"   - إجمالي المستخدمين: {stats['total_users']}")
            print(f"   - المستخدمين النشطين: {stats['active_users']}")
            print(f"   - المديرين: {stats['admin_users']}")
            print(f"   - المستخدمين العاديين: {stats['regular_users']}")
        else:
            print("❌ فشل في جلب الإحصائيات")
            return False
        
        # 9. تنظيف البيانات التجريبية
        print("\n9. تنظيف البيانات التجريبية...")
        from database.connection import db
        db.execute_update("DELETE FROM users WHERE username = 'test_gui_user'", ())
        print("✅ تم تنظيف البيانات التجريبية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف إدارة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🎨 اختبار مكونات واجهة المستخدم")
    print("=" * 50)
    
    try:
        # اختبار استيراد مكونات الواجهة
        print("1. اختبار استيراد مكونات الواجهة...")
        
        from config.colors import COLORS, BUTTON_STYLES, CARD_STYLES, ARABIC_TEXT_STYLES
        print("✅ تم استيراد الألوان والأنماط")
        
        from config.fonts import create_rtl_label, create_rtl_button, create_rtl_entry
        print("✅ تم استيراد مكونات الخطوط RTL")
        
        from gui.user_management_windows import AddUserWindow, EditUserWindow, ResetPasswordWindow
        print("✅ تم استيراد نوافذ إدارة المستخدمين")
        
        # اختبار إنشاء مكونات واجهة المستخدم (بدون عرض)
        print("\n2. اختبار إنشاء مكونات واجهة المستخدم...")
        import customtkinter as ctk
        
        # إنشاء نافذة مؤقتة للاختبار
        test_window = ctk.CTk()
        test_window.withdraw()  # إخفاء النافذة
        
        # اختبار إنشاء مكونات RTL
        test_label = create_rtl_label(
            test_window,
            text="اختبار النص العربي",
            font_size='body',
            text_color=COLORS['text_primary']
        )
        print("✅ تم إنشاء تسمية RTL")
        
        test_button = create_rtl_button(
            test_window,
            text="زر اختبار",
            command=lambda: None,
            **BUTTON_STYLES['primary']
        )
        print("✅ تم إنشاء زر RTL")
        
        test_entry = create_rtl_entry(
            test_window,
            placeholder_text="نص تجريبي"
        )
        print("✅ تم إنشاء حقل إدخال RTL")
        
        # إغلاق النافذة التجريبية
        test_window.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكونات واجهة المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لميزة إدارة المستخدمين")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار الوظائف الأساسية
    if test_user_management_functionality():
        tests_passed += 1
        print("\n✅ اختبار الوظائف الأساسية نجح")
    else:
        print("\n❌ اختبار الوظائف الأساسية فشل")
    
    # اختبار مكونات واجهة المستخدم
    if test_gui_components():
        tests_passed += 1
        print("\n✅ اختبار مكونات واجهة المستخدم نجح")
    else:
        print("\n❌ اختبار مكونات واجهة المستخدم فشل")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار الشامل: {tests_passed}/{total_tests} اختبارات نجحت")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! ميزة إدارة المستخدمين جاهزة للاستخدام.")
        print("\n📋 تعليمات الاستخدام:")
        print("1. شغل التطبيق: python main.py")
        print("2. سجل الدخول باستخدام: admin2 / 123456")
        print("3. انقر على '👥 إدارة المستخدمين' في الشريط الجانبي")
        print("4. استمتع بإدارة المستخدمين!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    main()
