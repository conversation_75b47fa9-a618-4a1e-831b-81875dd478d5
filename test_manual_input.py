#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للإدخال اليدوي للتصنيفات وأنواع الحسابات
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_manual_categories():
    """اختبار التصنيفات اليدوية"""
    print("🧪 اختبار التصنيفات اليدوية")
    print("=" * 40)
    
    try:
        from database.connection import db
        
        # التحقق من الاتصال
        if not db.is_connected():
            if not db.connect():
                print("❌ فشل في الاتصال بقاعدة البيانات")
                return False
        
        print("✅ الاتصال بقاعدة البيانات نشط")
        
        # اختبار إدراج معاملة بتصنيف يدوي
        print("\n💰 اختبار إدراج وارد بتصنيف يدوي...")
        test_income = db.execute_insert("""
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_name, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (1, 1, 'income', 500.0, 1, 'راتب شهري', 'اختبار التصنيف اليدوي', '2024-01-01'))
        
        if test_income > 0:
            print("   ✅ نجح إدراج وارد بتصنيف يدوي")
            
            # التحقق من البيانات
            result = db.execute_query("SELECT * FROM transactions WHERE id = %s", (test_income,))
            if result and result[0]['category_name'] == 'راتب شهري':
                print("   ✅ التصنيف محفوظ بشكل صحيح")
            else:
                print("   ❌ مشكلة في حفظ التصنيف")
                return False
            
            # حذف المعاملة التجريبية
            db.execute_update("DELETE FROM transactions WHERE id = %s", (test_income,))
        else:
            print("   ❌ فشل إدراج وارد بتصنيف يدوي")
            return False
        
        # اختبار إدراج مصروف بتصنيف يدوي
        print("\n💸 اختبار إدراج مصروف بتصنيف يدوي...")
        test_expense = db.execute_insert("""
            INSERT INTO transactions (user_id, account_id, transaction_type, amount, currency_id, category_name, description, transaction_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (1, 1, 'expense', 100.0, 1, 'طعام وشراب', 'اختبار التصنيف اليدوي', '2024-01-01'))
        
        if test_expense > 0:
            print("   ✅ نجح إدراج مصروف بتصنيف يدوي")
            
            # التحقق من البيانات
            result = db.execute_query("SELECT * FROM transactions WHERE id = %s", (test_expense,))
            if result and result[0]['category_name'] == 'طعام وشراب':
                print("   ✅ التصنيف محفوظ بشكل صحيح")
            else:
                print("   ❌ مشكلة في حفظ التصنيف")
                return False
            
            # حذف المعاملة التجريبية
            db.execute_update("DELETE FROM transactions WHERE id = %s", (test_expense,))
        else:
            print("   ❌ فشل إدراج مصروف بتصنيف يدوي")
            return False
        
        print("   🎉 جميع اختبارات التصنيفات نجحت!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التصنيفات: {e}")
        return False

def test_manual_account_types():
    """اختبار أنواع الحسابات اليدوية"""
    print("\n🧪 اختبار أنواع الحسابات اليدوية")
    print("=" * 40)
    
    try:
        from database.connection import db
        
        # اختبار إدراج حساب بنوع يدوي
        print("\n🏦 اختبار إدراج حساب بنوع يدوي...")
        test_account = db.execute_insert("""
            INSERT INTO accounts (user_id, name, account_type_name, currency_id, initial_balance, current_balance)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (1, 'حساب تجريبي', 'محفظة رقمية', 1, 1000.0, 1000.0))
        
        if test_account > 0:
            print("   ✅ نجح إدراج حساب بنوع يدوي")
            
            # التحقق من البيانات
            result = db.execute_query("SELECT * FROM accounts WHERE id = %s", (test_account,))
            if result and result[0]['account_type_name'] == 'محفظة رقمية':
                print("   ✅ نوع الحساب محفوظ بشكل صحيح")
            else:
                print("   ❌ مشكلة في حفظ نوع الحساب")
                return False
            
            # حذف الحساب التجريبي
            db.execute_update("DELETE FROM accounts WHERE id = %s", (test_account,))
        else:
            print("   ❌ فشل إدراج حساب بنوع يدوي")
            return False
        
        print("   🎉 جميع اختبارات أنواع الحسابات نجحت!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار أنواع الحسابات: {e}")
        return False

def test_ui_components():
    """اختبار مكونات الواجهة"""
    print("\n🧪 اختبار مكونات الواجهة")
    print("=" * 40)
    
    try:
        from gui.main_window import MainWindow
        
        # التحقق من وجود الدوال المحدثة
        main_window = MainWindow()
        
        # التحقق من دوال إضافة المعاملات
        if hasattr(main_window, 'save_new_transaction'):
            print("   ✅ دالة save_new_transaction موجودة")
        else:
            print("   ❌ دالة save_new_transaction غير موجودة")
            return False
        
        # التحقق من دوال إضافة الحسابات
        if hasattr(main_window, 'save_new_account'):
            print("   ✅ دالة save_new_account موجودة")
        else:
            print("   ❌ دالة save_new_account غير موجودة")
            return False
        
        print("   🎉 جميع مكونات الواجهة متوفرة!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("\n🧪 اختبار هيكل قاعدة البيانات")
    print("=" * 40)
    
    try:
        from database.connection import db
        
        # التحقق من وجود عمود category_name في جدول transactions
        print("   🔍 التحقق من عمود category_name...")
        columns = db.execute_query("DESCRIBE transactions")
        column_names = [col['Field'] for col in columns]
        
        if 'category_name' in column_names:
            print("   ✅ عمود category_name موجود في جدول transactions")
        else:
            print("   ❌ عمود category_name غير موجود في جدول transactions")
            return False
        
        # التحقق من وجود عمود account_type_name في جدول accounts
        print("   🔍 التحقق من عمود account_type_name...")
        columns = db.execute_query("DESCRIBE accounts")
        column_names = [col['Field'] for col in columns]
        
        if 'account_type_name' in column_names:
            print("   ✅ عمود account_type_name موجود في جدول accounts")
        else:
            print("   ❌ عمود account_type_name غير موجود في جدول accounts")
            return False
        
        print("   🎉 هيكل قاعدة البيانات صحيح!")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار شامل للإدخال اليدوي")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار هيكل قاعدة البيانات
    if test_database_structure():
        tests_passed += 1
    
    # اختبار التصنيفات اليدوية
    if test_manual_categories():
        tests_passed += 1
    
    # اختبار أنواع الحسابات اليدوية
    if test_manual_account_types():
        tests_passed += 1
    
    # اختبار مكونات الواجهة
    if test_ui_components():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار نجح")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n💡 الميزات الجديدة:")
        print("   ✅ إدخال التصنيفات يدوياً في الواردات والمصروفات")
        print("   ✅ إدخال أنواع الحسابات يدوياً")
        print("   ✅ حفظ البيانات في قاعدة البيانات")
        print("   ✅ واجهات مستخدم محدثة")
        
        print("\n🚀 يمكنك الآن:")
        print("   1. تشغيل التطبيق: python main.py")
        print("   2. إضافة وارد/مصروف بتصنيف يدوي")
        print("   3. إضافة حساب بنوع يدوي")
        print("   4. التعديل والحذف كما هو معتاد")
    else:
        print("⚠️ بعض الاختبارات فشلت - تحقق من الأخطاء أعلاه")
    
    print("\n🏁 انتهى الاختبار")
