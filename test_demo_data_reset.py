#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سكريبت إعادة تعيين البيانات التجريبية
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.connection import db
from utils.auth import auth_manager

def test_demo_data_reset():
    """اختبار نتائج إعادة تعيين البيانات التجريبية"""
    print("🧪 اختبار نتائج إعادة تعيين البيانات التجريبية")
    print("=" * 55)
    
    try:
        # الاتصال بقاعدة البيانات
        print("1. اختبار الاتصال بقاعدة البيانات...")
        if not (db.is_connected() or db.connect()):
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        print("\n2. اختبار تسجيل الدخول...")
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        user_id = auth_manager.current_user['id']
        print(f"✅ تم تسجيل الدخول بنجاح (المستخدم ID: {user_id})")
        
        # فحص الحسابات
        print("\n3. فحص الحسابات المنشأة...")
        accounts = db.execute_query("SELECT * FROM accounts WHERE user_id = %s ORDER BY created_at", (user_id,))
        
        if accounts:
            print(f"✅ تم العثور على {len(accounts)} حساب:")
            total_balance_sar = 0
            total_balance_usd = 0
            
            for account in accounts:
                print(f"   - {account['name']}: {account['balance']} {account['currency']} ({account['type']})")
                if account['currency'] == 'SAR':
                    total_balance_sar += float(account['balance'])
                elif account['currency'] == 'USD':
                    total_balance_usd += float(account['balance'])
            
            print(f"   📊 إجمالي الأرصدة: {total_balance_sar:.2f} SAR, {total_balance_usd:.2f} USD")
        else:
            print("❌ لا توجد حسابات")
            return False
        
        # فحص المعاملات
        print("\n4. فحص المعاملات المنشأة...")
        transactions = db.execute_query("""
            SELECT t.*, a.name as account_name, 
                   CASE WHEN ic.name IS NOT NULL THEN ic.name ELSE ec.name END as category_name
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            LEFT JOIN income_categories ic ON t.category_id = ic.id AND t.type = 'income'
            LEFT JOIN expense_categories ec ON t.category_id = ec.id AND t.type = 'expense'
            WHERE a.user_id = %s
            ORDER BY t.transaction_date DESC
        """, (user_id,))
        
        if transactions:
            print(f"✅ تم العثور على {len(transactions)} معاملة:")
            
            income_count = len([t for t in transactions if t['type'] == 'income'])
            expense_count = len([t for t in transactions if t['type'] == 'expense'])
            
            total_income = sum(float(t['amount']) for t in transactions if t['type'] == 'income')
            total_expense = sum(float(t['amount']) for t in transactions if t['type'] == 'expense')
            
            print(f"   📈 معاملات الدخل: {income_count} (إجمالي: {total_income:.2f})")
            print(f"   📉 معاملات المصروفات: {expense_count} (إجمالي: {total_expense:.2f})")
            print(f"   💰 صافي الدخل: {total_income - total_expense:.2f}")
            
            # عرض آخر 5 معاملات
            print("\n   📋 آخر 5 معاملات:")
            for trans in transactions[:5]:
                trans_type = "دخل" if trans['type'] == 'income' else "مصروف"
                print(f"      - {trans['description']}: {trans['amount']} ({trans_type}) - {trans['account_name']}")
        else:
            print("❌ لا توجد معاملات")
            return False
        
        # فحص التحويلات
        print("\n5. فحص التحويلات المنشأة...")
        transfers = db.execute_query("""
            SELECT t.*, 
                   fa.name as from_account_name, 
                   ta.name as to_account_name
            FROM transfers t
            JOIN accounts fa ON t.from_account_id = fa.id
            JOIN accounts ta ON t.to_account_id = ta.id
            WHERE fa.user_id = %s
            ORDER BY t.transfer_date DESC
        """, (user_id,))
        
        if transfers:
            print(f"✅ تم العثور على {len(transfers)} تحويل:")
            total_transfers = sum(float(t['amount']) for t in transfers)
            print(f"   💸 إجمالي مبلغ التحويلات: {total_transfers:.2f}")
            
            for transfer in transfers:
                print(f"   - {transfer['description']}: {transfer['amount']} من {transfer['from_account_name']} إلى {transfer['to_account_name']}")
        else:
            print("⚠️ لا توجد تحويلات")
        
        # فحص الفئات المستخدمة
        print("\n6. فحص الفئات المستخدمة...")
        
        # فئات الدخل المستخدمة
        used_income_categories = db.execute_query("""
            SELECT DISTINCT ic.name, COUNT(*) as usage_count
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN income_categories ic ON t.category_id = ic.id
            WHERE a.user_id = %s AND t.type = 'income'
            GROUP BY ic.id, ic.name
        """, (user_id,))
        
        if used_income_categories:
            print(f"   📈 فئات الدخل المستخدمة ({len(used_income_categories)}):")
            for category in used_income_categories:
                print(f"      - {category['name']}: {category['usage_count']} معاملة")
        
        # فئات المصروفات المستخدمة
        used_expense_categories = db.execute_query("""
            SELECT DISTINCT ec.name, COUNT(*) as usage_count
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN expense_categories ec ON t.category_id = ec.id
            WHERE a.user_id = %s AND t.type = 'expense'
            GROUP BY ec.id, ec.name
        """, (user_id,))
        
        if used_expense_categories:
            print(f"   📉 فئات المصروفات المستخدمة ({len(used_expense_categories)}):")
            for category in used_expense_categories:
                print(f"      - {category['name']}: {category['usage_count']} معاملة")
        
        # فحص التوزيع الزمني
        print("\n7. فحص التوزيع الزمني للمعاملات...")
        monthly_stats = db.execute_query("""
            SELECT 
                DATE_FORMAT(t.transaction_date, '%Y-%m') as month,
                COUNT(*) as transaction_count,
                SUM(CASE WHEN t.type = 'income' THEN t.amount ELSE 0 END) as total_income,
                SUM(CASE WHEN t.type = 'expense' THEN t.amount ELSE 0 END) as total_expense
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            WHERE a.user_id = %s
            GROUP BY DATE_FORMAT(t.transaction_date, '%Y-%m')
            ORDER BY month DESC
        """, (user_id,))
        
        if monthly_stats:
            print("   📅 الإحصائيات الشهرية:")
            for stat in monthly_stats:
                net_income = float(stat['total_income']) - float(stat['total_expense'])
                print(f"      - {stat['month']}: {stat['transaction_count']} معاملة، صافي الدخل: {net_income:.2f}")
        
        # فحص صحة الأرصدة
        print("\n8. فحص صحة أرصدة الحسابات...")
        balance_check_passed = True
        
        for account in accounts:
            # حساب الرصيد المتوقع بناءً على المعاملات والتحويلات
            account_transactions = [t for t in transactions if t['account_id'] == account['id']]
            account_transfers_out = [t for t in transfers if t['from_account_id'] == account['id']]
            account_transfers_in = [t for t in transfers if t['to_account_id'] == account['id']]
            
            # حساب الرصيد النظري (بدءاً من الرصيد الابتدائي المفترض)
            # ملاحظة: هذا فحص تقريبي لأن الرصيد الابتدائي تم تعديله أثناء إضافة المعاملات
            
            print(f"   💳 {account['name']}: الرصيد الحالي {account['balance']} {account['currency']}")
        
        auth_manager.logout()
        
        print("\n" + "=" * 55)
        print("📊 ملخص نتائج الاختبار:")
        print(f"✅ الحسابات: {len(accounts)}")
        print(f"✅ المعاملات: {len(transactions)} (دخل: {income_count}, مصروف: {expense_count})")
        print(f"✅ التحويلات: {len(transfers)}")
        print(f"✅ فئات الدخل المستخدمة: {len(used_income_categories) if used_income_categories else 0}")
        print(f"✅ فئات المصروفات المستخدمة: {len(used_expense_categories) if used_expense_categories else 0}")
        
        print("\n🎉 جميع الاختبارات نجحت! البيانات التجريبية تم إنشاؤها بشكل صحيح.")
        
        print("\n📋 للتحقق من النتائج في التطبيق:")
        print("   1. شغل التطبيق: python main.py")
        print("   2. سجل الدخول: admin / 123456")
        print("   3. تحقق من:")
        print("      - قائمة الحسابات")
        print("      - قسم الواردات (الدخل)")
        print("      - قسم المصروفات")
        print("      - التحويلات بين الحسابات")
        print("      - الإحصائيات والتقارير")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار سكريبت إعادة تعيين البيانات التجريبية")
    print("=" * 60)
    
    success = test_demo_data_reset()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت!")
    else:
        print("\n❌ بعض الاختبارات فشلت!")
        print("💡 تأكد من تشغيل reset_demo_data.py أولاً")

if __name__ == "__main__":
    main()
