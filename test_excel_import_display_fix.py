#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح عرض العمليات المستوردة من Excel
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_excel_import_display_fix():
    """اختبار إصلاح عرض العمليات المستوردة"""
    try:
        print("🧪 اختبار إصلاح عرض العمليات المستوردة من Excel")
        print("=" * 70)
        
        # تهيئة قاعدة البيانات
        from database.connection import db
        if not db.connect():
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # تسجيل الدخول
        from utils.auth import auth_manager
        success, message = auth_manager.login("admin", "123456")
        if not success:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        user_id = auth_manager.current_user['id']
        
        # اختبار الاستعلامات المُصححة
        print("\n🔍 اختبار الاستعلامات المُصححة...")
        
        # اختبار استعلام الواردات
        print("   📈 اختبار استعلام الواردات...")
        income_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'income'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        try:
            income_results = db.execute_query(income_query, (user_id,))
            print(f"      ✅ استعلام الواردات نجح - عدد النتائج: {len(income_results) if income_results else 0}")
            
            if income_results:
                for income in income_results[:2]:
                    print(f"         ID: {income['id']} - {income['amount']:,.2f} {income['currency_symbol']}")
                    print(f"         الحساب: {income['account_name']}")
                    print(f"         التاريخ: {income['transaction_date']}")
                    print(f"         الوصف: {income.get('description', 'بدون وصف')}")
                    print()
        except Exception as e:
            print(f"      ❌ خطأ في استعلام الواردات: {e}")
        
        # اختبار استعلام المصروفات
        print("   📉 اختبار استعلام المصروفات...")
        expense_query = """
            SELECT t.*, a.name as account_name, c.symbol as currency_symbol
            FROM transactions t
            JOIN accounts a ON t.account_id = a.id
            JOIN currencies c ON t.currency_id = c.id
            WHERE t.user_id = %s AND t.transaction_type = 'expense'
            ORDER BY t.transaction_date DESC, t.created_at DESC
            LIMIT 5
        """
        
        try:
            expense_results = db.execute_query(expense_query, (user_id,))
            print(f"      ✅ استعلام المصروفات نجح - عدد النتائج: {len(expense_results) if expense_results else 0}")
            
            if expense_results:
                for expense in expense_results[:2]:
                    print(f"         ID: {expense['id']} - {expense['amount']:,.2f} {expense['currency_symbol']}")
                    print(f"         الحساب: {expense['account_name']}")
                    print(f"         التاريخ: {expense['transaction_date']}")
                    print(f"         الوصف: {expense.get('description', 'بدون وصف')}")
                    print()
        except Exception as e:
            print(f"      ❌ خطأ في استعلام المصروفات: {e}")
        
        # اختبار إنشاء معاملة تجريبية لمحاكاة الاستيراد
        print("\n🔄 اختبار إنشاء معاملة تجريبية لمحاكاة الاستيراد...")
        
        from database.models import Transaction, Account
        from decimal import Decimal
        from datetime import date
        
        # الحصول على حساب للاختبار
        accounts = Account.get_by_user(user_id)
        if not accounts:
            print("   ❌ لا توجد حسابات للاختبار")
            return False
        
        test_account = accounts[0]
        print(f"   📋 استخدام الحساب: {test_account['name']} (ID: {test_account['id']})")
        
        # إنشاء وارد تجريبي
        print("   📈 إنشاء وارد تجريبي...")
        income_id = Transaction.create(
            user_id=user_id,
            account_id=test_account['id'],
            currency_id=1,
            transaction_type='income',
            amount=Decimal("500.00"),
            description="وارد تجريبي - اختبار عرض بعد الاستيراد",
            transaction_date=date.today()
        )
        
        if income_id > 0:
            print(f"      ✅ تم إنشاء وارد تجريبي: ID {income_id}")
            
            # اختبار جلب الوارد الجديد
            print("      🔍 اختبار جلب الوارد الجديد...")
            new_income_results = db.execute_query(income_query, (user_id,))
            
            if new_income_results:
                latest_income = new_income_results[0]
                if latest_income['id'] == income_id:
                    print(f"      ✅ الوارد الجديد يظهر في أعلى القائمة!")
                    print(f"         ID: {latest_income['id']} - {latest_income['amount']:,.2f} {latest_income['currency_symbol']}")
                    print(f"         الوصف: {latest_income['description']}")
                else:
                    print(f"      ⚠️ الوارد الجديد ليس في أعلى القائمة")
            else:
                print(f"      ❌ لم يتم العثور على الوارد الجديد")
        else:
            print(f"      ❌ فشل في إنشاء وارد تجريبي")
        
        # إنشاء مصروف تجريبي
        print("   📉 إنشاء مصروف تجريبي...")
        expense_id = Transaction.create(
            user_id=user_id,
            account_id=test_account['id'],
            currency_id=1,
            transaction_type='expense',
            amount=Decimal("200.00"),
            description="مصروف تجريبي - اختبار عرض بعد الاستيراد",
            transaction_date=date.today()
        )
        
        if expense_id > 0:
            print(f"      ✅ تم إنشاء مصروف تجريبي: ID {expense_id}")
            
            # اختبار جلب المصروف الجديد
            print("      🔍 اختبار جلب المصروف الجديد...")
            new_expense_results = db.execute_query(expense_query, (user_id,))
            
            if new_expense_results:
                latest_expense = new_expense_results[0]
                if latest_expense['id'] == expense_id:
                    print(f"      ✅ المصروف الجديد يظهر في أعلى القائمة!")
                    print(f"         ID: {latest_expense['id']} - {latest_expense['amount']:,.2f} {latest_expense['currency_symbol']}")
                    print(f"         الوصف: {latest_expense['description']}")
                else:
                    print(f"      ⚠️ المصروف الجديد ليس في أعلى القائمة")
            else:
                print(f"      ❌ لم يتم العثور على المصروف الجديد")
        else:
            print(f"      ❌ فشل في إنشاء مصروف تجريبي")
        
        # تنظيف البيانات التجريبية
        print("\n🗑️ تنظيف البيانات التجريبية...")
        
        if income_id > 0:
            if Transaction.delete(income_id):
                print(f"   ✅ تم حذف الوارد التجريبي: ID {income_id}")
            else:
                print(f"   ❌ فشل في حذف الوارد التجريبي: ID {income_id}")
        
        if expense_id > 0:
            if Transaction.delete(expense_id):
                print(f"   ✅ تم حذف المصروف التجريبي: ID {expense_id}")
            else:
                print(f"   ❌ فشل في حذف المصروف التجريبي: ID {expense_id}")
        
        # عرض الإصلاحات المطبقة
        print("\n" + "=" * 70)
        print("🔧 الإصلاحات المطبقة:")
        print("1. ✅ إصلاح استعلام الواردات: t.type → t.transaction_type")
        print("2. ✅ إصلاح استعلام المصروفات: t.type → t.transaction_type")
        print("3. ✅ تحسين تحديث الواجهة بعد الاستيراد")
        print("4. ✅ مسح التخزين المؤقت قبل التحديث")
        print("5. ✅ إعادة تحميل الصفحة الحالية بالكامل")
        
        print("\n🎯 النتيجة المتوقعة:")
        print("• عند استيراد ملف Excel للواردات:")
        print("  - ستظهر رسالة نجاح")
        print("  - ستتحدث الأرصدة فوراً")
        print("  - إذا كان المستخدم في صفحة 'الواردات'، ستظهر العمليات المستوردة فوراً")
        print("  - ستظهر العمليات في لوحة التحكم")
        
        print("\n• عند استيراد ملف Excel للمصروفات:")
        print("  - ستظهر رسالة نجاح")
        print("  - ستتحدث الأرصدة فوراً")
        print("  - إذا كان المستخدم في صفحة 'المصروفات'، ستظهر العمليات المستوردة فوراً")
        print("  - ستظهر العمليات في لوحة التحكم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إصلاح العرض: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح عرض العمليات المستوردة من Excel")
    print("=" * 80)
    
    success = test_excel_import_display_fix()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 تم إصلاح مشكلة عرض العمليات المستوردة بنجاح!")
        print("✅ العمليات المستوردة ستظهر الآن فوراً في الأقسام المخصصة لها")
        
        print("\n📋 للاختبار النهائي:")
        print("1. تشغيل التطبيق: python main.py")
        print("2. تسجيل الدخول: admin/123456")
        print("3. انتقل إلى قسم 'الواردات'")
        print("4. اضغط 'استيراد من Excel'")
        print("5. اختر ملف Excel للواردات وأكمل الاستيراد")
        print("6. تحقق من ظهور العمليات فوراً في قائمة الواردات")
        print("7. كرر نفس الخطوات مع قسم 'المصروفات'")
        
    else:
        print("❌ هناك مشاكل تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
