#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لإصلاح الأرقام العربية في واجهة التقارير
"""

import os
import sys
import locale

# إعداد الأرقام الإنجليزية
os.environ['LC_ALL'] = 'C'
os.environ['LC_NUMERIC'] = 'C'
try:
    locale.setlocale(locale.LC_ALL, 'C')
except:
    pass

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from config.fonts import create_rtl_label, create_rtl_button
from config.colors import COLORS, ARABIC_TEXT_STYLES

class ArabicNumbersTestGUI:
    """واجهة اختبار الأرقام العربية"""
    
    def __init__(self):
        """تهيئة الواجهة"""
        self.window = ctk.CTk()
        self.window.title("اختبار إصلاح الأرقام العربية")
        self.window.geometry("800x600")
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        self.create_test_interface()
    
    def create_test_interface(self):
        """إنشاء واجهة الاختبار"""
        
        # العنوان الرئيسي
        title_label = create_rtl_label(
            self.window,
            text="🧪 اختبار إصلاح الأرقام العربية في التقارير",
            font_size='title',
            text_color=COLORS['text_primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        title_label.pack(pady=(20, 10))
        
        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(
            self.window,
            fg_color=COLORS['bg_light'],
            corner_radius=15
        )
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # اختبار 1: الأرقام العادية
        self.create_normal_numbers_test(scroll_frame)
        
        # اختبار 2: الأرقام العربية
        self.create_arabic_numbers_test(scroll_frame)
        
        # اختبار 3: تنسيق العملات
        self.create_currency_test(scroll_frame)
        
        # اختبار 4: تنسيق المعاملات
        self.create_transactions_test(scroll_frame)
        
        # زر إغلاق
        close_button = create_rtl_button(
            self.window,
            text="إغلاق",
            command=self.window.destroy
        )
        close_button.pack(pady=10)
    
    def create_normal_numbers_test(self, parent):
        """اختبار الأرقام العادية"""
        
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📊 اختبار 1: الأرقام العادية",
            font_size='subtitle',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 10))
        
        # إطار القسم
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'], corner_radius=10)
        section_frame.pack(fill="x", padx=20, pady=10)
        
        # أرقام اختبار
        test_numbers = [
            ("1,234.56", "رقم بسيط"),
            ("1,000,000.78", "مليون"),
            ("500,296,960.00", "نصف مليار"),
            ("110,001,568,050.00", "مئة مليار")
        ]
        
        for number, description in test_numbers:
            test_label = create_rtl_label(
                section_frame,
                text=f"{description}: {number} ر.س",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            test_label.pack(anchor="e", padx=15, pady=5)
    
    def create_arabic_numbers_test(self, parent):
        """اختبار الأرقام العربية"""
        
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="🔄 اختبار 2: تحويل الأرقام العربية",
            font_size='subtitle',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 10))
        
        # إطار القسم
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'], corner_radius=10)
        section_frame.pack(fill="x", padx=20, pady=10)
        
        # أرقام عربية للاختبار
        arabic_numbers = [
            ("١٬٢٣٤٫٥٦", "رقم عربي بسيط"),
            ("١٬٠٠٠٬٠٠٠٫٧٨", "مليون عربي"),
            ("٥٠٠٬٢٩٦٬٩٦٠٫٠٠", "نصف مليار عربي"),
            ("١١٠٬٠٠١٬٥٦٨٬٠٥٠٫٠٠", "مئة مليار عربي")
        ]
        
        for arabic_number, description in arabic_numbers:
            # عرض الرقم العربي الأصلي
            original_label = create_rtl_label(
                section_frame,
                text=f"الأصلي: {arabic_number}",
                font_size='small',
                text_color=COLORS['text_muted'],
                **ARABIC_TEXT_STYLES['label']
            )
            original_label.pack(anchor="e", padx=15, pady=2)
            
            # عرض الرقم بعد التحويل (يجب أن يتحول تلقائياً)
            converted_label = create_rtl_label(
                section_frame,
                text=f"{description}: {arabic_number} ر.س",
                font_size='body',
                text_color=COLORS['success'],
                **ARABIC_TEXT_STYLES['label']
            )
            converted_label.pack(anchor="e", padx=15, pady=5)
    
    def create_currency_test(self, parent):
        """اختبار تنسيق العملات"""
        
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="💰 اختبار 3: تنسيق العملات",
            font_size='subtitle',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 10))
        
        # إطار القسم
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'], corner_radius=10)
        section_frame.pack(fill="x", padx=20, pady=10)
        
        # بيانات العملات
        currencies = [
            ("SAR", "500,296,960.00", "ر.س"),
            ("USD", "1,000,051,000.00", "$"),
            ("YER", "110,001,568,050.00", "ر.ي"),
            ("AED", "146,749.96", "د.إ")
        ]
        
        for code, amount, symbol in currencies:
            currency_label = create_rtl_label(
                section_frame,
                text=f"{code}: {amount} {symbol}",
                font_size='body',
                text_color=COLORS['text_primary'],
                **ARABIC_TEXT_STYLES['label']
            )
            currency_label.pack(anchor="e", padx=15, pady=5)
    
    def create_transactions_test(self, parent):
        """اختبار تنسيق المعاملات"""
        
        # عنوان القسم
        section_title = create_rtl_label(
            parent,
            text="📈 اختبار 4: تنسيق المعاملات",
            font_size='subtitle',
            text_color=COLORS['primary'],
            **ARABIC_TEXT_STYLES['title']
        )
        section_title.pack(pady=(20, 10))
        
        # إطار القسم
        section_frame = ctk.CTkFrame(parent, fg_color=COLORS['bg_card'], corner_radius=10)
        section_frame.pack(fill="x", padx=20, pady=10)
        
        # بيانات المعاملات
        transactions = [
            ("YER", "10,000,042,500.00", "0.00", "ر.ي"),
            ("AED", "101,000.00", "200.00", "د.إ"),
            ("SAR", "13,500.00", "7,845.00", "ر.س")
        ]
        
        for code, income, expense, symbol in transactions:
            net = float(income.replace(',', '')) - float(expense.replace(',', ''))
            net_text = f"{net:,.2f}"
            net_prefix = "+" if net >= 0 else ""
            
            transaction_label = create_rtl_label(
                section_frame,
                text=f"{code}: واردات {income} - مصروفات {expense} = صافي {net_prefix}{net_text} {symbol}",
                font_size='body',
                text_color=COLORS['success'] if net >= 0 else COLORS['error'],
                **ARABIC_TEXT_STYLES['label']
            )
            transaction_label.pack(anchor="e", padx=15, pady=5)
        
        # ملاحظة
        note_label = create_rtl_label(
            section_frame,
            text="ملاحظة: إذا ظهرت جميع الأرقام أعلاه بالتنسيق الإنجليزي (1,234.56) فإن الإصلاح يعمل بشكل صحيح",
            font_size='small',
            text_color=COLORS['text_muted'],
            **ARABIC_TEXT_STYLES['label']
        )
        note_label.pack(anchor="e", padx=15, pady=10)
    
    def run(self):
        """تشغيل الواجهة"""
        self.window.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل اختبار واجهة الأرقام العربية...")
    
    try:
        app = ArabicNumbersTestGUI()
        app.run()
        print("✅ تم إغلاق الاختبار")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")

if __name__ == "__main__":
    main()
