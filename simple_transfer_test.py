#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لعمليات التحويل بين الحسابات
"""

import mysql.connector
from decimal import Decimal
from datetime import datetime

def test_transfer_operations():
    """اختبار عمليات التحويل"""
    try:
        print("🧪 اختبار شامل لعمليات التحويل بين الحسابات")
        print("=" * 70)
        
        # الاتصال بقاعدة البيانات
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password='mohdam',
            database='money_manager',
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor(dictionary=True)
        print("✅ تم الاتصال بقاعدة البيانات")
        
        user_id = 1  # admin user
        
        # 1. إنشاء حسابين تجريبيين
        print("\n1️⃣ إنشاء حسابين تجريبيين...")
        
        # الحساب الأول
        cursor.execute("""
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, description, is_active, balance)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (user_id, 'حساب تجريبي مصدر', 1, 1, 'حساب للاختبار', True, 10000.00))
        
        account1_id = cursor.lastrowid
        
        # إضافة رصيد للحساب الأول
        cursor.execute("""
            INSERT INTO account_balances (account_id, currency_id, balance)
            VALUES (%s, %s, %s)
        """, (account1_id, 1, 10000.00))
        
        # الحساب الثاني
        cursor.execute("""
            INSERT INTO accounts (user_id, name, account_type_id, currency_id, description, is_active, balance)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
        """, (user_id, 'حساب تجريبي هدف', 1, 1, 'حساب للاختبار', True, 5000.00))
        
        account2_id = cursor.lastrowid
        
        # إضافة رصيد للحساب الثاني
        cursor.execute("""
            INSERT INTO account_balances (account_id, currency_id, balance)
            VALUES (%s, %s, %s)
        """, (account2_id, 1, 5000.00))
        
        print(f"   ✅ تم إنشاء الحساب المصدر (ID: {account1_id}) برصيد 10,000 ر.س")
        print(f"   ✅ تم إنشاء الحساب الهدف (ID: {account2_id}) برصيد 5,000 ر.س")
        
        # 2. اختبار التحويل الأساسي
        print("\n2️⃣ اختبار التحويل الأساسي...")
        
        transfer_amount = 1000.00
        
        # الحصول على الأرصدة قبل التحويل
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account1_id, 1))
        balance1_before = cursor.fetchone()['balance']
        
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account2_id, 1))
        balance2_before = cursor.fetchone()['balance']
        
        print(f"   📊 الأرصدة قبل التحويل:")
        print(f"      الحساب المصدر: {balance1_before:,.2f} ر.س")
        print(f"      الحساب الهدف: {balance2_before:,.2f} ر.س")
        
        # إنشاء التحويل
        cursor.execute("""
            INSERT INTO transfers (user_id, from_account_id, to_account_id, 
                                 from_amount, from_currency_id, to_amount, to_currency_id, 
                                 exchange_rate, description, transfer_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, account1_id, account2_id, transfer_amount, 1, transfer_amount, 1, 1.0, 'تحويل تجريبي', datetime.now().date()))
        
        transfer_id = cursor.lastrowid
        
        # تحديث الأرصدة
        cursor.execute("UPDATE account_balances SET balance = balance - %s WHERE account_id = %s AND currency_id = %s", 
                      (transfer_amount, account1_id, 1))
        
        cursor.execute("UPDATE account_balances SET balance = balance + %s WHERE account_id = %s AND currency_id = %s", 
                      (transfer_amount, account2_id, 1))
        
        # التحقق من الأرصدة بعد التحويل
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account1_id, 1))
        balance1_after = cursor.fetchone()['balance']
        
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account2_id, 1))
        balance2_after = cursor.fetchone()['balance']
        
        print(f"   📊 الأرصدة بعد التحويل:")
        print(f"      الحساب المصدر: {balance1_after:,.2f} ر.س")
        print(f"      الحساب الهدف: {balance2_after:,.2f} ر.س")
        
        # التحقق من صحة التحويل
        expected_balance1 = balance1_before - transfer_amount
        expected_balance2 = balance2_before + transfer_amount
        
        if (abs(balance1_after - expected_balance1) < 0.01 and 
            abs(balance2_after - expected_balance2) < 0.01):
            print(f"   ✅ التحويل نجح! معرف التحويل: {transfer_id}")
        else:
            print(f"   ❌ خطأ في التحويل!")
            print(f"      المتوقع للمصدر: {expected_balance1:,.2f}, الفعلي: {balance1_after:,.2f}")
            print(f"      المتوقع للهدف: {expected_balance2:,.2f}, الفعلي: {balance2_after:,.2f}")
        
        # 3. اختبار التحويل بمبلغ كبير
        print("\n3️⃣ اختبار التحويل بمبلغ كبير...")
        
        large_amount = 5000.00
        
        # الأرصدة قبل التحويل الكبير
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account1_id, 1))
        balance1_before_large = cursor.fetchone()['balance']
        
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account2_id, 1))
        balance2_before_large = cursor.fetchone()['balance']
        
        print(f"   🔄 تحويل {large_amount:,.2f} ر.س")
        
        # إنشاء التحويل الكبير
        cursor.execute("""
            INSERT INTO transfers (user_id, from_account_id, to_account_id, 
                                 from_amount, from_currency_id, to_amount, to_currency_id, 
                                 exchange_rate, description, transfer_date)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, account1_id, account2_id, large_amount, 1, large_amount, 1, 1.0, 'تحويل كبير تجريبي', datetime.now().date()))
        
        transfer_large_id = cursor.lastrowid
        
        # تحديث الأرصدة
        cursor.execute("UPDATE account_balances SET balance = balance - %s WHERE account_id = %s AND currency_id = %s", 
                      (large_amount, account1_id, 1))
        
        cursor.execute("UPDATE account_balances SET balance = balance + %s WHERE account_id = %s AND currency_id = %s", 
                      (large_amount, account2_id, 1))
        
        # التحقق من النتائج
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account1_id, 1))
        balance1_after_large = cursor.fetchone()['balance']
        
        cursor.execute("SELECT balance FROM account_balances WHERE account_id = %s AND currency_id = %s", (account2_id, 1))
        balance2_after_large = cursor.fetchone()['balance']
        
        expected_balance1_large = balance1_before_large - large_amount
        expected_balance2_large = balance2_before_large + large_amount
        
        if (abs(balance1_after_large - expected_balance1_large) < 0.01 and 
            abs(balance2_after_large - expected_balance2_large) < 0.01):
            print(f"   ✅ التحويل الكبير نجح! معرف التحويل: {transfer_large_id}")
        else:
            print(f"   ❌ خطأ في التحويل الكبير!")
        
        # 4. التحقق من سجلات التحويل
        print("\n4️⃣ التحقق من سجلات التحويل...")
        
        cursor.execute("""
            SELECT t.*, a1.name as from_account, a2.name as to_account
            FROM transfers t
            JOIN accounts a1 ON t.from_account_id = a1.id
            JOIN accounts a2 ON t.to_account_id = a2.id
            WHERE t.user_id = %s AND t.description LIKE '%تجريبي%'
            ORDER BY t.created_at DESC
        """, (user_id,))
        
        transfers = cursor.fetchall()
        
        if transfers:
            print(f"   📊 تم العثور على {len(transfers)} تحويل:")
            for transfer in transfers:
                print(f"      ID: {transfer['id']} - {transfer['from_amount']:,.2f} ر.س")
                print(f"         من: {transfer['from_account']} إلى: {transfer['to_account']}")
                print(f"         التاريخ: {transfer['transfer_date']}")
        else:
            print("   ❌ لم يتم العثور على سجلات تحويل")
        
        # 5. اختبار نموذج Transfer من التطبيق
        print("\n5️⃣ اختبار نموذج Transfer من التطبيق...")
        
        try:
            import sys
            import os
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            
            from database.models import Transfer
            from utils.auth import auth_manager
            
            # محاكاة تسجيل الدخول
            auth_manager.current_user = {'id': user_id, 'username': 'admin'}
            
            # اختبار Transfer.create
            model_transfer_id = Transfer.create(
                user_id=user_id,
                from_account_id=account1_id,
                to_account_id=account2_id,
                from_amount=Decimal("200.00"),
                from_currency_id=1,
                to_amount=Decimal("200.00"),
                to_currency_id=1,
                description="اختبار نموذج Transfer",
                transfer_date=datetime.now().date()
            )
            
            if model_transfer_id > 0:
                print(f"   ✅ نموذج Transfer يعمل بشكل صحيح! معرف التحويل: {model_transfer_id}")
            else:
                print("   ❌ فشل في نموذج Transfer")
                
        except Exception as e:
            print(f"   ❌ خطأ في اختبار نموذج Transfer: {e}")
        
        # 6. تنظيف البيانات التجريبية
        print("\n6️⃣ تنظيف البيانات التجريبية...")
        
        # حذف التحويلات
        cursor.execute("DELETE FROM transfers WHERE user_id = %s AND description LIKE '%تجريبي%'", (user_id,))
        
        # حذف الأرصدة
        cursor.execute("DELETE FROM account_balances WHERE account_id IN (%s, %s)", (account1_id, account2_id))
        
        # حذف الحسابات
        cursor.execute("DELETE FROM accounts WHERE id IN (%s, %s)", (account1_id, account2_id))
        
        print("   ✅ تم تنظيف جميع البيانات التجريبية")
        
        # إغلاق الاتصال
        cursor.close()
        connection.close()
        
        print("\n" + "=" * 70)
        print("🎉 اكتمل الاختبار الشامل بنجاح!")
        print("✅ جميع عمليات التحويل تعمل بشكل صحيح")
        print("📋 التوصية: المشكلة قد تكون في واجهة المستخدم وليس في النظام الأساسي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_transfer_operations()
