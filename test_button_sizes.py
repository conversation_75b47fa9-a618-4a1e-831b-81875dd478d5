#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف لاختبار أحجام الأزرار الجديدة
"""

import sys
import os

# إضافة المجلد الجذر للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import customtkinter as ctk
from config.colors import COLORS, BUTTON_STYLES

def test_button_sizes():
    """اختبار أحجام الأزرار المختلفة"""
    
    # إعداد النافذة
    ctk.set_appearance_mode("light")
    
    root = ctk.CTk()
    root.title("اختبار أحجام الأزرار")
    root.geometry("800x600")
    root.configure(fg_color=COLORS['bg_light'])
    
    # عنوان النافذة
    title = ctk.CTkLabel(
        root,
        text="اختبار أحجام الأزرار الجديدة",
        font=ctk.CTkFont(size=24, weight="bold"),
        text_color=COLORS['text_primary']
    )
    title.pack(pady=20)
    
    # إطار الاختبار
    test_frame = ctk.CTkFrame(root, fg_color="transparent")
    test_frame.pack(fill="both", expand=True, padx=40, pady=20)
    
    # أزرار القائمة الجانبية
    menu_section = ctk.CTkFrame(test_frame, fg_color=COLORS['bg_card'])
    menu_section.pack(fill="x", pady=(0, 20))
    
    menu_title = ctk.CTkLabel(
        menu_section,
        text="أزرار القائمة الجانبية",
        font=ctk.CTkFont(size=18, weight="bold"),
        text_color=COLORS['text_primary']
    )
    menu_title.pack(pady=15)
    
    menu_buttons = [
        "🏠 لوحة التحكم",
        "💰 الواردات", 
        "💸 المصروفات",
        "🏦 الحسابات",
        "🔄 التحويلات",
        "📂 إدارة الأقسام",
        "📊 التقارير",
        "⚙️ الإعدادات"
    ]
    
    for text in menu_buttons[:4]:  # عرض 4 أزرار فقط للاختبار
        btn = ctk.CTkButton(
            menu_section,
            text=text,
            anchor="e",
            **BUTTON_STYLES['secondary']
        )
        btn.pack(pady=5, padx=20)
    
    # الأزرار الرئيسية
    main_section = ctk.CTkFrame(test_frame, fg_color=COLORS['bg_card'])
    main_section.pack(fill="x", pady=(0, 20))
    
    main_title = ctk.CTkLabel(
        main_section,
        text="الأزرار الرئيسية",
        font=ctk.CTkFont(size=18, weight="bold"),
        text_color=COLORS['text_primary']
    )
    main_title.pack(pady=15)
    
    main_buttons_frame = ctk.CTkFrame(main_section, fg_color="transparent")
    main_buttons_frame.pack(pady=10)
    
    # أزرار بأحجام مختلفة
    btn_primary = ctk.CTkButton(
        main_buttons_frame,
        text="+ إضافة جديد",
        **BUTTON_STYLES['primary']
    )
    btn_primary.pack(side="left", padx=10)
    
    btn_secondary = ctk.CTkButton(
        main_buttons_frame,
        text="تعديل",
        **BUTTON_STYLES['secondary']
    )
    btn_secondary.pack(side="left", padx=10)
    
    btn_success = ctk.CTkButton(
        main_buttons_frame,
        text="حفظ",
        **BUTTON_STYLES['success']
    )
    btn_success.pack(side="left", padx=10)
    
    btn_danger = ctk.CTkButton(
        main_buttons_frame,
        text="حذف",
        **BUTTON_STYLES['danger']
    )
    btn_danger.pack(side="left", padx=10)
    
    # أزرار صغيرة للبطاقات
    cards_section = ctk.CTkFrame(test_frame, fg_color=COLORS['bg_card'])
    cards_section.pack(fill="x")
    
    cards_title = ctk.CTkLabel(
        cards_section,
        text="أزرار البطاقات (أحجام مخصصة)",
        font=ctk.CTkFont(size=18, weight="bold"),
        text_color=COLORS['text_primary']
    )
    cards_title.pack(pady=15)
    
    # محاكاة بطاقة معاملة
    card_frame = ctk.CTkFrame(cards_section, fg_color=COLORS['bg_light'], corner_radius=10)
    card_frame.pack(fill="x", padx=20, pady=10)
    
    # معلومات المعاملة
    info_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
    info_frame.pack(fill="x", padx=15, pady=15)
    
    amount_label = ctk.CTkLabel(
        info_frame,
        text="+1,500.00 ر.س",
        font=ctk.CTkFont(size=16, weight="bold"),
        text_color=COLORS['success']
    )
    amount_label.pack(side="left")
    
    date_label = ctk.CTkLabel(
        info_frame,
        text="2024-01-15",
        font=ctk.CTkFont(size=12),
        text_color=COLORS['text_secondary']
    )
    date_label.pack(side="right")
    
    desc_label = ctk.CTkLabel(
        card_frame,
        text="راتب شهر يناير",
        font=ctk.CTkFont(size=14),
        text_color=COLORS['text_primary']
    )
    desc_label.pack(anchor="w", padx=15)
    
    # أزرار البطاقة
    card_buttons_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
    card_buttons_frame.pack(fill="x", padx=15, pady=(10, 15))
    
    edit_btn = ctk.CTkButton(
        card_buttons_frame,
        text="✏️ تعديل",
        width=80,
        height=30,
        font=ctk.CTkFont(size=12),
        fg_color=COLORS['primary'],
        hover_color=COLORS['primary_dark'],
        text_color=COLORS['text_light'],
        corner_radius=8
    )
    edit_btn.pack(side="left", padx=(0, 5))
    
    delete_btn = ctk.CTkButton(
        card_buttons_frame,
        text="🗑️ حذف",
        width=80,
        height=30,
        font=ctk.CTkFont(size=12),
        fg_color=COLORS['error'],
        hover_color="#DC2626",
        text_color=COLORS['text_light'],
        corner_radius=8
    )
    delete_btn.pack(side="right")
    
    # معلومات الاختبار
    info_text = ctk.CTkLabel(
        root,
        text="تم تطبيق الأحجام الجديدة للأزرار:\n"
             "• أزرار رئيسية: 200x50 بكسل\n"
             "• أزرار ثانوية: 200x45 بكسل\n" 
             "• أزرار النجاح: 150x45 بكسل\n"
             "• أزرار الخطر: 120x40 بكسل\n"
             "• أزرار البطاقات: 80x30 بكسل",
        font=ctk.CTkFont(size=12),
        text_color=COLORS['text_secondary'],
        justify="center"
    )
    info_text.pack(pady=10)
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    test_button_sizes()
